import React, { useEffect, useState } from "react";
import { StyleSheet, SafeAreaView } from "react-native";
import { Surface } from "react-native-paper";
import { Option, optionService } from "@/services/optionService";
import { useLocalSearchParams, router } from "expo-router";
import { questionService } from "@/services/questionService";
import {
  PaperListAppBar,
  PaperRefreshableFlatList,
  PaperCard,
  PaperCardTitle,
  PaperCardActions,
  PaperButton,
  PaperTitle,
  PaperBody,
  PaperAddFAB,
  PaperDeleteDialog,
  useRefresh,
} from "@/components/paper";
import { t } from "@/constants/Localization";
import { useApiError } from "@/hooks/useApiError";

export default function OptionsListScreen() {
  const [options, setOptions] = useState<Option[]>([]);
  const [loading, setLoading] = useState(true);
  const [deleteDialogVisible, setDeleteDialogVisible] = useState(false);
  const [optionToDelete, setOptionToDelete] = useState<Option | null>(null);
  const { questionId } = useLocalSearchParams();
  const { showError } = useApiError();

  const loadOptions = async () => {
    try {
      setLoading(true);
      // const data = await optionService.getOptions(questionId);
      const data = await questionService.getQuestion(questionId).options;
      console.log(questionId, await questionService.getQuestion(questionId));
      setOptions(data || []);
    } catch (err) {
      showError(err, t('errorLoadingOptions') || "خطا در دریافت گزینه‌ها");
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  const { refreshing, onRefresh } = useRefresh(loadOptions);

  useEffect(() => {
    loadOptions();
  }, []);

  // Navigation handlers
  const handleAddOption = () => {
    router.push(`/screens/OptionAdd${questionId ? `?questionId=${questionId}` : ''}`);
  };

  const handleOptionPress = (option: Option) => {
    router.push(`/screens/OptionDetails?id=${option.id}`);
  };

  const handleEditOption = (option: Option) => {
    router.push(`/screens/OptionEdit?id=${option.id}`);
  };

  const handleDeleteOption = (option: Option) => {
    setOptionToDelete(option);
    setDeleteDialogVisible(true);
  };

  const confirmDeleteOption = async () => {
    if (!optionToDelete) return;

    try {
      await optionService.deleteOption(optionToDelete.id);
      setDeleteDialogVisible(false);
      setOptionToDelete(null);
      loadOptions(); // Refresh the list
    } catch (error) {
      showError(error, t('errorDeletingOption') || 'خطا در حذف گزینه');
      console.error('Failed to delete option:', error);
    }
  };

  const renderOptionItem = ({ item }: { item: Option }) => (
    <PaperCard
      style={styles.optionCard}
      onPress={() => handleOptionPress(item)}
    >
      <PaperCardTitle
        title={`${t('option')} #${item.id}`}
        subtitle={item.option_text}
        left={(props) => (
          <Surface {...props} style={styles.optionIcon}>
            <PaperTitle size="small">{item.is_correct ? '✓' : 'O'}</PaperTitle>
          </Surface>
        )}
      />
      <PaperCardActions>
        <PaperButton
          title={t('edit')}
          mode="outlined"
          onPress={() => handleEditOption(item)}
          icon="pencil"
          compact
        />
        <PaperButton
          title={t('delete')}
          mode="outlined"
          onPress={() => handleDeleteOption(item)}
          icon="delete"
          compact
          style={styles.deleteButton}
        />
      </PaperCardActions>
    </PaperCard>
  );

  return (
    <SafeAreaView style={styles.container}>
      {/* App Bar */}
      <PaperListAppBar
        title={t('questionOptions') || 'گزینه‌های سوال'}
        onAddPress={handleAddOption}
        showBackButton
      />

      {/* Content */}
      <Surface style={styles.content}>
        <PaperRefreshableFlatList
          data={options}
          renderItem={renderOptionItem}
          keyExtractor={(item) => item.id}
          refreshing={refreshing}
          onRefresh={onRefresh}
          loading={loading}
          style={styles.list}
          contentContainerStyle={styles.listContent}
          emptyTitle={t('noOptionsFound') || 'هیچ گزینه‌ای ثبت نشده است'}
          emptyDescription={t('noOptionsDescription') || 'برای افزودن گزینه جدید از دکمه + استفاده کنید'}
          emptyIcon="format-list-bulleted"
          emptyAction={
            <PaperButton
              title={t('addOption') || 'افزودن گزینه'}
              mode="contained"
              onPress={handleAddOption}
              icon="plus"
            />
          }
        />
      </Surface>

      {/* Floating Action Button */}
      <PaperAddFAB
        onPress={handleAddOption}
        visible={!loading}
      />

      {/* Delete Confirmation Dialog */}
      <PaperDeleteDialog
        visible={deleteDialogVisible}
        onDismiss={() => setDeleteDialogVisible(false)}
        onConfirm={confirmDeleteOption}
        title={t('deleteOption') || 'حذف گزینه'}
        content={
          optionToDelete
            ? `${t('confirmDeleteOption') || 'آیا از حذف گزینه'} "${optionToDelete.option_text}" ${t('areYouSure') || 'مطمئن هستید؟'}`
            : ''
        }
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
  },
  list: {
    flex: 1,
  },
  listContent: {
    padding: 16,
  },
  optionCard: {
    marginVertical: 8,
  },
  optionIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#e8f5e8',
  },
  deleteButton: {
    borderColor: '#f44336',
  },
});
