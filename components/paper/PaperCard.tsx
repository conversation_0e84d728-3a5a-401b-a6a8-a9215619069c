import React from 'react';
import { Card as PaperCard, useTheme } from 'react-native-paper';
import { StyleSheet, type ViewStyle } from 'react-native';

export interface PaperCardProps {
  children: React.ReactNode;
  style?: ViewStyle;
  mode?: 'elevated' | 'outlined' | 'contained';
  onPress?: () => void;
  onLongPress?: () => void;
  disabled?: boolean;
}

export function PaperCardComponent({
  children,
  style,
  mode = 'elevated',
  onPress,
  onLongPress,
  disabled = false,
}: PaperCardProps) {
  return (
    <PaperCard
      mode={mode}
      style={[styles.card, style]}
      onPress={onPress}
      onLongPress={onLongPress}
      disabled={disabled}
    >
      <PaperCard.Content style={styles.cardContent}>
        {children}
      </PaperCard.Content>
    </PaperCard>
  );
}

// Card Title component
export function PaperCardTitle({ 
  title, 
  subtitle, 
  left, 
  right,
  style 
}: {
  title: string;
  subtitle?: string;
  left?: (props: any) => React.ReactNode;
  right?: (props: any) => React.ReactNode;
  style?: ViewStyle;
}) {
  return (
    <PaperCard.Title
      title={title}
      subtitle={subtitle}
      left={left}
      right={right}
      style={style}
      titleStyle={styles.cardTitle}
      subtitleStyle={styles.cardSubtitle}
    />
  );
}

// Card Actions component
export function PaperCardActions({ 
  children, 
  style 
}: { 
  children: React.ReactNode; 
  style?: ViewStyle; 
}) {
  return (
    <PaperCard.Actions style={style}>
      {children}
    </PaperCard.Actions>
  );
}

// Card Cover component
export function PaperCardCover({ 
  source, 
  style 
}: { 
  source: { uri: string } | number; 
  style?: ViewStyle; 
}) {
  return (
    <PaperCard.Cover 
      source={source} 
      style={style} 
    />
  );
}

const styles = StyleSheet.create({
  card: {
    marginVertical: 8,
    marginHorizontal: 16,
  },
  cardContent: {
    padding: 16,
  },
  cardTitle: {
    fontFamily: 'Vazirmatn',
    fontSize: 18,
    fontWeight: '600',
    textAlign: 'right',
  },
  cardSubtitle: {
    fontFamily: 'Vazirmatn',
    fontSize: 14,
    textAlign: 'right',
    marginTop: 4,
  },
});

// Export as default for easier importing
export default PaperCardComponent;
