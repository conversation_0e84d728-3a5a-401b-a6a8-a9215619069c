import React, { useState } from 'react';
import { StyleSheet, SafeAreaView } from 'react-native';
import { Surface } from 'react-native-paper';
import { majorService } from '@/services/majorService';
import KeyboardAvoidingWrapper from '@/components/KeyboardAvoidingWrapper';
import { router } from 'expo-router';
import {
  PaperFormAppBar,
  PaperTextInput,
  PaperButton,
  PaperFormField,
  PaperFormSection,
  PaperAlertDialog,
} from '@/components/paper';
import { t } from '@/constants/Localization';
import { useApiError } from '@/hooks/useApiError';

export default function MajorAddScreen() {
  const [name, setName] = useState('');
  const [description, setDescription] = useState('');
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState<{[key: string]: string}>({});
  const [showAlert, setShowAlert] = useState(false);
  const [alertTitle, setAlertTitle] = useState('');
  const [alertMessage, setAlertMessage] = useState('');
  const { handleApiError } = useApiError();

  const validateForm = () => {
    const newErrors: {[key: string]: string} = {};

    if (!name.trim()) {
      newErrors.name = t('majorNameRequired') || 'نام رشته الزامی است';
    }
    if (!description.trim()) {
      newErrors.description = t('majorDescriptionRequired') || 'توضیحات رشته الزامی است';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleAddMajor = async () => {
    if (!validateForm()) {
      return;
    }

    setLoading(true);
    try {
      await majorService.createMajor({ name, description });
      setAlertTitle(t('success') || 'موفقیت');
      setAlertMessage(t('majorAddedSuccessfully') || 'رشته با موفقیت اضافه شد!');
      setShowAlert(true);
      // Navigate back after showing success message
      setTimeout(() => {
        setShowAlert(false);
        router.back();
      }, 2000);
    } catch (error) {
      handleApiError(error, t('errorAddingMajor') || 'خطا در افزودن رشته');
      console.error('Failed to add major:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      {/* App Bar */}
      <PaperFormAppBar
        title={t('addMajor') || 'افزودن رشته جدید'}
        onSavePress={handleAddMajor}
        saveDisabled={loading}
        loading={loading}
      />

      {/* Content */}
      <Surface style={styles.content}>
        <KeyboardAvoidingWrapper>
          <PaperFormSection>
            <PaperFormField
              label={t('majorName') || 'نام رشته'}
              error={errors.name}
              required
            >
              <PaperTextInput
                value={name}
                onChangeText={setName}
                placeholder={t('enterMajorName') || 'نام رشته را وارد کنید'}
                disabled={loading}
                error={!!errors.name}
              />
            </PaperFormField>

            <PaperFormField
              label={t('majorDescription') || 'توضیحات رشته'}
              error={errors.description}
              required
            >
              <PaperTextInput
                value={description}
                onChangeText={setDescription}
                placeholder={t('enterMajorDescription') || 'توضیحات رشته را وارد کنید'}
                disabled={loading}
                multiline
                numberOfLines={3}
                error={!!errors.description}
              />
            </PaperFormField>

            <PaperButton
              title={loading ? t('adding') || 'در حال افزودن...' : t('addMajor') || 'افزودن رشته'}
              onPress={handleAddMajor}
              disabled={loading}
              loading={loading}
              mode="contained"
              style={styles.submitButton}
              icon="plus"
            />
          </PaperFormSection>
        </KeyboardAvoidingWrapper>
      </Surface>

      {/* Alert Dialog */}
      <PaperAlertDialog
        visible={showAlert}
        onDismiss={() => setShowAlert(false)}
        title={alertTitle}
        message={alertMessage}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  submitButton: {
    marginTop: 24,
  },
});
