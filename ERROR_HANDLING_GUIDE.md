# Error Handling System Guide

This guide explains the comprehensive error handling system implemented in the Azmoon Client application, which provides automatic error categorization, user-friendly toast notifications, and intelligent retry mechanisms.

## Overview

The error handling system consists of several components working together:

1. **Error Handler Utility** - Categorizes and processes errors
2. **Toast Notification Service** - Displays user-friendly messages
3. **Retry Mechanism** - Automatically retries failed operations
4. **Error Boundary** - Catches React component errors
5. **API Service Integration** - Enhanced API calls with error handling
6. **Custom Hooks** - Easy-to-use hooks for components

## Features

### ✅ Automatic Error Categorization
- Network errors (connection issues, timeouts)
- Server errors (5xx status codes)
- Validation errors (4xx status codes)
- Unknown errors (unexpected failures)

### ✅ User-Friendly Toast Notifications
- Success, Error, Warning, and Info toast types
- Persian/Farsi localized messages
- Retry buttons for recoverable errors
- Automatic dismissal with configurable duration

### ✅ Intelligent Retry Mechanism
- Exponential backoff with jitter
- Configurable retry attempts and delays
- Retry condition evaluation
- Cancellation support

### ✅ React Error Boundary
- Catches unhandled React errors
- Graceful fallback UI
- Retry functionality for component errors

## Quick Start

### 1. Basic Usage with Hooks

```tsx
import { useApiError } from '@/hooks/useApiError';

function MyComponent() {
  const { handleApiError, showSuccess } = useApiError();

  const loadData = async () => {
    try {
      const data = await apiService.getData();
      showSuccess('Data loaded successfully');
    } catch (error) {
      // Automatically shows appropriate toast with retry option if applicable
      handleApiError(error, 'Load Data', loadData);
    }
  };

  return (
    <Button onPress={loadData} title="Load Data" />
  );
}
```

### 2. API Service with Retry

```tsx
import { api } from '@/services/api';

// Automatic retry with default configuration
const data = await api.getWithRetry('/api/users');

// Custom retry configuration
const data = await api.get('/api/users', {}, {
  maxAttempts: 5,
  baseDelay: 2000,
});
```

### 3. Manual Retry Operations

```tsx
import { withRetry, RetryPresets } from '@/utils/retryMechanism';

const result = await withRetry(
  () => fetchUserData(userId),
  RetryPresets.AGGRESSIVE
);

if (result.success) {
  console.log('Data:', result.data);
} else {
  console.error('Failed after', result.attempts, 'attempts');
}
```

## Components

### Error Handler Utility (`utils/errorHandler.ts`)

Categorizes errors and provides user-friendly messages:

```tsx
import { handleError, ErrorType, ErrorSeverity } from '@/utils/errorHandler';

const errorInfo = handleError(error);
console.log('Type:', errorInfo.type); // NETWORK, SERVER, VALIDATION, etc.
console.log('Can Retry:', errorInfo.canRetry);
console.log('User Message:', errorInfo.userMessage);
```

### Toast Service (`services/toastService.ts`)

Provides toast notifications with React Native Paper Snackbar:

```tsx
import { useToast } from '@/services/toastService';

const toast = useToast();

// Different toast types
toast.showSuccess('Operation completed');
toast.showError('Something went wrong');
toast.showWarning('Please check your input');
toast.showInfo('New update available');

// Error with retry button
toast.showRetryError('Connection failed', () => retryOperation());
```

### Retry Mechanism (`utils/retryMechanism.ts`)

Handles automatic retries with exponential backoff:

```tsx
import { withRetry, RetryPresets, RetryManager } from '@/utils/retryMechanism';

// Simple retry
const result = await withRetry(operation, RetryPresets.STANDARD);

// Advanced retry with custom configuration
const result = await withRetry(operation, {
  maxAttempts: 3,
  baseDelay: 1000,
  maxDelay: 10000,
  backoffFactor: 2,
  retryCondition: (error, attempt) => error.status >= 500,
  onRetry: (error, attempt) => console.log(`Retry ${attempt}`),
});

// Retry manager for cancellation
const manager = new RetryManager();
const result = await manager.executeWithRetry('operation-key', operation);
manager.cancelRetry('operation-key'); // Cancel if needed
```

### Error Boundary (`components/ErrorBoundary.tsx`)

Catches React component errors:

```tsx
import { ErrorBoundary, withErrorBoundary } from '@/components/ErrorBoundary';

// Wrap components
<ErrorBoundary>
  <MyComponent />
</ErrorBoundary>

// Or use HOC
const SafeComponent = withErrorBoundary(MyComponent);
```

## Configuration

### Retry Presets

Pre-configured retry settings for common scenarios:

- **QUICK**: 2 attempts, 500ms base delay (user interactions)
- **STANDARD**: 3 attempts, 1s base delay (API calls)
- **AGGRESSIVE**: 5 attempts, 1s base delay (critical operations)
- **NETWORK**: 4 attempts, 2s base delay (network-specific)

### Localization

All error messages are localized in `constants/Localization.ts`:

```tsx
// Persian messages
connectionError: 'خطا در اتصال به اینترنت',
retry: 'تلاش مجدد',
checkConnection: 'لطفاً اتصال اینترنت خود را بررسی کنید',
```

## Integration

The error handling system is automatically integrated into:

1. **App Layout** - ToastProvider and ErrorBoundary wrap the entire app
2. **API Service** - All API calls include error handling and retry
3. **Service Layer** - Individual services can use the error handling hooks
4. **Components** - Easy integration with custom hooks

## Testing

Comprehensive tests are available:

```bash
# Run error handler tests
npm test __tests__/utils/errorHandler.test.ts

# Run retry mechanism tests
npm test __tests__/utils/retryMechanism.test.ts

# Run all error handling tests
npm test -- --testPathPattern="error|retry"
```

## Demo

A demo screen is available at `app/screens/ErrorHandlingDemo.tsx` that showcases:

- Different error types and their handling
- Toast notification variants
- Retry functionality
- Success scenarios

## Best Practices

1. **Always use the error handling hooks** in components
2. **Provide context** when calling `handleApiError` for better debugging
3. **Use appropriate retry presets** based on operation criticality
4. **Test error scenarios** during development
5. **Monitor error logs** in production for patterns

## Troubleshooting

### Common Issues

1. **Toast not showing**: Ensure ToastProvider wraps your app
2. **Retry not working**: Check retry conditions and network connectivity
3. **Error boundary not catching**: Only catches render errors, not async errors
4. **Localization missing**: Add missing keys to Localization.ts

### Debug Mode

In development mode (`__DEV__ = true`), detailed error logs are shown in console:

```
🚨 Error in Load Data
Type: NETWORK
Severity: MEDIUM
Message: Network request failed
User Message: خطا در اتصال به اینترنت
Can Retry: true
```

## Migration

To migrate existing error handling:

1. Replace manual error handling with `useApiError` hook
2. Remove custom toast/alert implementations
3. Update API calls to use enhanced API service methods
4. Add Error Boundary to component trees
5. Update tests to use new error handling patterns

This error handling system provides a robust, user-friendly way to handle all types of errors in your React Native application with automatic retry capabilities and localized messaging.
