import React, { useEffect, useState } from "react";
import {
  FlatList,
  ActivityIndicator,
  StyleSheet,
  Pressable,
  RefreshControl,
} from "react-native";
import { ThemedText } from "@/components/ThemedText";
import { ThemedView } from "@/components/ThemedView";
import { Option, optionService } from "@/services/optionService";
import { Link, useLocalSearchParams } from "expo-router";
import { questionService } from "@/services/questionService";

export default function OptionsListScreen() {
  const [options, setOptions] = useState<Option[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { questionId } = useLocalSearchParams();

  const loadOptions = async () => {
    try {
      setLoading(true);
      setError(null);
      // const data = await optionService.getOptions(questionId);
      const data = await questionService.getQuestion(questionId).options;
      console.log(questionId, await questionService.getQuestion(questionId));
      setOptions(data || []);
    } catch (err) {
      setError("خطا در دریافت گزینه‌ها.");
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = async () => {
    try {
      setRefreshing(true);
      setError(null);
      const data = await optionService.getOptions();
      setOptions(data);
    } catch (err) {
      setError("Failed to refresh options.");
      console.error(err);
    } finally {
      setRefreshing(false);
    }
  };

  useEffect(() => {
    loadOptions();
  }, []);

  if (loading) {
    return (
      <ThemedView style={styles.centered}>
        <ActivityIndicator size="large" />
        <ThemedText>بارگذاری...</ThemedText>
      </ThemedView>
    );
  }

  if (error) {
    return (
      <ThemedView style={styles.centered}>
        <ThemedText style={styles.errorText}>{error}</ThemedText>
      </ThemedView>
    );
  }

  return (
    <ThemedView style={styles.container}>
      <ThemedText style={styles.title}>گزینه‌های سوال</ThemedText>
      <Link href={`/screens/OptionAdd${questionId ? `?questionId=${questionId}` : ''}`} asChild>
        <Pressable style={styles.addButton}>
          <ThemedText style={styles.addButtonText}>افزودن گزینه جدید</ThemedText>
        </Pressable>
      </Link>
      {options.length === 0 ? (
        <ThemedText style={styles.noDataText}>هیچ گزینه‌ای ثبت نشده است</ThemedText>
      ) : (
        <FlatList
          testID="options-flatlist"
          data={options}
          keyExtractor={(item: Option) => item.id}
          renderItem={({ item }: any) => (
            <ThemedView style={styles.optionItem}>
              <Link href={`/screens/OptionDetails?id=${item.id}`} asChild>
                <Pressable>
                  <ThemedText style={styles.optionName}>
                    #: {item.id}
                  </ThemedText>
                  <ThemedText style={styles.optionName}>
                    #: {item.option_text}
                  </ThemedText>
                  {/* Display other option properties here */}
                </Pressable>
              </Link>
              <ThemedView style={styles.actions}>
                <Link href={`/screens/OptionEdit?id=${item.id}`} asChild>
                  <Pressable style={styles.actionButton}>
                    <ThemedText style={styles.actionButtonText}>
                      ویرایش
                    </ThemedText>
                  </Pressable>
                </Link>
                {/* Delete functionality would be implemented here */}
              </ThemedView>
            </ThemedView>
          )}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={handleRefresh}
              colors={["#007bff"]}
              tintColor="#007bff"
            />
          }
        />
      )}
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  centered: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  title: {
    fontSize: 24,
    fontWeight: "bold",
    marginBottom: 16,
  },
  addButton: {
    backgroundColor: "#007bff",
    padding: 10,
    borderRadius: 5,
    alignItems: "center",
    marginBottom: 16,
  },
  addButtonText: {
    color: "#fff",
    fontWeight: "bold",
  },
  optionItem: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    padding: 12,
    marginVertical: 8,
    borderWidth: 1,
    borderColor: "#ccc",
    borderRadius: 8,
  },
  optionName: {
    fontSize: 16,
  },
  actions: {
    flexDirection: "row",
  },
  actionButton: {
    backgroundColor: "#28a745",
    padding: 8,
    borderRadius: 5,
    marginLeft: 8,
  },
  actionButtonText: {
    color: "#fff",
  },
  errorText: {
    color: "red",
    fontSize: 16,
  },
  noDataText: {
    fontSize: 18,
    textAlign: "center",
    marginTop: 20,
  },
});
