# RTL Drawer Implementation Guide

This guide explains how the drawer navigation has been implemented with full RTL (Right-to-Left) support for Persian/Farsi localization in the Azmoon Client app.

## Overview

The drawer is configured to:
- Open from the **right side** of the screen
- Display content in **RTL layout**
- Use **Persian fonts** (Vazirmatn)
- Support **RTL gestures** and interactions

## Implementation Details

### 1. Global RTL Configuration

RTL is enabled globally in `app/_layout.tsx`:

```typescript
// Enable RTL layout
I18nManager.allowRTL(true);
I18nManager.forceRTL(true);
```

### 2. Drawer Layout Configuration

The drawer is configured in `app/(drawer)/_layout.tsx`:

```typescript
<Drawer
  drawerContent={CustomDrawerContent}
  screenOptions={{
    headerShown: false,
    drawerPosition: I18nManager.isRTL ? "right" : "left", // Opens from right
    drawerType: Platform.select({
      ios: "slide",
      android: "front",
      default: "front",
    }),
    drawerStyle: {
      backgroundColor: Colors[colorScheme ?? "light"].background,
      width: 280,
      direction: I18nManager.isRTL ? 'rtl' : 'ltr', // RTL layout direction
    },
    drawerLabelStyle: {
      fontFamily: "Vazirmatn", // Persian font
      fontSize: 16,
      textAlign: I18nManager.isRTL ? "right" : "left",
      writingDirection: I18nManager.isRTL ? 'rtl' : 'ltr',
    },
    // Enable swipe gestures from the correct edge
    swipeEnabled: true,
    swipeEdgeWidth: 50,
  }}
>
```

### 3. Custom Drawer Content

The drawer content (`components/CustomDrawerContent.tsx`) uses:

- **RTL utilities** from `utils/rtl.ts` for consistent styling
- **React Native Paper** components for Material Design 3 compliance
- **Persian localization** strings from `constants/Localization.ts`

Key RTL features:
```typescript
const styles = StyleSheet.create({
  container: {
    flex: 1,
    direction: 'rtl', // Ensure RTL layout direction
  },
  profileSection: {
    flexDirection: rtlStyle.flexDirection.row, // RTL-aware flex direction
    alignItems: "center",
  },
  avatar: {
    ...rtlStyle.marginEnd(16), // RTL-aware margin
  },
  userName: {
    fontFamily: "Vazirmatn",
    textAlign: rtlStyle.textAlign.start,
    writingDirection: 'rtl',
  },
  // ... more RTL-aware styles
});
```

### 4. Menu Button Integration

The drawer is opened via menu buttons in the AppBar:

```typescript
// In app/(drawer)/(tabs)/_layout.tsx
const openDrawer = () => {
  navigation.openDrawer();
};

// AppBar action
actions={[
  {
    icon: 'menu',
    onPress: openDrawer,
    accessibilityLabel: 'باز کردن منو',
  }
]}
```

### 5. Paper Theme RTL Support

The React Native Paper theme includes RTL support:

```typescript
// In constants/PaperTheme.ts
export const lightTheme = {
  ...MD3LightTheme,
  colors: { ...customColors },
  fonts: configureFonts({ config: fontConfig }),
  isRTL: true, // Enable RTL support in Paper components
};
```

## Features

### ✅ Implemented Features

1. **Right-side drawer positioning**
2. **RTL content layout**
3. **Persian font support** (Vazirmatn)
4. **RTL-aware gestures** and swipe interactions
5. **Material Design 3** compliance with Paper components
6. **Proper text direction** for Persian content
7. **RTL-aware margins and padding**
8. **Accessibility support** with Persian labels

### 🎯 Key Components

- **Drawer Layout**: `app/(drawer)/_layout.tsx`
- **Custom Content**: `components/CustomDrawerContent.tsx`
- **RTL Utilities**: `utils/rtl.ts`
- **Menu Button**: `components/ui/MenuButton.tsx`
- **Paper AppBar**: `components/paper/PaperAppBar.tsx`

### 🔧 Configuration Files

- **App Config**: `app.config.js` (includes `supportsRTL: true`)
- **Paper Theme**: `constants/PaperTheme.ts`
- **Localization**: `constants/Localization.ts`

## Usage

### Opening the Drawer

The drawer can be opened by:
1. **Tapping the menu button** in the AppBar
2. **Swiping from the right edge** of the screen
3. **Programmatically** using `navigation.openDrawer()`

### Navigation Items

The drawer includes navigation to:
- خانه (Home)
- رشته‌ها (Majors)
- دروس (Courses)
- سؤالات (Questions)
- کاربران (Users)
- آزمون‌ها (Exams)
- جلسات (Sessions)
- پروفایل (Profile)
- تنظیمات (Settings)
- درباره (About)
- خروج از حساب (Logout)

## Testing

To test the RTL drawer:

1. **Visual Test**: Verify the drawer opens from the right side
2. **Gesture Test**: Swipe from the right edge to open
3. **Content Test**: Verify Persian text displays correctly
4. **Navigation Test**: Tap menu items to navigate
5. **Theme Test**: Switch between light/dark themes

## Troubleshooting

### Common Issues

1. **Drawer opens from left**: Check `I18nManager.forceRTL(true)` is called
2. **Text alignment issues**: Verify `writingDirection: 'rtl'` in styles
3. **Gesture not working**: Ensure `swipeEnabled: true` and correct `swipeEdgeWidth`
4. **Font not loading**: Check Vazirmatn font is properly loaded in `_layout.tsx`

### Debug Tips

```typescript
// Check RTL status
console.log('Is RTL:', I18nManager.isRTL);

// Check drawer position
console.log('Drawer position:', I18nManager.isRTL ? "right" : "left");
```

## Best Practices

1. **Always use RTL utilities** from `utils/rtl.ts` for consistent styling
2. **Test on both platforms** (iOS and Android) as behavior may differ
3. **Use semantic properties** like `marginStart`/`marginEnd` when possible
4. **Include accessibility labels** in Persian for screen readers
5. **Test with different screen sizes** to ensure responsive behavior

## Related Files

- `app/_layout.tsx` - Global RTL configuration
- `app/(drawer)/_layout.tsx` - Drawer layout setup
- `components/CustomDrawerContent.tsx` - Drawer content implementation
- `utils/rtl.ts` - RTL utility functions
- `constants/PaperTheme.ts` - Theme with RTL support
- `constants/Localization.ts` - Persian localization strings
