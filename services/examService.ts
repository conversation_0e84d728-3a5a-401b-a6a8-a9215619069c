import { api } from './api';

// Define types
export interface Exam {
  id: string;
  name: string;
  duration_minutes: number;
  instructions: string;
  majorId: number;
  questionIds: string[];
  createdAt?: string;
  updatedAt?: string;
}

export interface CreateExamDto {
  name: string;
  duration_minutes: number;
  instructions: string;
  majorId: number;
  questionIds: string[];
}

export interface UpdateExamDto {
  name?: string;
  duration_minutes?: number;
  instructions?: string;
  majorId?: number;
  questionIds?: string[];
}

// Exam service for handling exam-related API calls
export const examService = {
  // Get all exams
  async getExams(): Promise<Exam[]> {
    const response = await api.get<Exam[]>('/v1/api/exams');
    return response.data;
  },

  // Get a specific exam
  async getExam(id: string): Promise<Exam> {
    const response = await api.get<Exam>(`/v1/api/exams/${id}`);
    return response.data;
  },

  // Create a new exam
  async createExam(examData: CreateExamDto): Promise<Exam> {
    const response = await api.post<Exam>('/v1/api/exams', examData);
    return response.data;
  },

  // Update a exam
  async updateExam(id: string, examData: UpdateExamDto): Promise<Exam> {
    const response = await api.patch<Exam>(`/v1/api/exams/${id}`, examData);
    return response.data;
  },

  // Delete a exam
  async deleteExam(id: string): Promise<void> {
    await api.delete(`/v1/api/exams/${id}`);
  },

  // Get questions for an exam
  async getExamQuestions(id: string): Promise<any[]> { // Assuming 'any[]' for now, will define Question interface later
    const response = await api.get<any[]>(`/v1/api/exams/${id}/questions`);
    return response.data;
  }
};
