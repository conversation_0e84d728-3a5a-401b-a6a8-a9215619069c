import { MD3LightTheme, MD3DarkTheme, configureFonts } from 'react-native-paper';

// Persian/Farsi font configuration
const fontConfig = {
  web: {
    regular: {
      fontFamily: 'Vazirmatn, sans-serif',
      fontWeight: 'normal' as const,
    },
    medium: {
      fontFamily: 'Vazirmatn, sans-serif',
      fontWeight: '500' as const,
    },
    light: {
      fontFamily: 'Vazirmatn, sans-serif',
      fontWeight: '300' as const,
    },
    thin: {
      fontFamily: 'Vazirmatn, sans-serif',
      fontWeight: '100' as const,
    },
  },
  ios: {
    regular: {
      fontFamily: 'Vazirmatn',
      fontWeight: 'normal' as const,
    },
    medium: {
      fontFamily: 'Vazirmatn',
      fontWeight: '500' as const,
    },
    light: {
      fontFamily: 'Vazirmatn',
      fontWeight: '300' as const,
    },
    thin: {
      fontFamily: 'Vazirmatn',
      fontWeight: '100' as const,
    },
  },
  android: {
    regular: {
      fontFamily: 'Vazirmatn',
      fontWeight: 'normal' as const,
    },
    medium: {
      fontFamily: 'Vazirmatn',
      fontWeight: '500' as const,
    },
    light: {
      fontFamily: 'Vazirmatn',
      fontWeight: '300' as const,
    },
    thin: {
      fontFamily: 'Vazirmatn',
      fontWeight: '100' as const,
    },
  },
};

// Custom color scheme for the app
const customColors = {
  primary: '#2196F3', // Blue
  primaryContainer: '#E3F2FD',
  secondary: '#FF9800', // Orange
  secondaryContainer: '#FFF3E0',
  tertiary: '#4CAF50', // Green
  tertiaryContainer: '#E8F5E8',
  surface: '#FFFFFF',
  surfaceVariant: '#F5F5F5',
  background: '#FAFAFA',
  error: '#F44336',
  errorContainer: '#FFEBEE',
  onPrimary: '#FFFFFF',
  onPrimaryContainer: '#0D47A1',
  onSecondary: '#FFFFFF',
  onSecondaryContainer: '#E65100',
  onTertiary: '#FFFFFF',
  onTertiaryContainer: '#1B5E20',
  onSurface: '#212121',
  onSurfaceVariant: '#424242',
  onBackground: '#212121',
  onError: '#FFFFFF',
  onErrorContainer: '#B71C1C',
  outline: '#BDBDBD',
  outlineVariant: '#E0E0E0',
  inverseSurface: '#303030',
  inverseOnSurface: '#F5F5F5',
  inversePrimary: '#90CAF9',
  shadow: '#000000',
  scrim: '#000000',
  surfaceDisabled: 'rgba(33, 33, 33, 0.12)',
  onSurfaceDisabled: 'rgba(33, 33, 33, 0.38)',
  backdrop: 'rgba(0, 0, 0, 0.4)',
};

const customDarkColors = {
  primary: '#90CAF9', // Light Blue
  primaryContainer: '#1565C0',
  secondary: '#FFB74D', // Light Orange
  secondaryContainer: '#F57C00',
  tertiary: '#81C784', // Light Green
  tertiaryContainer: '#388E3C',
  surface: '#121212',
  surfaceVariant: '#1E1E1E',
  background: '#000000',
  error: '#CF6679',
  errorContainer: '#B00020',
  onPrimary: '#0D47A1',
  onPrimaryContainer: '#E3F2FD',
  onSecondary: '#E65100',
  onSecondaryContainer: '#FFF3E0',
  onTertiary: '#1B5E20',
  onTertiaryContainer: '#E8F5E8',
  onSurface: '#FFFFFF',
  onSurfaceVariant: '#E0E0E0',
  onBackground: '#FFFFFF',
  onError: '#000000',
  onErrorContainer: '#FFEBEE',
  outline: '#757575',
  outlineVariant: '#424242',
  inverseSurface: '#F5F5F5',
  inverseOnSurface: '#303030',
  inversePrimary: '#2196F3',
  shadow: '#000000',
  scrim: '#000000',
  surfaceDisabled: 'rgba(255, 255, 255, 0.12)',
  onSurfaceDisabled: 'rgba(255, 255, 255, 0.38)',
  backdrop: 'rgba(0, 0, 0, 0.4)',
};

export const lightTheme = {
  ...MD3LightTheme,
  colors: {
    ...MD3LightTheme.colors,
    ...customColors,
  },
  fonts: configureFonts({ config: fontConfig }),
  // RTL support
  isRTL: true,
};

export const darkTheme = {
  ...MD3DarkTheme,
  colors: {
    ...MD3DarkTheme.colors,
    ...customDarkColors,
  },
  fonts: configureFonts({ config: fontConfig }),
  // RTL support
  isRTL: true,
};

// Export theme type for TypeScript
export type AppTheme = typeof lightTheme;
