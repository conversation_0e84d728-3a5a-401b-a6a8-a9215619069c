{"/home/<USER>/projects/azmoon-client/app/+not-found.tsx": {"path": "/home/<USER>/projects/azmoon-client/app/+not-found.tsx", "statementMap": {"0": {"start": {"line": 9, "column": 16}, "end": {"line": 9, "column": 26}}, "1": {"start": {"line": 10, "column": 2}, "end": {"line": 21, "column": 4}}, "2": {"start": {"line": 24, "column": 15}, "end": {"line": 35, "column": 2}}}, "fnMap": {"0": {"name": "NotFoundScreen", "decl": {"start": {"line": 8, "column": 24}, "end": {"line": 8, "column": 38}}, "loc": {"start": {"line": 8, "column": 51}, "end": {"line": 22, "column": 1}}, "line": 8}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0}, "b": {}}, "/home/<USER>/projects/azmoon-client/app/_layout.tsx": {"path": "/home/<USER>/projects/azmoon-client/app/_layout.tsx", "statementMap": {"0": {"start": {"line": 11, "column": 22}, "end": {"line": 11, "column": 38}}, "1": {"start": {"line": 12, "column": 19}, "end": {"line": 15, "column": 4}}, "2": {"start": {"line": 18, "column": 2}, "end": {"line": 18, "column": 29}}, "3": {"start": {"line": 19, "column": 2}, "end": {"line": 19, "column": 29}}, "4": {"start": {"line": 21, "column": 2}, "end": {"line": 24, "column": 3}}, "5": {"start": {"line": 23, "column": 4}, "end": {"line": 23, "column": 16}}, "6": {"start": {"line": 26, "column": 2}, "end": {"line": 63, "column": 4}}}, "fnMap": {"0": {"name": "RootLayout", "decl": {"start": {"line": 10, "column": 24}, "end": {"line": 10, "column": 34}}, "loc": {"start": {"line": 10, "column": 37}, "end": {"line": 64, "column": 1}}, "line": 10}}, "branchMap": {"0": {"loc": {"start": {"line": 21, "column": 2}, "end": {"line": 24, "column": 3}}, "type": "if", "locations": [{"start": {"line": 21, "column": 2}, "end": {"line": 24, "column": 3}}, {"start": {}, "end": {}}], "line": 21}, "1": {"loc": {"start": {"line": 27, "column": 26}, "end": {"line": 27, "column": 75}}, "type": "cond-expr", "locations": [{"start": {"line": 27, "column": 51}, "end": {"line": 27, "column": 60}}, {"start": {"line": 27, "column": 63}, "end": {"line": 27, "column": 75}}], "line": 27}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0}, "f": {"0": 0}, "b": {"0": [0, 0], "1": [0, 0]}}, "/home/<USER>/projects/azmoon-client/app/(drawer)/_layout.tsx": {"path": "/home/<USER>/projects/azmoon-client/app/(drawer)/_layout.tsx", "statementMap": {"0": {"start": {"line": 11, "column": 22}, "end": {"line": 11, "column": 38}}, "1": {"start": {"line": 13, "column": 2}, "end": {"line": 40, "column": 4}}}, "fnMap": {"0": {"name": "DrawerLayout", "decl": {"start": {"line": 10, "column": 24}, "end": {"line": 10, "column": 36}}, "loc": {"start": {"line": 10, "column": 39}, "end": {"line": 41, "column": 1}}, "line": 10}}, "branchMap": {"0": {"loc": {"start": {"line": 19, "column": 26}, "end": {"line": 19, "column": 62}}, "type": "cond-expr", "locations": [{"start": {"line": 19, "column": 46}, "end": {"line": 19, "column": 53}}, {"start": {"line": 19, "column": 56}, "end": {"line": 19, "column": 62}}], "line": 19}, "1": {"loc": {"start": {"line": 26, "column": 36}, "end": {"line": 26, "column": 58}}, "type": "binary-expr", "locations": [{"start": {"line": 26, "column": 36}, "end": {"line": 26, "column": 47}}, {"start": {"line": 26, "column": 51}, "end": {"line": 26, "column": 58}}], "line": 26}, "2": {"loc": {"start": {"line": 29, "column": 40}, "end": {"line": 29, "column": 62}}, "type": "binary-expr", "locations": [{"start": {"line": 29, "column": 40}, "end": {"line": 29, "column": 51}}, {"start": {"line": 29, "column": 55}, "end": {"line": 29, "column": 62}}], "line": 29}, "3": {"loc": {"start": {"line": 31, "column": 19}, "end": {"line": 31, "column": 41}}, "type": "binary-expr", "locations": [{"start": {"line": 31, "column": 19}, "end": {"line": 31, "column": 30}}, {"start": {"line": 31, "column": 34}, "end": {"line": 31, "column": 41}}], "line": 31}, "4": {"loc": {"start": {"line": 35, "column": 23}, "end": {"line": 35, "column": 59}}, "type": "cond-expr", "locations": [{"start": {"line": 35, "column": 43}, "end": {"line": 35, "column": 50}}, {"start": {"line": 35, "column": 53}, "end": {"line": 35, "column": 59}}], "line": 35}}, "s": {"0": 0, "1": 0}, "f": {"0": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0], "3": [0, 0], "4": [0, 0]}}, "/home/<USER>/projects/azmoon-client/app/(drawer)/index.tsx": {"path": "/home/<USER>/projects/azmoon-client/app/(drawer)/index.tsx", "statementMap": {"0": {"start": {"line": 4, "column": 2}, "end": {"line": 4, "column": 46}}}, "fnMap": {"0": {"name": "DrawerIndex", "decl": {"start": {"line": 3, "column": 24}, "end": {"line": 3, "column": 35}}, "loc": {"start": {"line": 3, "column": 38}, "end": {"line": 5, "column": 1}}, "line": 3}}, "branchMap": {}, "s": {"0": 0}, "f": {"0": 0}, "b": {}}, "/home/<USER>/projects/azmoon-client/app/(drawer)/(tabs)/Explore.tsx": {"path": "/home/<USER>/projects/azmoon-client/app/(drawer)/(tabs)/Explore.tsx", "statementMap": {"0": {"start": {"line": 4, "column": 2}, "end": {"line": 4, "column": 27}}}, "fnMap": {"0": {"name": "Explore", "decl": {"start": {"line": 3, "column": 24}, "end": {"line": 3, "column": 31}}, "loc": {"start": {"line": 3, "column": 34}, "end": {"line": 5, "column": 1}}, "line": 3}}, "branchMap": {}, "s": {"0": 0}, "f": {"0": 0}, "b": {}}, "/home/<USER>/projects/azmoon-client/app/(drawer)/(tabs)/Home.tsx": {"path": "/home/<USER>/projects/azmoon-client/app/(drawer)/(tabs)/Home.tsx", "statementMap": {"0": {"start": {"line": 5, "column": 2}, "end": {"line": 5, "column": 24}}}, "fnMap": {"0": {"name": "Home", "decl": {"start": {"line": 3, "column": 24}, "end": {"line": 3, "column": 28}}, "loc": {"start": {"line": 3, "column": 31}, "end": {"line": 6, "column": 1}}, "line": 3}}, "branchMap": {}, "s": {"0": 0}, "f": {"0": 0}, "b": {}}, "/home/<USER>/projects/azmoon-client/app/(drawer)/(tabs)/_layout.tsx": {"path": "/home/<USER>/projects/azmoon-client/app/(drawer)/(tabs)/_layout.tsx", "statementMap": {"0": {"start": {"line": 13, "column": 22}, "end": {"line": 13, "column": 38}}, "1": {"start": {"line": 15, "column": 2}, "end": {"line": 52, "column": 4}}, "2": {"start": {"line": 36, "column": 12}, "end": {"line": 36, "column": 68}}, "3": {"start": {"line": 39, "column": 28}, "end": {"line": 39, "column": 42}}, "4": {"start": {"line": 47, "column": 12}, "end": {"line": 47, "column": 73}}}, "fnMap": {"0": {"name": "TabLayout", "decl": {"start": {"line": 12, "column": 24}, "end": {"line": 12, "column": 33}}, "loc": {"start": {"line": 12, "column": 36}, "end": {"line": 53, "column": 1}}, "line": 12}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 35, "column": 22}, "end": {"line": 35, "column": 23}}, "loc": {"start": {"line": 36, "column": 12}, "end": {"line": 36, "column": 68}}, "line": 36}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 39, "column": 22}, "end": {"line": 39, "column": 23}}, "loc": {"start": {"line": 39, "column": 28}, "end": {"line": 39, "column": 42}}, "line": 39}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 46, "column": 22}, "end": {"line": 46, "column": 23}}, "loc": {"start": {"line": 47, "column": 12}, "end": {"line": 47, "column": 73}}, "line": 47}}, "branchMap": {"0": {"loc": {"start": {"line": 18, "column": 38}, "end": {"line": 18, "column": 60}}, "type": "binary-expr", "locations": [{"start": {"line": 18, "column": 38}, "end": {"line": 18, "column": 49}}, {"start": {"line": 18, "column": 53}, "end": {"line": 18, "column": 60}}], "line": 18}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0}, "b": {"0": [0, 0]}}, "/home/<USER>/projects/azmoon-client/app/screens/About.tsx": {"path": "/home/<USER>/projects/azmoon-client/app/screens/About.tsx", "statementMap": {"0": {"start": {"line": 20, "column": 22}, "end": {"line": 24, "column": 1}}, "1": {"start": {"line": 21, "column": 2}, "end": {"line": 23, "column": 8}}, "2": {"start": {"line": 26, "column": 17}, "end": {"line": 30, "column": 1}}, "3": {"start": {"line": 27, "column": 2}, "end": {"line": 29, "column": 8}}, "4": {"start": {"line": 32, "column": 18}, "end": {"line": 36, "column": 1}}, "5": {"start": {"line": 33, "column": 2}, "end": {"line": 35, "column": 8}}, "6": {"start": {"line": 38, "column": 25}, "end": {"line": 44, "column": 1}}, "7": {"start": {"line": 39, "column": 2}, "end": {"line": 43, "column": 8}}, "8": {"start": {"line": 46, "column": 17}, "end": {"line": 51, "column": 1}}, "9": {"start": {"line": 47, "column": 2}, "end": {"line": 50, "column": 8}}, "10": {"start": {"line": 54, "column": 22}, "end": {"line": 54, "column": 49}}, "11": {"start": {"line": 56, "column": 21}, "end": {"line": 58, "column": 3}}, "12": {"start": {"line": 57, "column": 4}, "end": {"line": 57, "column": 18}}, "13": {"start": {"line": 60, "column": 21}, "end": {"line": 62, "column": 3}}, "14": {"start": {"line": 61, "column": 4}, "end": {"line": 61, "column": 36}}, "15": {"start": {"line": 64, "column": 19}, "end": {"line": 66, "column": 3}}, "16": {"start": {"line": 65, "column": 4}, "end": {"line": 65, "column": 25}}, "17": {"start": {"line": 68, "column": 20}, "end": {"line": 70, "column": 3}}, "18": {"start": {"line": 69, "column": 4}, "end": {"line": 69, "column": 49}}, "19": {"start": {"line": 72, "column": 19}, "end": {"line": 96, "column": 3}}, "20": {"start": {"line": 81, "column": 4}, "end": {"line": 95, "column": 23}}, "21": {"start": {"line": 98, "column": 2}, "end": {"line": 214, "column": 4}}, "22": {"start": {"line": 188, "column": 27}, "end": {"line": 188, "column": 57}}, "23": {"start": {"line": 217, "column": 15}, "end": {"line": 359, "column": 2}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 20, "column": 22}, "end": {"line": 20, "column": 23}}, "loc": {"start": {"line": 21, "column": 2}, "end": {"line": 23, "column": 8}}, "line": 21}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 26, "column": 17}, "end": {"line": 26, "column": 18}}, "loc": {"start": {"line": 27, "column": 2}, "end": {"line": 29, "column": 8}}, "line": 27}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 32, "column": 18}, "end": {"line": 32, "column": 19}}, "loc": {"start": {"line": 33, "column": 2}, "end": {"line": 35, "column": 8}}, "line": 33}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 38, "column": 25}, "end": {"line": 38, "column": 26}}, "loc": {"start": {"line": 39, "column": 2}, "end": {"line": 43, "column": 8}}, "line": 39}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 46, "column": 17}, "end": {"line": 46, "column": 18}}, "loc": {"start": {"line": 47, "column": 2}, "end": {"line": 50, "column": 8}}, "line": 47}, "5": {"name": "AboutScreen", "decl": {"start": {"line": 53, "column": 24}, "end": {"line": 53, "column": 35}}, "loc": {"start": {"line": 53, "column": 38}, "end": {"line": 215, "column": 1}}, "line": 53}, "6": {"name": "(anonymous_6)", "decl": {"start": {"line": 56, "column": 21}, "end": {"line": 56, "column": 22}}, "loc": {"start": {"line": 56, "column": 27}, "end": {"line": 58, "column": 3}}, "line": 56}, "7": {"name": "(anonymous_7)", "decl": {"start": {"line": 60, "column": 21}, "end": {"line": 60, "column": 22}}, "loc": {"start": {"line": 60, "column": 27}, "end": {"line": 62, "column": 3}}, "line": 60}, "8": {"name": "(anonymous_8)", "decl": {"start": {"line": 64, "column": 19}, "end": {"line": 64, "column": 20}}, "loc": {"start": {"line": 64, "column": 36}, "end": {"line": 66, "column": 3}}, "line": 64}, "9": {"name": "(anonymous_9)", "decl": {"start": {"line": 68, "column": 20}, "end": {"line": 68, "column": 21}}, "loc": {"start": {"line": 68, "column": 26}, "end": {"line": 70, "column": 3}}, "line": 68}, "10": {"name": "(anonymous_10)", "decl": {"start": {"line": 72, "column": 19}, "end": {"line": 72, "column": 20}}, "loc": {"start": {"line": 81, "column": 4}, "end": {"line": 95, "column": 23}}, "line": 81}, "11": {"name": "(anonymous_11)", "decl": {"start": {"line": 188, "column": 21}, "end": {"line": 188, "column": 22}}, "loc": {"start": {"line": 188, "column": 27}, "end": {"line": 188, "column": 57}}, "line": 188}}, "branchMap": {"0": {"loc": {"start": {"line": 20, "column": 25}, "end": {"line": 20, "column": 42}}, "type": "default-arg", "locations": [{"start": {"line": 20, "column": 33}, "end": {"line": 20, "column": 42}}], "line": 20}, "1": {"loc": {"start": {"line": 20, "column": 44}, "end": {"line": 20, "column": 53}}, "type": "default-arg", "locations": [{"start": {"line": 20, "column": 51}, "end": {"line": 20, "column": 53}}], "line": 20}, "2": {"loc": {"start": {"line": 26, "column": 20}, "end": {"line": 26, "column": 37}}, "type": "default-arg", "locations": [{"start": {"line": 26, "column": 28}, "end": {"line": 26, "column": 37}}], "line": 26}, "3": {"loc": {"start": {"line": 26, "column": 39}, "end": {"line": 26, "column": 48}}, "type": "default-arg", "locations": [{"start": {"line": 26, "column": 46}, "end": {"line": 26, "column": 48}}], "line": 26}, "4": {"loc": {"start": {"line": 32, "column": 21}, "end": {"line": 32, "column": 38}}, "type": "default-arg", "locations": [{"start": {"line": 32, "column": 29}, "end": {"line": 32, "column": 38}}], "line": 32}, "5": {"loc": {"start": {"line": 32, "column": 40}, "end": {"line": 32, "column": 49}}, "type": "default-arg", "locations": [{"start": {"line": 32, "column": 47}, "end": {"line": 32, "column": 49}}], "line": 32}, "6": {"loc": {"start": {"line": 38, "column": 28}, "end": {"line": 38, "column": 45}}, "type": "default-arg", "locations": [{"start": {"line": 38, "column": 36}, "end": {"line": 38, "column": 45}}], "line": 38}, "7": {"loc": {"start": {"line": 38, "column": 47}, "end": {"line": 38, "column": 56}}, "type": "default-arg", "locations": [{"start": {"line": 38, "column": 54}, "end": {"line": 38, "column": 56}}], "line": 38}, "8": {"loc": {"start": {"line": 46, "column": 20}, "end": {"line": 46, "column": 37}}, "type": "default-arg", "locations": [{"start": {"line": 46, "column": 28}, "end": {"line": 46, "column": 37}}], "line": 46}, "9": {"loc": {"start": {"line": 46, "column": 39}, "end": {"line": 46, "column": 48}}, "type": "default-arg", "locations": [{"start": {"line": 46, "column": 46}, "end": {"line": 46, "column": 48}}], "line": 46}, "10": {"loc": {"start": {"line": 54, "column": 22}, "end": {"line": 54, "column": 49}}, "type": "binary-expr", "locations": [{"start": {"line": 54, "column": 22}, "end": {"line": 54, "column": 38}}, {"start": {"line": 54, "column": 42}, "end": {"line": 54, "column": 49}}], "line": 54}, "11": {"loc": {"start": {"line": 93, "column": 9}, "end": {"line": 93, "column": 83}}, "type": "binary-expr", "locations": [{"start": {"line": 93, "column": 9}, "end": {"line": 93, "column": 16}}, {"start": {"line": 93, "column": 20}, "end": {"line": 93, "column": 83}}], "line": 93}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0}, "b": {"0": [0], "1": [0], "2": [0], "3": [0], "4": [0], "5": [0], "6": [0], "7": [0], "8": [0], "9": [0], "10": [0, 0], "11": [0, 0]}}, "/home/<USER>/projects/azmoon-client/app/screens/ChapterAdd.tsx": {"path": "/home/<USER>/projects/azmoon-client/app/screens/ChapterAdd.tsx", "statementMap": {"0": {"start": {"line": 9, "column": 26}, "end": {"line": 9, "column": 38}}, "1": {"start": {"line": 10, "column": 32}, "end": {"line": 10, "column": 47}}, "2": {"start": {"line": 12, "column": 27}, "end": {"line": 29, "column": 3}}, "3": {"start": {"line": 13, "column": 4}, "end": {"line": 16, "column": 5}}, "4": {"start": {"line": 14, "column": 6}, "end": {"line": 14, "column": 71}}, "5": {"start": {"line": 15, "column": 6}, "end": {"line": 15, "column": 13}}, "6": {"start": {"line": 18, "column": 4}, "end": {"line": 18, "column": 21}}, "7": {"start": {"line": 19, "column": 4}, "end": {"line": 28, "column": 5}}, "8": {"start": {"line": 20, "column": 6}, "end": {"line": 20, "column": 51}}, "9": {"start": {"line": 21, "column": 6}, "end": {"line": 21, "column": 60}}, "10": {"start": {"line": 22, "column": 6}, "end": {"line": 22, "column": 20}}, "11": {"start": {"line": 24, "column": 6}, "end": {"line": 24, "column": 71}}, "12": {"start": {"line": 25, "column": 6}, "end": {"line": 25, "column": 53}}, "13": {"start": {"line": 27, "column": 6}, "end": {"line": 27, "column": 24}}, "14": {"start": {"line": 31, "column": 2}, "end": {"line": 51, "column": 4}}, "15": {"start": {"line": 54, "column": 15}, "end": {"line": 89, "column": 2}}}, "fnMap": {"0": {"name": "ChapterAddScreen", "decl": {"start": {"line": 8, "column": 24}, "end": {"line": 8, "column": 40}}, "loc": {"start": {"line": 8, "column": 43}, "end": {"line": 52, "column": 1}}, "line": 8}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 12, "column": 27}, "end": {"line": 12, "column": 28}}, "loc": {"start": {"line": 12, "column": 39}, "end": {"line": 29, "column": 3}}, "line": 12}}, "branchMap": {"0": {"loc": {"start": {"line": 13, "column": 4}, "end": {"line": 16, "column": 5}}, "type": "if", "locations": [{"start": {"line": 13, "column": 4}, "end": {"line": 16, "column": 5}}, {"start": {}, "end": {}}], "line": 13}, "1": {"loc": {"start": {"line": 42, "column": 31}, "end": {"line": 42, "column": 63}}, "type": "binary-expr", "locations": [{"start": {"line": 42, "column": 31}, "end": {"line": 42, "column": 38}}, {"start": {"line": 42, "column": 42}, "end": {"line": 42, "column": 63}}], "line": 42}, "2": {"loc": {"start": {"line": 47, "column": 11}, "end": {"line": 47, "column": 48}}, "type": "cond-expr", "locations": [{"start": {"line": 47, "column": 21}, "end": {"line": 47, "column": 32}}, {"start": {"line": 47, "column": 35}, "end": {"line": 47, "column": 48}}], "line": 47}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0}, "f": {"0": 0, "1": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0]}}, "/home/<USER>/projects/azmoon-client/app/screens/ChapterDetails.tsx": {"path": "/home/<USER>/projects/azmoon-client/app/screens/ChapterDetails.tsx", "statementMap": {"0": {"start": {"line": 9, "column": 17}, "end": {"line": 9, "column": 39}}, "1": {"start": {"line": 10, "column": 32}, "end": {"line": 10, "column": 62}}, "2": {"start": {"line": 11, "column": 32}, "end": {"line": 11, "column": 46}}, "3": {"start": {"line": 12, "column": 28}, "end": {"line": 12, "column": 57}}, "4": {"start": {"line": 14, "column": 2}, "end": {"line": 32, "column": 11}}, "5": {"start": {"line": 15, "column": 4}, "end": {"line": 31, "column": 5}}, "6": {"start": {"line": 16, "column": 27}, "end": {"line": 26, "column": 7}}, "7": {"start": {"line": 17, "column": 8}, "end": {"line": 25, "column": 9}}, "8": {"start": {"line": 18, "column": 23}, "end": {"line": 18, "column": 68}}, "9": {"start": {"line": 19, "column": 10}, "end": {"line": 19, "column": 27}}, "10": {"start": {"line": 21, "column": 10}, "end": {"line": 21, "column": 55}}, "11": {"start": {"line": 22, "column": 10}, "end": {"line": 22, "column": 29}}, "12": {"start": {"line": 24, "column": 10}, "end": {"line": 24, "column": 28}}, "13": {"start": {"line": 27, "column": 6}, "end": {"line": 27, "column": 21}}, "14": {"start": {"line": 29, "column": 6}, "end": {"line": 29, "column": 43}}, "15": {"start": {"line": 30, "column": 6}, "end": {"line": 30, "column": 24}}, "16": {"start": {"line": 34, "column": 2}, "end": {"line": 41, "column": 3}}, "17": {"start": {"line": 35, "column": 4}, "end": {"line": 40, "column": 6}}, "18": {"start": {"line": 43, "column": 2}, "end": {"line": 49, "column": 3}}, "19": {"start": {"line": 44, "column": 4}, "end": {"line": 48, "column": 6}}, "20": {"start": {"line": 51, "column": 2}, "end": {"line": 57, "column": 3}}, "21": {"start": {"line": 52, "column": 4}, "end": {"line": 56, "column": 6}}, "22": {"start": {"line": 59, "column": 2}, "end": {"line": 77, "column": 4}}, "23": {"start": {"line": 80, "column": 15}, "end": {"line": 107, "column": 2}}}, "fnMap": {"0": {"name": "ChapterDetailsScreen", "decl": {"start": {"line": 8, "column": 24}, "end": {"line": 8, "column": 44}}, "loc": {"start": {"line": 8, "column": 47}, "end": {"line": 78, "column": 1}}, "line": 8}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 14, "column": 12}, "end": {"line": 14, "column": 13}}, "loc": {"start": {"line": 14, "column": 18}, "end": {"line": 32, "column": 3}}, "line": 14}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 16, "column": 27}, "end": {"line": 16, "column": 28}}, "loc": {"start": {"line": 16, "column": 39}, "end": {"line": 26, "column": 7}}, "line": 16}}, "branchMap": {"0": {"loc": {"start": {"line": 15, "column": 4}, "end": {"line": 31, "column": 5}}, "type": "if", "locations": [{"start": {"line": 15, "column": 4}, "end": {"line": 31, "column": 5}}, {"start": {"line": 28, "column": 11}, "end": {"line": 31, "column": 5}}], "line": 15}, "1": {"loc": {"start": {"line": 34, "column": 2}, "end": {"line": 41, "column": 3}}, "type": "if", "locations": [{"start": {"line": 34, "column": 2}, "end": {"line": 41, "column": 3}}, {"start": {}, "end": {}}], "line": 34}, "2": {"loc": {"start": {"line": 43, "column": 2}, "end": {"line": 49, "column": 3}}, "type": "if", "locations": [{"start": {"line": 43, "column": 2}, "end": {"line": 49, "column": 3}}, {"start": {}, "end": {}}], "line": 43}, "3": {"loc": {"start": {"line": 51, "column": 2}, "end": {"line": 57, "column": 3}}, "type": "if", "locations": [{"start": {"line": 51, "column": 2}, "end": {"line": 57, "column": 3}}, {"start": {}, "end": {}}], "line": 51}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0}, "f": {"0": 0, "1": 0, "2": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0], "3": [0, 0]}}, "/home/<USER>/projects/azmoon-client/app/screens/ChapterEdit.tsx": {"path": "/home/<USER>/projects/azmoon-client/app/screens/ChapterEdit.tsx", "statementMap": {"0": {"start": {"line": 9, "column": 17}, "end": {"line": 9, "column": 39}}, "1": {"start": {"line": 10, "column": 26}, "end": {"line": 10, "column": 38}}, "2": {"start": {"line": 11, "column": 32}, "end": {"line": 11, "column": 46}}, "3": {"start": {"line": 12, "column": 30}, "end": {"line": 12, "column": 45}}, "4": {"start": {"line": 13, "column": 28}, "end": {"line": 13, "column": 57}}, "5": {"start": {"line": 15, "column": 2}, "end": {"line": 33, "column": 11}}, "6": {"start": {"line": 16, "column": 4}, "end": {"line": 32, "column": 5}}, "7": {"start": {"line": 17, "column": 27}, "end": {"line": 27, "column": 7}}, "8": {"start": {"line": 18, "column": 8}, "end": {"line": 26, "column": 9}}, "9": {"start": {"line": 19, "column": 23}, "end": {"line": 19, "column": 68}}, "10": {"start": {"line": 20, "column": 10}, "end": {"line": 20, "column": 29}}, "11": {"start": {"line": 22, "column": 10}, "end": {"line": 22, "column": 67}}, "12": {"start": {"line": 23, "column": 10}, "end": {"line": 23, "column": 29}}, "13": {"start": {"line": 25, "column": 10}, "end": {"line": 25, "column": 28}}, "14": {"start": {"line": 28, "column": 6}, "end": {"line": 28, "column": 21}}, "15": {"start": {"line": 30, "column": 6}, "end": {"line": 30, "column": 55}}, "16": {"start": {"line": 31, "column": 6}, "end": {"line": 31, "column": 24}}, "17": {"start": {"line": 35, "column": 30}, "end": {"line": 56, "column": 3}}, "18": {"start": {"line": 36, "column": 4}, "end": {"line": 39, "column": 5}}, "19": {"start": {"line": 37, "column": 6}, "end": {"line": 37, "column": 71}}, "20": {"start": {"line": 38, "column": 6}, "end": {"line": 38, "column": 13}}, "21": {"start": {"line": 40, "column": 4}, "end": {"line": 43, "column": 5}}, "22": {"start": {"line": 41, "column": 6}, "end": {"line": 41, "column": 53}}, "23": {"start": {"line": 42, "column": 6}, "end": {"line": 42, "column": 13}}, "24": {"start": {"line": 45, "column": 4}, "end": {"line": 45, "column": 20}}, "25": {"start": {"line": 46, "column": 4}, "end": {"line": 55, "column": 5}}, "26": {"start": {"line": 47, "column": 6}, "end": {"line": 47, "column": 65}}, "27": {"start": {"line": 48, "column": 6}, "end": {"line": 48, "column": 62}}, "28": {"start": {"line": 49, "column": 6}, "end": {"line": 49, "column": 20}}, "29": {"start": {"line": 51, "column": 6}, "end": {"line": 51, "column": 74}}, "30": {"start": {"line": 52, "column": 6}, "end": {"line": 52, "column": 56}}, "31": {"start": {"line": 54, "column": 6}, "end": {"line": 54, "column": 23}}, "32": {"start": {"line": 58, "column": 2}, "end": {"line": 65, "column": 3}}, "33": {"start": {"line": 59, "column": 4}, "end": {"line": 64, "column": 6}}, "34": {"start": {"line": 67, "column": 2}, "end": {"line": 73, "column": 3}}, "35": {"start": {"line": 68, "column": 4}, "end": {"line": 72, "column": 6}}, "36": {"start": {"line": 75, "column": 2}, "end": {"line": 95, "column": 4}}, "37": {"start": {"line": 98, "column": 15}, "end": {"line": 142, "column": 2}}}, "fnMap": {"0": {"name": "ChapterEditScreen", "decl": {"start": {"line": 8, "column": 24}, "end": {"line": 8, "column": 41}}, "loc": {"start": {"line": 8, "column": 44}, "end": {"line": 96, "column": 1}}, "line": 8}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 15, "column": 12}, "end": {"line": 15, "column": 13}}, "loc": {"start": {"line": 15, "column": 18}, "end": {"line": 33, "column": 3}}, "line": 15}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 17, "column": 27}, "end": {"line": 17, "column": 28}}, "loc": {"start": {"line": 17, "column": 39}, "end": {"line": 27, "column": 7}}, "line": 17}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 35, "column": 30}, "end": {"line": 35, "column": 31}}, "loc": {"start": {"line": 35, "column": 42}, "end": {"line": 56, "column": 3}}, "line": 35}}, "branchMap": {"0": {"loc": {"start": {"line": 16, "column": 4}, "end": {"line": 32, "column": 5}}, "type": "if", "locations": [{"start": {"line": 16, "column": 4}, "end": {"line": 32, "column": 5}}, {"start": {"line": 29, "column": 11}, "end": {"line": 32, "column": 5}}], "line": 16}, "1": {"loc": {"start": {"line": 36, "column": 4}, "end": {"line": 39, "column": 5}}, "type": "if", "locations": [{"start": {"line": 36, "column": 4}, "end": {"line": 39, "column": 5}}, {"start": {}, "end": {}}], "line": 36}, "2": {"loc": {"start": {"line": 40, "column": 4}, "end": {"line": 43, "column": 5}}, "type": "if", "locations": [{"start": {"line": 40, "column": 4}, "end": {"line": 43, "column": 5}}, {"start": {}, "end": {}}], "line": 40}, "3": {"loc": {"start": {"line": 58, "column": 2}, "end": {"line": 65, "column": 3}}, "type": "if", "locations": [{"start": {"line": 58, "column": 2}, "end": {"line": 65, "column": 3}}, {"start": {}, "end": {}}], "line": 58}, "4": {"loc": {"start": {"line": 67, "column": 2}, "end": {"line": 73, "column": 3}}, "type": "if", "locations": [{"start": {"line": 67, "column": 2}, "end": {"line": 73, "column": 3}}, {"start": {}, "end": {}}], "line": 67}, "5": {"loc": {"start": {"line": 86, "column": 31}, "end": {"line": 86, "column": 62}}, "type": "binary-expr", "locations": [{"start": {"line": 86, "column": 31}, "end": {"line": 86, "column": 37}}, {"start": {"line": 86, "column": 41}, "end": {"line": 86, "column": 62}}], "line": 86}, "6": {"loc": {"start": {"line": 91, "column": 11}, "end": {"line": 91, "column": 49}}, "type": "cond-expr", "locations": [{"start": {"line": 91, "column": 20}, "end": {"line": 91, "column": 31}}, {"start": {"line": 91, "column": 34}, "end": {"line": 91, "column": 49}}], "line": 91}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0], "3": [0, 0], "4": [0, 0], "5": [0, 0], "6": [0, 0]}}, "/home/<USER>/projects/azmoon-client/app/screens/ChaptersList.tsx": {"path": "/home/<USER>/projects/azmoon-client/app/screens/ChaptersList.tsx", "statementMap": {"0": {"start": {"line": 9, "column": 23}, "end": {"line": 9, "column": 45}}, "1": {"start": {"line": 10, "column": 34}, "end": {"line": 10, "column": 57}}, "2": {"start": {"line": 11, "column": 32}, "end": {"line": 11, "column": 46}}, "3": {"start": {"line": 12, "column": 38}, "end": {"line": 12, "column": 53}}, "4": {"start": {"line": 13, "column": 28}, "end": {"line": 13, "column": 57}}, "5": {"start": {"line": 15, "column": 23}, "end": {"line": 34, "column": 3}}, "6": {"start": {"line": 16, "column": 4}, "end": {"line": 33, "column": 5}}, "7": {"start": {"line": 17, "column": 6}, "end": {"line": 17, "column": 23}}, "8": {"start": {"line": 18, "column": 6}, "end": {"line": 18, "column": 21}}, "9": {"start": {"line": 19, "column": 19}, "end": {"line": 19, "column": 53}}, "10": {"start": {"line": 21, "column": 6}, "end": {"line": 27, "column": 7}}, "11": {"start": {"line": 22, "column": 32}, "end": {"line": 22, "column": 100}}, "12": {"start": {"line": 23, "column": 33}, "end": {"line": 23, "column": 94}}, "13": {"start": {"line": 23, "column": 56}, "end": {"line": 23, "column": 93}}, "14": {"start": {"line": 24, "column": 8}, "end": {"line": 24, "column": 38}}, "15": {"start": {"line": 26, "column": 8}, "end": {"line": 26, "column": 26}}, "16": {"start": {"line": 29, "column": 6}, "end": {"line": 29, "column": 44}}, "17": {"start": {"line": 30, "column": 6}, "end": {"line": 30, "column": 25}}, "18": {"start": {"line": 32, "column": 6}, "end": {"line": 32, "column": 24}}, "19": {"start": {"line": 36, "column": 24}, "end": {"line": 55, "column": 3}}, "20": {"start": {"line": 37, "column": 4}, "end": {"line": 54, "column": 5}}, "21": {"start": {"line": 38, "column": 6}, "end": {"line": 38, "column": 26}}, "22": {"start": {"line": 39, "column": 6}, "end": {"line": 39, "column": 21}}, "23": {"start": {"line": 40, "column": 19}, "end": {"line": 40, "column": 53}}, "24": {"start": {"line": 42, "column": 6}, "end": {"line": 48, "column": 7}}, "25": {"start": {"line": 43, "column": 32}, "end": {"line": 43, "column": 100}}, "26": {"start": {"line": 44, "column": 33}, "end": {"line": 44, "column": 94}}, "27": {"start": {"line": 44, "column": 56}, "end": {"line": 44, "column": 93}}, "28": {"start": {"line": 45, "column": 8}, "end": {"line": 45, "column": 38}}, "29": {"start": {"line": 47, "column": 8}, "end": {"line": 47, "column": 26}}, "30": {"start": {"line": 50, "column": 6}, "end": {"line": 50, "column": 46}}, "31": {"start": {"line": 51, "column": 6}, "end": {"line": 51, "column": 25}}, "32": {"start": {"line": 53, "column": 6}, "end": {"line": 53, "column": 27}}, "33": {"start": {"line": 57, "column": 2}, "end": {"line": 59, "column": 17}}, "34": {"start": {"line": 58, "column": 4}, "end": {"line": 58, "column": 19}}, "35": {"start": {"line": 61, "column": 30}, "end": {"line": 87, "column": 3}}, "36": {"start": {"line": 62, "column": 4}, "end": {"line": 86, "column": 6}}, "37": {"start": {"line": 73, "column": 12}, "end": {"line": 80, "column": 13}}, "38": {"start": {"line": 74, "column": 14}, "end": {"line": 74, "column": 53}}, "39": {"start": {"line": 75, "column": 14}, "end": {"line": 75, "column": 70}}, "40": {"start": {"line": 76, "column": 14}, "end": {"line": 76, "column": 29}}, "41": {"start": {"line": 78, "column": 14}, "end": {"line": 78, "column": 82}}, "42": {"start": {"line": 79, "column": 14}, "end": {"line": 79, "column": 64}}, "43": {"start": {"line": 89, "column": 2}, "end": {"line": 96, "column": 3}}, "44": {"start": {"line": 90, "column": 4}, "end": {"line": 95, "column": 6}}, "45": {"start": {"line": 98, "column": 2}, "end": {"line": 104, "column": 3}}, "46": {"start": {"line": 99, "column": 4}, "end": {"line": 103, "column": 6}}, "47": {"start": {"line": 106, "column": 2}, "end": {"line": 153, "column": 4}}, "48": {"start": {"line": 120, "column": 34}, "end": {"line": 120, "column": 41}}, "49": {"start": {"line": 122, "column": 12}, "end": {"line": 140, "column": 25}}, "50": {"start": {"line": 123, "column": 40}, "end": {"line": 123, "column": 92}}, "51": {"start": {"line": 135, "column": 33}, "end": {"line": 135, "column": 61}}, "52": {"start": {"line": 156, "column": 15}, "end": {"line": 219, "column": 2}}}, "fnMap": {"0": {"name": "ChaptersListScreen", "decl": {"start": {"line": 8, "column": 24}, "end": {"line": 8, "column": 42}}, "loc": {"start": {"line": 8, "column": 45}, "end": {"line": 154, "column": 1}}, "line": 8}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 15, "column": 23}, "end": {"line": 15, "column": 24}}, "loc": {"start": {"line": 15, "column": 35}, "end": {"line": 34, "column": 3}}, "line": 15}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 23, "column": 45}, "end": {"line": 23, "column": 46}}, "loc": {"start": {"line": 23, "column": 56}, "end": {"line": 23, "column": 93}}, "line": 23}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 36, "column": 24}, "end": {"line": 36, "column": 25}}, "loc": {"start": {"line": 36, "column": 36}, "end": {"line": 55, "column": 3}}, "line": 36}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 44, "column": 45}, "end": {"line": 44, "column": 46}}, "loc": {"start": {"line": 44, "column": 56}, "end": {"line": 44, "column": 93}}, "line": 44}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 57, "column": 12}, "end": {"line": 57, "column": 13}}, "loc": {"start": {"line": 57, "column": 18}, "end": {"line": 59, "column": 3}}, "line": 57}, "6": {"name": "(anonymous_6)", "decl": {"start": {"line": 61, "column": 30}, "end": {"line": 61, "column": 31}}, "loc": {"start": {"line": 61, "column": 52}, "end": {"line": 87, "column": 3}}, "line": 61}, "7": {"name": "(anonymous_7)", "decl": {"start": {"line": 72, "column": 19}, "end": {"line": 72, "column": 20}}, "loc": {"start": {"line": 72, "column": 31}, "end": {"line": 81, "column": 11}}, "line": 72}, "8": {"name": "(anonymous_8)", "decl": {"start": {"line": 120, "column": 24}, "end": {"line": 120, "column": 25}}, "loc": {"start": {"line": 120, "column": 34}, "end": {"line": 120, "column": 41}}, "line": 120}, "9": {"name": "(anonymous_9)", "decl": {"start": {"line": 121, "column": 22}, "end": {"line": 121, "column": 23}}, "loc": {"start": {"line": 122, "column": 12}, "end": {"line": 140, "column": 25}}, "line": 122}, "10": {"name": "(anonymous_10)", "decl": {"start": {"line": 123, "column": 34}, "end": {"line": 123, "column": 35}}, "loc": {"start": {"line": 123, "column": 40}, "end": {"line": 123, "column": 92}}, "line": 123}, "11": {"name": "(anonymous_11)", "decl": {"start": {"line": 135, "column": 27}, "end": {"line": 135, "column": 28}}, "loc": {"start": {"line": 135, "column": 33}, "end": {"line": 135, "column": 61}}, "line": 135}}, "branchMap": {"0": {"loc": {"start": {"line": 21, "column": 6}, "end": {"line": 27, "column": 7}}, "type": "if", "locations": [{"start": {"line": 21, "column": 6}, "end": {"line": 27, "column": 7}}, {"start": {"line": 25, "column": 13}, "end": {"line": 27, "column": 7}}], "line": 21}, "1": {"loc": {"start": {"line": 22, "column": 32}, "end": {"line": 22, "column": 100}}, "type": "cond-expr", "locations": [{"start": {"line": 22, "column": 58}, "end": {"line": 22, "column": 79}}, {"start": {"line": 22, "column": 82}, "end": {"line": 22, "column": 100}}], "line": 22}, "2": {"loc": {"start": {"line": 42, "column": 6}, "end": {"line": 48, "column": 7}}, "type": "if", "locations": [{"start": {"line": 42, "column": 6}, "end": {"line": 48, "column": 7}}, {"start": {"line": 46, "column": 13}, "end": {"line": 48, "column": 7}}], "line": 42}, "3": {"loc": {"start": {"line": 43, "column": 32}, "end": {"line": 43, "column": 100}}, "type": "cond-expr", "locations": [{"start": {"line": 43, "column": 58}, "end": {"line": 43, "column": 79}}, {"start": {"line": 43, "column": 82}, "end": {"line": 43, "column": 100}}], "line": 43}, "4": {"loc": {"start": {"line": 89, "column": 2}, "end": {"line": 96, "column": 3}}, "type": "if", "locations": [{"start": {"line": 89, "column": 2}, "end": {"line": 96, "column": 3}}, {"start": {}, "end": {}}], "line": 89}, "5": {"loc": {"start": {"line": 98, "column": 2}, "end": {"line": 104, "column": 3}}, "type": "if", "locations": [{"start": {"line": 98, "column": 2}, "end": {"line": 104, "column": 3}}, {"start": {}, "end": {}}], "line": 98}, "6": {"loc": {"start": {"line": 108, "column": 49}, "end": {"line": 108, "column": 89}}, "type": "cond-expr", "locations": [{"start": {"line": 108, "column": 60}, "end": {"line": 108, "column": 84}}, {"start": {"line": 108, "column": 87}, "end": {"line": 108, "column": 89}}], "line": 108}, "7": {"loc": {"start": {"line": 109, "column": 40}, "end": {"line": 109, "column": 79}}, "type": "cond-expr", "locations": [{"start": {"line": 109, "column": 51}, "end": {"line": 109, "column": 74}}, {"start": {"line": 109, "column": 77}, "end": {"line": 109, "column": 79}}], "line": 109}, "8": {"loc": {"start": {"line": 114, "column": 7}, "end": {"line": 151, "column": 7}}, "type": "cond-expr", "locations": [{"start": {"line": 115, "column": 8}, "end": {"line": 115, "column": 77}}, {"start": {"line": 117, "column": 8}, "end": {"line": 150, "column": 10}}], "line": 114}, "9": {"loc": {"start": {"line": 125, "column": 17}, "end": {"line": 125, "column": 87}}, "type": "binary-expr", "locations": [{"start": {"line": 125, "column": 17}, "end": {"line": 125, "column": 31}}, {"start": {"line": 125, "column": 35}, "end": {"line": 125, "column": 87}}], "line": 125}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0, "50": 0, "51": 0, "52": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0], "3": [0, 0], "4": [0, 0], "5": [0, 0], "6": [0, 0], "7": [0, 0], "8": [0, 0], "9": [0, 0]}}, "/home/<USER>/projects/azmoon-client/app/screens/CourseAdd.tsx": {"path": "/home/<USER>/projects/azmoon-client/app/screens/CourseAdd.tsx", "statementMap": {"0": {"start": {"line": 9, "column": 26}, "end": {"line": 9, "column": 38}}, "1": {"start": {"line": 10, "column": 32}, "end": {"line": 10, "column": 47}}, "2": {"start": {"line": 12, "column": 26}, "end": {"line": 29, "column": 3}}, "3": {"start": {"line": 13, "column": 4}, "end": {"line": 16, "column": 5}}, "4": {"start": {"line": 14, "column": 6}, "end": {"line": 14, "column": 70}}, "5": {"start": {"line": 15, "column": 6}, "end": {"line": 15, "column": 13}}, "6": {"start": {"line": 18, "column": 4}, "end": {"line": 18, "column": 21}}, "7": {"start": {"line": 19, "column": 4}, "end": {"line": 28, "column": 5}}, "8": {"start": {"line": 20, "column": 6}, "end": {"line": 20, "column": 49}}, "9": {"start": {"line": 21, "column": 6}, "end": {"line": 21, "column": 59}}, "10": {"start": {"line": 22, "column": 6}, "end": {"line": 22, "column": 20}}, "11": {"start": {"line": 24, "column": 6}, "end": {"line": 24, "column": 70}}, "12": {"start": {"line": 25, "column": 6}, "end": {"line": 25, "column": 52}}, "13": {"start": {"line": 27, "column": 6}, "end": {"line": 27, "column": 24}}, "14": {"start": {"line": 31, "column": 2}, "end": {"line": 51, "column": 4}}, "15": {"start": {"line": 54, "column": 15}, "end": {"line": 89, "column": 2}}}, "fnMap": {"0": {"name": "CourseAddScreen", "decl": {"start": {"line": 8, "column": 24}, "end": {"line": 8, "column": 39}}, "loc": {"start": {"line": 8, "column": 42}, "end": {"line": 52, "column": 1}}, "line": 8}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 12, "column": 26}, "end": {"line": 12, "column": 27}}, "loc": {"start": {"line": 12, "column": 38}, "end": {"line": 29, "column": 3}}, "line": 12}}, "branchMap": {"0": {"loc": {"start": {"line": 13, "column": 4}, "end": {"line": 16, "column": 5}}, "type": "if", "locations": [{"start": {"line": 13, "column": 4}, "end": {"line": 16, "column": 5}}, {"start": {}, "end": {}}], "line": 13}, "1": {"loc": {"start": {"line": 42, "column": 31}, "end": {"line": 42, "column": 63}}, "type": "binary-expr", "locations": [{"start": {"line": 42, "column": 31}, "end": {"line": 42, "column": 38}}, {"start": {"line": 42, "column": 42}, "end": {"line": 42, "column": 63}}], "line": 42}, "2": {"loc": {"start": {"line": 47, "column": 11}, "end": {"line": 47, "column": 47}}, "type": "cond-expr", "locations": [{"start": {"line": 47, "column": 21}, "end": {"line": 47, "column": 32}}, {"start": {"line": 47, "column": 35}, "end": {"line": 47, "column": 47}}], "line": 47}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0}, "f": {"0": 0, "1": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0]}}, "/home/<USER>/projects/azmoon-client/app/screens/CourseDetails.tsx": {"path": "/home/<USER>/projects/azmoon-client/app/screens/CourseDetails.tsx", "statementMap": {"0": {"start": {"line": 9, "column": 17}, "end": {"line": 9, "column": 39}}, "1": {"start": {"line": 10, "column": 30}, "end": {"line": 10, "column": 59}}, "2": {"start": {"line": 11, "column": 32}, "end": {"line": 11, "column": 46}}, "3": {"start": {"line": 12, "column": 28}, "end": {"line": 12, "column": 57}}, "4": {"start": {"line": 14, "column": 2}, "end": {"line": 32, "column": 11}}, "5": {"start": {"line": 15, "column": 4}, "end": {"line": 31, "column": 5}}, "6": {"start": {"line": 16, "column": 26}, "end": {"line": 26, "column": 7}}, "7": {"start": {"line": 17, "column": 8}, "end": {"line": 25, "column": 9}}, "8": {"start": {"line": 18, "column": 23}, "end": {"line": 18, "column": 66}}, "9": {"start": {"line": 19, "column": 10}, "end": {"line": 19, "column": 26}}, "10": {"start": {"line": 21, "column": 10}, "end": {"line": 21, "column": 54}}, "11": {"start": {"line": 22, "column": 10}, "end": {"line": 22, "column": 29}}, "12": {"start": {"line": 24, "column": 10}, "end": {"line": 24, "column": 28}}, "13": {"start": {"line": 27, "column": 6}, "end": {"line": 27, "column": 20}}, "14": {"start": {"line": 29, "column": 6}, "end": {"line": 29, "column": 42}}, "15": {"start": {"line": 30, "column": 6}, "end": {"line": 30, "column": 24}}, "16": {"start": {"line": 34, "column": 2}, "end": {"line": 41, "column": 3}}, "17": {"start": {"line": 35, "column": 4}, "end": {"line": 40, "column": 6}}, "18": {"start": {"line": 43, "column": 2}, "end": {"line": 49, "column": 3}}, "19": {"start": {"line": 44, "column": 4}, "end": {"line": 48, "column": 6}}, "20": {"start": {"line": 51, "column": 2}, "end": {"line": 57, "column": 3}}, "21": {"start": {"line": 52, "column": 4}, "end": {"line": 56, "column": 6}}, "22": {"start": {"line": 59, "column": 2}, "end": {"line": 77, "column": 4}}, "23": {"start": {"line": 80, "column": 15}, "end": {"line": 107, "column": 2}}}, "fnMap": {"0": {"name": "CourseDetailsScreen", "decl": {"start": {"line": 8, "column": 24}, "end": {"line": 8, "column": 43}}, "loc": {"start": {"line": 8, "column": 46}, "end": {"line": 78, "column": 1}}, "line": 8}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 14, "column": 12}, "end": {"line": 14, "column": 13}}, "loc": {"start": {"line": 14, "column": 18}, "end": {"line": 32, "column": 3}}, "line": 14}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 16, "column": 26}, "end": {"line": 16, "column": 27}}, "loc": {"start": {"line": 16, "column": 38}, "end": {"line": 26, "column": 7}}, "line": 16}}, "branchMap": {"0": {"loc": {"start": {"line": 15, "column": 4}, "end": {"line": 31, "column": 5}}, "type": "if", "locations": [{"start": {"line": 15, "column": 4}, "end": {"line": 31, "column": 5}}, {"start": {"line": 28, "column": 11}, "end": {"line": 31, "column": 5}}], "line": 15}, "1": {"loc": {"start": {"line": 34, "column": 2}, "end": {"line": 41, "column": 3}}, "type": "if", "locations": [{"start": {"line": 34, "column": 2}, "end": {"line": 41, "column": 3}}, {"start": {}, "end": {}}], "line": 34}, "2": {"loc": {"start": {"line": 43, "column": 2}, "end": {"line": 49, "column": 3}}, "type": "if", "locations": [{"start": {"line": 43, "column": 2}, "end": {"line": 49, "column": 3}}, {"start": {}, "end": {}}], "line": 43}, "3": {"loc": {"start": {"line": 51, "column": 2}, "end": {"line": 57, "column": 3}}, "type": "if", "locations": [{"start": {"line": 51, "column": 2}, "end": {"line": 57, "column": 3}}, {"start": {}, "end": {}}], "line": 51}, "4": {"loc": {"start": {"line": 70, "column": 7}, "end": {"line": 75, "column": 7}}, "type": "binary-expr", "locations": [{"start": {"line": 70, "column": 7}, "end": {"line": 70, "column": 23}}, {"start": {"line": 71, "column": 8}, "end": {"line": 74, "column": 21}}], "line": 70}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0}, "f": {"0": 0, "1": 0, "2": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0], "3": [0, 0], "4": [0, 0]}}, "/home/<USER>/projects/azmoon-client/app/screens/CourseEdit.tsx": {"path": "/home/<USER>/projects/azmoon-client/app/screens/CourseEdit.tsx", "statementMap": {"0": {"start": {"line": 9, "column": 17}, "end": {"line": 9, "column": 39}}, "1": {"start": {"line": 10, "column": 26}, "end": {"line": 10, "column": 38}}, "2": {"start": {"line": 11, "column": 32}, "end": {"line": 11, "column": 46}}, "3": {"start": {"line": 12, "column": 30}, "end": {"line": 12, "column": 45}}, "4": {"start": {"line": 13, "column": 28}, "end": {"line": 13, "column": 57}}, "5": {"start": {"line": 15, "column": 2}, "end": {"line": 33, "column": 11}}, "6": {"start": {"line": 16, "column": 4}, "end": {"line": 32, "column": 5}}, "7": {"start": {"line": 17, "column": 26}, "end": {"line": 27, "column": 7}}, "8": {"start": {"line": 18, "column": 8}, "end": {"line": 26, "column": 9}}, "9": {"start": {"line": 19, "column": 23}, "end": {"line": 19, "column": 66}}, "10": {"start": {"line": 20, "column": 10}, "end": {"line": 20, "column": 29}}, "11": {"start": {"line": 22, "column": 10}, "end": {"line": 22, "column": 66}}, "12": {"start": {"line": 23, "column": 10}, "end": {"line": 23, "column": 29}}, "13": {"start": {"line": 25, "column": 10}, "end": {"line": 25, "column": 28}}, "14": {"start": {"line": 28, "column": 6}, "end": {"line": 28, "column": 20}}, "15": {"start": {"line": 30, "column": 6}, "end": {"line": 30, "column": 54}}, "16": {"start": {"line": 31, "column": 6}, "end": {"line": 31, "column": 24}}, "17": {"start": {"line": 35, "column": 29}, "end": {"line": 56, "column": 3}}, "18": {"start": {"line": 36, "column": 4}, "end": {"line": 39, "column": 5}}, "19": {"start": {"line": 37, "column": 6}, "end": {"line": 37, "column": 70}}, "20": {"start": {"line": 38, "column": 6}, "end": {"line": 38, "column": 13}}, "21": {"start": {"line": 40, "column": 4}, "end": {"line": 43, "column": 5}}, "22": {"start": {"line": 41, "column": 6}, "end": {"line": 41, "column": 52}}, "23": {"start": {"line": 42, "column": 6}, "end": {"line": 42, "column": 13}}, "24": {"start": {"line": 45, "column": 4}, "end": {"line": 45, "column": 20}}, "25": {"start": {"line": 46, "column": 4}, "end": {"line": 55, "column": 5}}, "26": {"start": {"line": 47, "column": 6}, "end": {"line": 47, "column": 63}}, "27": {"start": {"line": 48, "column": 6}, "end": {"line": 48, "column": 61}}, "28": {"start": {"line": 49, "column": 6}, "end": {"line": 49, "column": 20}}, "29": {"start": {"line": 51, "column": 6}, "end": {"line": 51, "column": 73}}, "30": {"start": {"line": 52, "column": 6}, "end": {"line": 52, "column": 55}}, "31": {"start": {"line": 54, "column": 6}, "end": {"line": 54, "column": 23}}, "32": {"start": {"line": 58, "column": 2}, "end": {"line": 65, "column": 3}}, "33": {"start": {"line": 59, "column": 4}, "end": {"line": 64, "column": 6}}, "34": {"start": {"line": 67, "column": 2}, "end": {"line": 73, "column": 3}}, "35": {"start": {"line": 68, "column": 4}, "end": {"line": 72, "column": 6}}, "36": {"start": {"line": 75, "column": 2}, "end": {"line": 95, "column": 4}}, "37": {"start": {"line": 98, "column": 15}, "end": {"line": 142, "column": 2}}}, "fnMap": {"0": {"name": "CourseEditScreen", "decl": {"start": {"line": 8, "column": 24}, "end": {"line": 8, "column": 40}}, "loc": {"start": {"line": 8, "column": 43}, "end": {"line": 96, "column": 1}}, "line": 8}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 15, "column": 12}, "end": {"line": 15, "column": 13}}, "loc": {"start": {"line": 15, "column": 18}, "end": {"line": 33, "column": 3}}, "line": 15}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 17, "column": 26}, "end": {"line": 17, "column": 27}}, "loc": {"start": {"line": 17, "column": 38}, "end": {"line": 27, "column": 7}}, "line": 17}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 35, "column": 29}, "end": {"line": 35, "column": 30}}, "loc": {"start": {"line": 35, "column": 41}, "end": {"line": 56, "column": 3}}, "line": 35}}, "branchMap": {"0": {"loc": {"start": {"line": 16, "column": 4}, "end": {"line": 32, "column": 5}}, "type": "if", "locations": [{"start": {"line": 16, "column": 4}, "end": {"line": 32, "column": 5}}, {"start": {"line": 29, "column": 11}, "end": {"line": 32, "column": 5}}], "line": 16}, "1": {"loc": {"start": {"line": 36, "column": 4}, "end": {"line": 39, "column": 5}}, "type": "if", "locations": [{"start": {"line": 36, "column": 4}, "end": {"line": 39, "column": 5}}, {"start": {}, "end": {}}], "line": 36}, "2": {"loc": {"start": {"line": 40, "column": 4}, "end": {"line": 43, "column": 5}}, "type": "if", "locations": [{"start": {"line": 40, "column": 4}, "end": {"line": 43, "column": 5}}, {"start": {}, "end": {}}], "line": 40}, "3": {"loc": {"start": {"line": 58, "column": 2}, "end": {"line": 65, "column": 3}}, "type": "if", "locations": [{"start": {"line": 58, "column": 2}, "end": {"line": 65, "column": 3}}, {"start": {}, "end": {}}], "line": 58}, "4": {"loc": {"start": {"line": 67, "column": 2}, "end": {"line": 73, "column": 3}}, "type": "if", "locations": [{"start": {"line": 67, "column": 2}, "end": {"line": 73, "column": 3}}, {"start": {}, "end": {}}], "line": 67}, "5": {"loc": {"start": {"line": 86, "column": 31}, "end": {"line": 86, "column": 62}}, "type": "binary-expr", "locations": [{"start": {"line": 86, "column": 31}, "end": {"line": 86, "column": 37}}, {"start": {"line": 86, "column": 41}, "end": {"line": 86, "column": 62}}], "line": 86}, "6": {"loc": {"start": {"line": 91, "column": 11}, "end": {"line": 91, "column": 49}}, "type": "cond-expr", "locations": [{"start": {"line": 91, "column": 20}, "end": {"line": 91, "column": 31}}, {"start": {"line": 91, "column": 34}, "end": {"line": 91, "column": 49}}], "line": 91}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0], "3": [0, 0], "4": [0, 0], "5": [0, 0], "6": [0, 0]}}, "/home/<USER>/projects/azmoon-client/app/screens/CoursesList.tsx": {"path": "/home/<USER>/projects/azmoon-client/app/screens/CoursesList.tsx", "statementMap": {"0": {"start": {"line": 9, "column": 32}, "end": {"line": 9, "column": 54}}, "1": {"start": {"line": 10, "column": 32}, "end": {"line": 10, "column": 46}}, "2": {"start": {"line": 11, "column": 38}, "end": {"line": 11, "column": 53}}, "3": {"start": {"line": 12, "column": 28}, "end": {"line": 12, "column": 57}}, "4": {"start": {"line": 14, "column": 22}, "end": {"line": 26, "column": 3}}, "5": {"start": {"line": 15, "column": 4}, "end": {"line": 25, "column": 5}}, "6": {"start": {"line": 16, "column": 6}, "end": {"line": 16, "column": 23}}, "7": {"start": {"line": 17, "column": 6}, "end": {"line": 17, "column": 21}}, "8": {"start": {"line": 18, "column": 19}, "end": {"line": 18, "column": 51}}, "9": {"start": {"line": 19, "column": 6}, "end": {"line": 19, "column": 23}}, "10": {"start": {"line": 21, "column": 6}, "end": {"line": 21, "column": 43}}, "11": {"start": {"line": 22, "column": 6}, "end": {"line": 22, "column": 25}}, "12": {"start": {"line": 24, "column": 6}, "end": {"line": 24, "column": 24}}, "13": {"start": {"line": 28, "column": 24}, "end": {"line": 40, "column": 3}}, "14": {"start": {"line": 29, "column": 4}, "end": {"line": 39, "column": 5}}, "15": {"start": {"line": 30, "column": 6}, "end": {"line": 30, "column": 26}}, "16": {"start": {"line": 31, "column": 6}, "end": {"line": 31, "column": 21}}, "17": {"start": {"line": 32, "column": 19}, "end": {"line": 32, "column": 51}}, "18": {"start": {"line": 33, "column": 6}, "end": {"line": 33, "column": 23}}, "19": {"start": {"line": 35, "column": 6}, "end": {"line": 35, "column": 45}}, "20": {"start": {"line": 36, "column": 6}, "end": {"line": 36, "column": 25}}, "21": {"start": {"line": 38, "column": 6}, "end": {"line": 38, "column": 27}}, "22": {"start": {"line": 42, "column": 2}, "end": {"line": 44, "column": 9}}, "23": {"start": {"line": 43, "column": 4}, "end": {"line": 43, "column": 18}}, "24": {"start": {"line": 46, "column": 2}, "end": {"line": 53, "column": 3}}, "25": {"start": {"line": 47, "column": 4}, "end": {"line": 52, "column": 6}}, "26": {"start": {"line": 55, "column": 2}, "end": {"line": 61, "column": 3}}, "27": {"start": {"line": 56, "column": 4}, "end": {"line": 60, "column": 6}}, "28": {"start": {"line": 63, "column": 2}, "end": {"line": 111, "column": 4}}, "29": {"start": {"line": 76, "column": 34}, "end": {"line": 76, "column": 41}}, "30": {"start": {"line": 78, "column": 12}, "end": {"line": 98, "column": 25}}, "31": {"start": {"line": 92, "column": 33}, "end": {"line": 92, "column": 89}}, "32": {"start": {"line": 114, "column": 15}, "end": {"line": 177, "column": 2}}}, "fnMap": {"0": {"name": "CoursesListScreen", "decl": {"start": {"line": 8, "column": 24}, "end": {"line": 8, "column": 41}}, "loc": {"start": {"line": 8, "column": 44}, "end": {"line": 112, "column": 1}}, "line": 8}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 14, "column": 22}, "end": {"line": 14, "column": 23}}, "loc": {"start": {"line": 14, "column": 34}, "end": {"line": 26, "column": 3}}, "line": 14}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 28, "column": 24}, "end": {"line": 28, "column": 25}}, "loc": {"start": {"line": 28, "column": 36}, "end": {"line": 40, "column": 3}}, "line": 28}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 42, "column": 12}, "end": {"line": 42, "column": 13}}, "loc": {"start": {"line": 42, "column": 18}, "end": {"line": 44, "column": 3}}, "line": 42}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 76, "column": 24}, "end": {"line": 76, "column": 25}}, "loc": {"start": {"line": 76, "column": 34}, "end": {"line": 76, "column": 41}}, "line": 76}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 77, "column": 22}, "end": {"line": 77, "column": 23}}, "loc": {"start": {"line": 78, "column": 12}, "end": {"line": 98, "column": 25}}, "line": 78}, "6": {"name": "(anonymous_6)", "decl": {"start": {"line": 92, "column": 27}, "end": {"line": 92, "column": 28}}, "loc": {"start": {"line": 92, "column": 33}, "end": {"line": 92, "column": 89}}, "line": 92}}, "branchMap": {"0": {"loc": {"start": {"line": 46, "column": 2}, "end": {"line": 53, "column": 3}}, "type": "if", "locations": [{"start": {"line": 46, "column": 2}, "end": {"line": 53, "column": 3}}, {"start": {}, "end": {}}], "line": 46}, "1": {"loc": {"start": {"line": 55, "column": 2}, "end": {"line": 61, "column": 3}}, "type": "if", "locations": [{"start": {"line": 55, "column": 2}, "end": {"line": 61, "column": 3}}, {"start": {}, "end": {}}], "line": 55}, "2": {"loc": {"start": {"line": 71, "column": 7}, "end": {"line": 109, "column": 7}}, "type": "cond-expr", "locations": [{"start": {"line": 72, "column": 8}, "end": {"line": 72, "column": 73}}, {"start": {"line": 74, "column": 8}, "end": {"line": 108, "column": 10}}], "line": 71}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0]}}, "/home/<USER>/projects/azmoon-client/app/screens/ExamAdd.tsx": {"path": "/home/<USER>/projects/azmoon-client/app/screens/ExamAdd.tsx", "statementMap": {"0": {"start": {"line": 26, "column": 22}, "end": {"line": 39, "column": 1}}, "1": {"start": {"line": 27, "column": 2}, "end": {"line": 38, "column": 8}}, "2": {"start": {"line": 43, "column": 22}, "end": {"line": 197, "column": 1}}, "3": {"start": {"line": 44, "column": 26}, "end": {"line": 44, "column": 46}}, "4": {"start": {"line": 45, "column": 40}, "end": {"line": 45, "column": 52}}, "5": {"start": {"line": 46, "column": 32}, "end": {"line": 46, "column": 47}}, "6": {"start": {"line": 47, "column": 36}, "end": {"line": 47, "column": 48}}, "7": {"start": {"line": 48, "column": 50}, "end": {"line": 48, "column": 62}}, "8": {"start": {"line": 51, "column": 21}, "end": {"line": 53, "column": 25}}, "9": {"start": {"line": 52, "column": 4}, "end": {"line": 52, "column": 48}}, "10": {"start": {"line": 56, "column": 2}, "end": {"line": 59, "column": 19}}, "11": {"start": {"line": 57, "column": 4}, "end": {"line": 57, "column": 46}}, "12": {"start": {"line": 58, "column": 4}, "end": {"line": 58, "column": 60}}, "13": {"start": {"line": 62, "column": 27}, "end": {"line": 67, "column": 3}}, "14": {"start": {"line": 63, "column": 4}, "end": {"line": 63, "column": 18}}, "15": {"start": {"line": 64, "column": 4}, "end": {"line": 66, "column": 5}}, "16": {"start": {"line": 65, "column": 6}, "end": {"line": 65, "column": 23}}, "17": {"start": {"line": 70, "column": 34}, "end": {"line": 75, "column": 3}}, "18": {"start": {"line": 71, "column": 4}, "end": {"line": 71, "column": 25}}, "19": {"start": {"line": 72, "column": 4}, "end": {"line": 74, "column": 5}}, "20": {"start": {"line": 73, "column": 6}, "end": {"line": 73, "column": 30}}, "21": {"start": {"line": 77, "column": 23}, "end": {"line": 105, "column": 3}}, "22": {"start": {"line": 79, "column": 4}, "end": {"line": 82, "column": 5}}, "23": {"start": {"line": 80, "column": 6}, "end": {"line": 80, "column": 59}}, "24": {"start": {"line": 81, "column": 6}, "end": {"line": 81, "column": 13}}, "25": {"start": {"line": 84, "column": 4}, "end": {"line": 104, "column": 5}}, "26": {"start": {"line": 85, "column": 6}, "end": {"line": 85, "column": 23}}, "27": {"start": {"line": 86, "column": 6}, "end": {"line": 89, "column": 9}}, "28": {"start": {"line": 91, "column": 6}, "end": {"line": 98, "column": 9}}, "29": {"start": {"line": 95, "column": 12}, "end": {"line": 95, "column": 26}}, "30": {"start": {"line": 100, "column": 6}, "end": {"line": 100, "column": 52}}, "31": {"start": {"line": 101, "column": 6}, "end": {"line": 101, "column": 72}}, "32": {"start": {"line": 103, "column": 6}, "end": {"line": 103, "column": 24}}, "33": {"start": {"line": 107, "column": 21}, "end": {"line": 109, "column": 3}}, "34": {"start": {"line": 108, "column": 4}, "end": {"line": 108, "column": 18}}, "35": {"start": {"line": 111, "column": 2}, "end": {"line": 196, "column": 4}}, "36": {"start": {"line": 201, "column": 15}, "end": {"line": 294, "column": 2}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 26, "column": 22}, "end": {"line": 26, "column": 23}}, "loc": {"start": {"line": 27, "column": 2}, "end": {"line": 38, "column": 8}}, "line": 27}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 43, "column": 22}, "end": {"line": 43, "column": 23}}, "loc": {"start": {"line": 43, "column": 28}, "end": {"line": 197, "column": 1}}, "line": 43}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 51, "column": 29}, "end": {"line": 51, "column": 30}}, "loc": {"start": {"line": 51, "column": 35}, "end": {"line": 53, "column": 3}}, "line": 51}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 56, "column": 18}, "end": {"line": 56, "column": 19}}, "loc": {"start": {"line": 56, "column": 24}, "end": {"line": 59, "column": 3}}, "line": 56}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 62, "column": 27}, "end": {"line": 62, "column": 28}}, "loc": {"start": {"line": 62, "column": 45}, "end": {"line": 67, "column": 3}}, "line": 62}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 70, "column": 34}, "end": {"line": 70, "column": 35}}, "loc": {"start": {"line": 70, "column": 52}, "end": {"line": 75, "column": 3}}, "line": 70}, "6": {"name": "(anonymous_6)", "decl": {"start": {"line": 77, "column": 23}, "end": {"line": 77, "column": 24}}, "loc": {"start": {"line": 77, "column": 35}, "end": {"line": 105, "column": 3}}, "line": 77}, "7": {"name": "(anonymous_7)", "decl": {"start": {"line": 94, "column": 19}, "end": {"line": 94, "column": 20}}, "loc": {"start": {"line": 94, "column": 31}, "end": {"line": 96, "column": 11}}, "line": 94}, "8": {"name": "(anonymous_8)", "decl": {"start": {"line": 107, "column": 21}, "end": {"line": 107, "column": 22}}, "loc": {"start": {"line": 107, "column": 33}, "end": {"line": 109, "column": 3}}, "line": 107}}, "branchMap": {"0": {"loc": {"start": {"line": 26, "column": 25}, "end": {"line": 26, "column": 42}}, "type": "default-arg", "locations": [{"start": {"line": 26, "column": 33}, "end": {"line": 26, "column": 42}}], "line": 26}, "1": {"loc": {"start": {"line": 26, "column": 44}, "end": {"line": 26, "column": 53}}, "type": "default-arg", "locations": [{"start": {"line": 26, "column": 51}, "end": {"line": 26, "column": 53}}], "line": 26}, "2": {"loc": {"start": {"line": 57, "column": 17}, "end": {"line": 57, "column": 44}}, "type": "binary-expr", "locations": [{"start": {"line": 57, "column": 17}, "end": {"line": 57, "column": 38}}, {"start": {"line": 57, "column": 42}, "end": {"line": 57, "column": 44}}], "line": 57}, "3": {"loc": {"start": {"line": 58, "column": 24}, "end": {"line": 58, "column": 58}}, "type": "binary-expr", "locations": [{"start": {"line": 58, "column": 24}, "end": {"line": 58, "column": 52}}, {"start": {"line": 58, "column": 56}, "end": {"line": 58, "column": 58}}], "line": 58}, "4": {"loc": {"start": {"line": 64, "column": 4}, "end": {"line": 66, "column": 5}}, "type": "if", "locations": [{"start": {"line": 64, "column": 4}, "end": {"line": 66, "column": 5}}, {"start": {}, "end": {}}], "line": 64}, "5": {"loc": {"start": {"line": 72, "column": 4}, "end": {"line": 74, "column": 5}}, "type": "if", "locations": [{"start": {"line": 72, "column": 4}, "end": {"line": 74, "column": 5}}, {"start": {}, "end": {}}], "line": 72}, "6": {"loc": {"start": {"line": 79, "column": 4}, "end": {"line": 82, "column": 5}}, "type": "if", "locations": [{"start": {"line": 79, "column": 4}, "end": {"line": 82, "column": 5}}, {"start": {}, "end": {}}], "line": 79}, "7": {"loc": {"start": {"line": 101, "column": 25}, "end": {"line": 101, "column": 70}}, "type": "binary-expr", "locations": [{"start": {"line": 101, "column": 25}, "end": {"line": 101, "column": 47}}, {"start": {"line": 101, "column": 51}, "end": {"line": 101, "column": 70}}], "line": 101}, "8": {"loc": {"start": {"line": 136, "column": 36}, "end": {"line": 136, "column": 72}}, "type": "cond-expr", "locations": [{"start": {"line": 136, "column": 48}, "end": {"line": 136, "column": 65}}, {"start": {"line": 136, "column": 68}, "end": {"line": 136, "column": 72}}], "line": 136}, "9": {"loc": {"start": {"line": 144, "column": 13}, "end": {"line": 146, "column": 20}}, "type": "cond-expr", "locations": [{"start": {"line": 145, "column": 14}, "end": {"line": 145, "column": 63}}, {"start": {"line": 146, "column": 16}, "end": {"line": 146, "column": 20}}], "line": 144}, "10": {"loc": {"start": {"line": 160, "column": 16}, "end": {"line": 160, "column": 59}}, "type": "cond-expr", "locations": [{"start": {"line": 160, "column": 35}, "end": {"line": 160, "column": 52}}, {"start": {"line": 160, "column": 55}, "end": {"line": 160, "column": 59}}], "line": 160}, "11": {"loc": {"start": {"line": 172, "column": 13}, "end": {"line": 174, "column": 20}}, "type": "cond-expr", "locations": [{"start": {"line": 173, "column": 14}, "end": {"line": 173, "column": 70}}, {"start": {"line": 174, "column": 16}, "end": {"line": 174, "column": 20}}], "line": 172}, "12": {"loc": {"start": {"line": 181, "column": 14}, "end": {"line": 182, "column": 43}}, "type": "binary-expr", "locations": [{"start": {"line": 181, "column": 15}, "end": {"line": 181, "column": 22}}, {"start": {"line": 181, "column": 26}, "end": {"line": 181, "column": 49}}, {"start": {"line": 182, "column": 16}, "end": {"line": 182, "column": 43}}], "line": 181}, "13": {"loc": {"start": {"line": 185, "column": 22}, "end": {"line": 185, "column": 56}}, "type": "binary-expr", "locations": [{"start": {"line": 185, "column": 22}, "end": {"line": 185, "column": 29}}, {"start": {"line": 185, "column": 33}, "end": {"line": 185, "column": 56}}], "line": 185}, "14": {"loc": {"start": {"line": 187, "column": 13}, "end": {"line": 191, "column": 13}}, "type": "cond-expr", "locations": [{"start": {"line": 188, "column": 14}, "end": {"line": 188, "column": 64}}, {"start": {"line": 190, "column": 14}, "end": {"line": 190, "column": 64}}], "line": 187}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0}, "b": {"0": [0], "1": [0], "2": [0, 0], "3": [0, 0], "4": [0, 0], "5": [0, 0], "6": [0, 0], "7": [0, 0], "8": [0, 0], "9": [0, 0], "10": [0, 0], "11": [0, 0], "12": [0, 0, 0], "13": [0, 0], "14": [0, 0]}}, "/home/<USER>/projects/azmoon-client/app/screens/ExamEdit.tsx": {"path": "/home/<USER>/projects/azmoon-client/app/screens/ExamEdit.tsx", "statementMap": {"0": {"start": {"line": 26, "column": 22}, "end": {"line": 39, "column": 1}}, "1": {"start": {"line": 27, "column": 2}, "end": {"line": 38, "column": 8}}, "2": {"start": {"line": 41, "column": 18}, "end": {"line": 54, "column": 1}}, "3": {"start": {"line": 42, "column": 2}, "end": {"line": 53, "column": 8}}, "4": {"start": {"line": 58, "column": 23}, "end": {"line": 309, "column": 1}}, "5": {"start": {"line": 59, "column": 17}, "end": {"line": 59, "column": 39}}, "6": {"start": {"line": 60, "column": 26}, "end": {"line": 60, "column": 38}}, "7": {"start": {"line": 61, "column": 40}, "end": {"line": 61, "column": 52}}, "8": {"start": {"line": 62, "column": 32}, "end": {"line": 62, "column": 47}}, "9": {"start": {"line": 63, "column": 34}, "end": {"line": 63, "column": 49}}, "10": {"start": {"line": 64, "column": 46}, "end": {"line": 64, "column": 60}}, "11": {"start": {"line": 65, "column": 36}, "end": {"line": 65, "column": 48}}, "12": {"start": {"line": 66, "column": 50}, "end": {"line": 66, "column": 62}}, "13": {"start": {"line": 68, "column": 20}, "end": {"line": 85, "column": 10}}, "14": {"start": {"line": 69, "column": 4}, "end": {"line": 72, "column": 5}}, "15": {"start": {"line": 70, "column": 6}, "end": {"line": 70, "column": 51}}, "16": {"start": {"line": 71, "column": 6}, "end": {"line": 71, "column": 13}}, "17": {"start": {"line": 74, "column": 4}, "end": {"line": 84, "column": 5}}, "18": {"start": {"line": 75, "column": 6}, "end": {"line": 75, "column": 30}}, "19": {"start": {"line": 76, "column": 20}, "end": {"line": 76, "column": 51}}, "20": {"start": {"line": 77, "column": 6}, "end": {"line": 77, "column": 26}}, "21": {"start": {"line": 78, "column": 6}, "end": {"line": 78, "column": 46}}, "22": {"start": {"line": 80, "column": 6}, "end": {"line": 80, "column": 50}}, "23": {"start": {"line": 81, "column": 6}, "end": {"line": 81, "column": 75}}, "24": {"start": {"line": 83, "column": 6}, "end": {"line": 83, "column": 31}}, "25": {"start": {"line": 87, "column": 2}, "end": {"line": 89, "column": 18}}, "26": {"start": {"line": 88, "column": 4}, "end": {"line": 88, "column": 16}}, "27": {"start": {"line": 92, "column": 21}, "end": {"line": 94, "column": 25}}, "28": {"start": {"line": 93, "column": 4}, "end": {"line": 93, "column": 48}}, "29": {"start": {"line": 97, "column": 2}, "end": {"line": 100, "column": 19}}, "30": {"start": {"line": 98, "column": 4}, "end": {"line": 98, "column": 46}}, "31": {"start": {"line": 99, "column": 4}, "end": {"line": 99, "column": 60}}, "32": {"start": {"line": 103, "column": 27}, "end": {"line": 109, "column": 3}}, "33": {"start": {"line": 104, "column": 4}, "end": {"line": 104, "column": 18}}, "34": {"start": {"line": 106, "column": 4}, "end": {"line": 108, "column": 5}}, "35": {"start": {"line": 107, "column": 6}, "end": {"line": 107, "column": 23}}, "36": {"start": {"line": 112, "column": 34}, "end": {"line": 118, "column": 3}}, "37": {"start": {"line": 113, "column": 4}, "end": {"line": 113, "column": 25}}, "38": {"start": {"line": 115, "column": 4}, "end": {"line": 117, "column": 5}}, "39": {"start": {"line": 116, "column": 6}, "end": {"line": 116, "column": 30}}, "40": {"start": {"line": 120, "column": 23}, "end": {"line": 153, "column": 3}}, "41": {"start": {"line": 122, "column": 4}, "end": {"line": 125, "column": 5}}, "42": {"start": {"line": 123, "column": 6}, "end": {"line": 123, "column": 59}}, "43": {"start": {"line": 124, "column": 6}, "end": {"line": 124, "column": 13}}, "44": {"start": {"line": 127, "column": 4}, "end": {"line": 130, "column": 5}}, "45": {"start": {"line": 128, "column": 6}, "end": {"line": 128, "column": 51}}, "46": {"start": {"line": 129, "column": 6}, "end": {"line": 129, "column": 13}}, "47": {"start": {"line": 132, "column": 4}, "end": {"line": 152, "column": 5}}, "48": {"start": {"line": 133, "column": 6}, "end": {"line": 133, "column": 23}}, "49": {"start": {"line": 134, "column": 6}, "end": {"line": 137, "column": 9}}, "50": {"start": {"line": 139, "column": 6}, "end": {"line": 146, "column": 9}}, "51": {"start": {"line": 143, "column": 12}, "end": {"line": 143, "column": 26}}, "52": {"start": {"line": 148, "column": 6}, "end": {"line": 148, "column": 52}}, "53": {"start": {"line": 149, "column": 6}, "end": {"line": 149, "column": 78}}, "54": {"start": {"line": 151, "column": 6}, "end": {"line": 151, "column": 24}}, "55": {"start": {"line": 155, "column": 23}, "end": {"line": 195, "column": 3}}, "56": {"start": {"line": 156, "column": 4}, "end": {"line": 159, "column": 5}}, "57": {"start": {"line": 157, "column": 6}, "end": {"line": 157, "column": 51}}, "58": {"start": {"line": 158, "column": 6}, "end": {"line": 158, "column": 13}}, "59": {"start": {"line": 161, "column": 4}, "end": {"line": 194, "column": 6}}, "60": {"start": {"line": 173, "column": 12}, "end": {"line": 190, "column": 13}}, "61": {"start": {"line": 174, "column": 14}, "end": {"line": 174, "column": 32}}, "62": {"start": {"line": 175, "column": 14}, "end": {"line": 175, "column": 49}}, "63": {"start": {"line": 177, "column": 14}, "end": {"line": 184, "column": 17}}, "64": {"start": {"line": 181, "column": 20}, "end": {"line": 181, "column": 34}}, "65": {"start": {"line": 186, "column": 14}, "end": {"line": 186, "column": 60}}, "66": {"start": {"line": 187, "column": 14}, "end": {"line": 187, "column": 78}}, "67": {"start": {"line": 189, "column": 14}, "end": {"line": 189, "column": 33}}, "68": {"start": {"line": 197, "column": 21}, "end": {"line": 199, "column": 3}}, "69": {"start": {"line": 198, "column": 4}, "end": {"line": 198, "column": 18}}, "70": {"start": {"line": 201, "column": 2}, "end": {"line": 211, "column": 3}}, "71": {"start": {"line": 202, "column": 4}, "end": {"line": 210, "column": 6}}, "72": {"start": {"line": 213, "column": 2}, "end": {"line": 308, "column": 4}}, "73": {"start": {"line": 313, "column": 15}, "end": {"line": 421, "column": 2}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 26, "column": 22}, "end": {"line": 26, "column": 23}}, "loc": {"start": {"line": 27, "column": 2}, "end": {"line": 38, "column": 8}}, "line": 27}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 41, "column": 18}, "end": {"line": 41, "column": 19}}, "loc": {"start": {"line": 42, "column": 2}, "end": {"line": 53, "column": 8}}, "line": 42}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 58, "column": 23}, "end": {"line": 58, "column": 24}}, "loc": {"start": {"line": 58, "column": 29}, "end": {"line": 309, "column": 1}}, "line": 58}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 68, "column": 32}, "end": {"line": 68, "column": 33}}, "loc": {"start": {"line": 68, "column": 44}, "end": {"line": 85, "column": 3}}, "line": 68}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 87, "column": 12}, "end": {"line": 87, "column": 13}}, "loc": {"start": {"line": 87, "column": 18}, "end": {"line": 89, "column": 3}}, "line": 87}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 92, "column": 29}, "end": {"line": 92, "column": 30}}, "loc": {"start": {"line": 92, "column": 35}, "end": {"line": 94, "column": 3}}, "line": 92}, "6": {"name": "(anonymous_6)", "decl": {"start": {"line": 97, "column": 18}, "end": {"line": 97, "column": 19}}, "loc": {"start": {"line": 97, "column": 24}, "end": {"line": 100, "column": 3}}, "line": 97}, "7": {"name": "(anonymous_7)", "decl": {"start": {"line": 103, "column": 27}, "end": {"line": 103, "column": 28}}, "loc": {"start": {"line": 103, "column": 45}, "end": {"line": 109, "column": 3}}, "line": 103}, "8": {"name": "(anonymous_8)", "decl": {"start": {"line": 112, "column": 34}, "end": {"line": 112, "column": 35}}, "loc": {"start": {"line": 112, "column": 52}, "end": {"line": 118, "column": 3}}, "line": 112}, "9": {"name": "(anonymous_9)", "decl": {"start": {"line": 120, "column": 23}, "end": {"line": 120, "column": 24}}, "loc": {"start": {"line": 120, "column": 35}, "end": {"line": 153, "column": 3}}, "line": 120}, "10": {"name": "(anonymous_10)", "decl": {"start": {"line": 142, "column": 19}, "end": {"line": 142, "column": 20}}, "loc": {"start": {"line": 142, "column": 31}, "end": {"line": 144, "column": 11}}, "line": 142}, "11": {"name": "(anonymous_11)", "decl": {"start": {"line": 155, "column": 23}, "end": {"line": 155, "column": 24}}, "loc": {"start": {"line": 155, "column": 35}, "end": {"line": 195, "column": 3}}, "line": 155}, "12": {"name": "(anonymous_12)", "decl": {"start": {"line": 172, "column": 19}, "end": {"line": 172, "column": 20}}, "loc": {"start": {"line": 172, "column": 31}, "end": {"line": 191, "column": 11}}, "line": 172}, "13": {"name": "(anonymous_13)", "decl": {"start": {"line": 180, "column": 27}, "end": {"line": 180, "column": 28}}, "loc": {"start": {"line": 180, "column": 39}, "end": {"line": 182, "column": 19}}, "line": 180}, "14": {"name": "(anonymous_14)", "decl": {"start": {"line": 197, "column": 21}, "end": {"line": 197, "column": 22}}, "loc": {"start": {"line": 197, "column": 33}, "end": {"line": 199, "column": 3}}, "line": 197}}, "branchMap": {"0": {"loc": {"start": {"line": 26, "column": 25}, "end": {"line": 26, "column": 42}}, "type": "default-arg", "locations": [{"start": {"line": 26, "column": 33}, "end": {"line": 26, "column": 42}}], "line": 26}, "1": {"loc": {"start": {"line": 26, "column": 44}, "end": {"line": 26, "column": 53}}, "type": "default-arg", "locations": [{"start": {"line": 26, "column": 51}, "end": {"line": 26, "column": 53}}], "line": 26}, "2": {"loc": {"start": {"line": 41, "column": 21}, "end": {"line": 41, "column": 38}}, "type": "default-arg", "locations": [{"start": {"line": 41, "column": 29}, "end": {"line": 41, "column": 38}}], "line": 41}, "3": {"loc": {"start": {"line": 41, "column": 40}, "end": {"line": 41, "column": 49}}, "type": "default-arg", "locations": [{"start": {"line": 41, "column": 47}, "end": {"line": 41, "column": 49}}], "line": 41}, "4": {"loc": {"start": {"line": 69, "column": 4}, "end": {"line": 72, "column": 5}}, "type": "if", "locations": [{"start": {"line": 69, "column": 4}, "end": {"line": 72, "column": 5}}, {"start": {}, "end": {}}], "line": 69}, "5": {"loc": {"start": {"line": 69, "column": 8}, "end": {"line": 69, "column": 37}}, "type": "binary-expr", "locations": [{"start": {"line": 69, "column": 8}, "end": {"line": 69, "column": 11}}, {"start": {"line": 69, "column": 15}, "end": {"line": 69, "column": 37}}], "line": 69}, "6": {"loc": {"start": {"line": 78, "column": 21}, "end": {"line": 78, "column": 44}}, "type": "binary-expr", "locations": [{"start": {"line": 78, "column": 21}, "end": {"line": 78, "column": 38}}, {"start": {"line": 78, "column": 42}, "end": {"line": 78, "column": 44}}], "line": 78}, "7": {"loc": {"start": {"line": 81, "column": 25}, "end": {"line": 81, "column": 73}}, "type": "binary-expr", "locations": [{"start": {"line": 81, "column": 25}, "end": {"line": 81, "column": 47}}, {"start": {"line": 81, "column": 51}, "end": {"line": 81, "column": 73}}], "line": 81}, "8": {"loc": {"start": {"line": 98, "column": 17}, "end": {"line": 98, "column": 44}}, "type": "binary-expr", "locations": [{"start": {"line": 98, "column": 17}, "end": {"line": 98, "column": 38}}, {"start": {"line": 98, "column": 42}, "end": {"line": 98, "column": 44}}], "line": 98}, "9": {"loc": {"start": {"line": 99, "column": 24}, "end": {"line": 99, "column": 58}}, "type": "binary-expr", "locations": [{"start": {"line": 99, "column": 24}, "end": {"line": 99, "column": 52}}, {"start": {"line": 99, "column": 56}, "end": {"line": 99, "column": 58}}], "line": 99}, "10": {"loc": {"start": {"line": 106, "column": 4}, "end": {"line": 108, "column": 5}}, "type": "if", "locations": [{"start": {"line": 106, "column": 4}, "end": {"line": 108, "column": 5}}, {"start": {}, "end": {}}], "line": 106}, "11": {"loc": {"start": {"line": 115, "column": 4}, "end": {"line": 117, "column": 5}}, "type": "if", "locations": [{"start": {"line": 115, "column": 4}, "end": {"line": 117, "column": 5}}, {"start": {}, "end": {}}], "line": 115}, "12": {"loc": {"start": {"line": 122, "column": 4}, "end": {"line": 125, "column": 5}}, "type": "if", "locations": [{"start": {"line": 122, "column": 4}, "end": {"line": 125, "column": 5}}, {"start": {}, "end": {}}], "line": 122}, "13": {"loc": {"start": {"line": 127, "column": 4}, "end": {"line": 130, "column": 5}}, "type": "if", "locations": [{"start": {"line": 127, "column": 4}, "end": {"line": 130, "column": 5}}, {"start": {}, "end": {}}], "line": 127}, "14": {"loc": {"start": {"line": 127, "column": 8}, "end": {"line": 127, "column": 37}}, "type": "binary-expr", "locations": [{"start": {"line": 127, "column": 8}, "end": {"line": 127, "column": 11}}, {"start": {"line": 127, "column": 15}, "end": {"line": 127, "column": 37}}], "line": 127}, "15": {"loc": {"start": {"line": 149, "column": 25}, "end": {"line": 149, "column": 76}}, "type": "binary-expr", "locations": [{"start": {"line": 149, "column": 25}, "end": {"line": 149, "column": 47}}, {"start": {"line": 149, "column": 51}, "end": {"line": 149, "column": 76}}], "line": 149}, "16": {"loc": {"start": {"line": 156, "column": 4}, "end": {"line": 159, "column": 5}}, "type": "if", "locations": [{"start": {"line": 156, "column": 4}, "end": {"line": 159, "column": 5}}, {"start": {}, "end": {}}], "line": 156}, "17": {"loc": {"start": {"line": 156, "column": 8}, "end": {"line": 156, "column": 37}}, "type": "binary-expr", "locations": [{"start": {"line": 156, "column": 8}, "end": {"line": 156, "column": 11}}, {"start": {"line": 156, "column": 15}, "end": {"line": 156, "column": 37}}], "line": 156}, "18": {"loc": {"start": {"line": 187, "column": 33}, "end": {"line": 187, "column": 76}}, "type": "binary-expr", "locations": [{"start": {"line": 187, "column": 33}, "end": {"line": 187, "column": 55}}, {"start": {"line": 187, "column": 59}, "end": {"line": 187, "column": 76}}], "line": 187}, "19": {"loc": {"start": {"line": 201, "column": 2}, "end": {"line": 211, "column": 3}}, "type": "if", "locations": [{"start": {"line": 201, "column": 2}, "end": {"line": 211, "column": 3}}, {"start": {}, "end": {}}], "line": 201}, "20": {"loc": {"start": {"line": 228, "column": 13}, "end": {"line": 232, "column": 13}}, "type": "cond-expr", "locations": [{"start": {"line": 229, "column": 14}, "end": {"line": 229, "column": 64}}, {"start": {"line": 231, "column": 14}, "end": {"line": 231, "column": 27}}], "line": 228}, "21": {"loc": {"start": {"line": 249, "column": 36}, "end": {"line": 249, "column": 72}}, "type": "cond-expr", "locations": [{"start": {"line": 249, "column": 48}, "end": {"line": 249, "column": 65}}, {"start": {"line": 249, "column": 68}, "end": {"line": 249, "column": 72}}], "line": 249}, "22": {"loc": {"start": {"line": 254, "column": 24}, "end": {"line": 254, "column": 45}}, "type": "binary-expr", "locations": [{"start": {"line": 254, "column": 24}, "end": {"line": 254, "column": 32}}, {"start": {"line": 254, "column": 36}, "end": {"line": 254, "column": 45}}], "line": 254}, "23": {"loc": {"start": {"line": 257, "column": 13}, "end": {"line": 259, "column": 20}}, "type": "cond-expr", "locations": [{"start": {"line": 258, "column": 14}, "end": {"line": 258, "column": 63}}, {"start": {"line": 259, "column": 16}, "end": {"line": 259, "column": 20}}], "line": 257}, "24": {"loc": {"start": {"line": 273, "column": 16}, "end": {"line": 273, "column": 59}}, "type": "cond-expr", "locations": [{"start": {"line": 273, "column": 35}, "end": {"line": 273, "column": 52}}, {"start": {"line": 273, "column": 55}, "end": {"line": 273, "column": 59}}], "line": 273}, "25": {"loc": {"start": {"line": 282, "column": 24}, "end": {"line": 282, "column": 45}}, "type": "binary-expr", "locations": [{"start": {"line": 282, "column": 24}, "end": {"line": 282, "column": 32}}, {"start": {"line": 282, "column": 36}, "end": {"line": 282, "column": 45}}], "line": 282}, "26": {"loc": {"start": {"line": 285, "column": 13}, "end": {"line": 287, "column": 20}}, "type": "cond-expr", "locations": [{"start": {"line": 286, "column": 14}, "end": {"line": 286, "column": 70}}, {"start": {"line": 287, "column": 16}, "end": {"line": 287, "column": 20}}], "line": 285}, "27": {"loc": {"start": {"line": 293, "column": 14}, "end": {"line": 294, "column": 43}}, "type": "binary-expr", "locations": [{"start": {"line": 293, "column": 15}, "end": {"line": 293, "column": 22}}, {"start": {"line": 293, "column": 26}, "end": {"line": 293, "column": 34}}, {"start": {"line": 293, "column": 38}, "end": {"line": 293, "column": 61}}, {"start": {"line": 294, "column": 16}, "end": {"line": 294, "column": 43}}], "line": 293}, "28": {"loc": {"start": {"line": 297, "column": 22}, "end": {"line": 297, "column": 68}}, "type": "binary-expr", "locations": [{"start": {"line": 297, "column": 22}, "end": {"line": 297, "column": 29}}, {"start": {"line": 297, "column": 33}, "end": {"line": 297, "column": 41}}, {"start": {"line": 297, "column": 45}, "end": {"line": 297, "column": 68}}], "line": 297}, "29": {"loc": {"start": {"line": 299, "column": 13}, "end": {"line": 303, "column": 13}}, "type": "cond-expr", "locations": [{"start": {"line": 300, "column": 14}, "end": {"line": 300, "column": 64}}, {"start": {"line": 302, "column": 14}, "end": {"line": 302, "column": 70}}], "line": 299}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0, "50": 0, "51": 0, "52": 0, "53": 0, "54": 0, "55": 0, "56": 0, "57": 0, "58": 0, "59": 0, "60": 0, "61": 0, "62": 0, "63": 0, "64": 0, "65": 0, "66": 0, "67": 0, "68": 0, "69": 0, "70": 0, "71": 0, "72": 0, "73": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0}, "b": {"0": [0], "1": [0], "2": [0], "3": [0], "4": [0, 0], "5": [0, 0], "6": [0, 0], "7": [0, 0], "8": [0, 0], "9": [0, 0], "10": [0, 0], "11": [0, 0], "12": [0, 0], "13": [0, 0], "14": [0, 0], "15": [0, 0], "16": [0, 0], "17": [0, 0], "18": [0, 0], "19": [0, 0], "20": [0, 0], "21": [0, 0], "22": [0, 0], "23": [0, 0], "24": [0, 0], "25": [0, 0], "26": [0, 0], "27": [0, 0, 0, 0], "28": [0, 0, 0], "29": [0, 0]}}, "/home/<USER>/projects/azmoon-client/app/screens/ExamsList.tsx": {"path": "/home/<USER>/projects/azmoon-client/app/screens/ExamsList.tsx", "statementMap": {"0": {"start": {"line": 19, "column": 22}, "end": {"line": 32, "column": 1}}, "1": {"start": {"line": 20, "column": 2}, "end": {"line": 31, "column": 8}}, "2": {"start": {"line": 34, "column": 26}, "end": {"line": 47, "column": 1}}, "3": {"start": {"line": 35, "column": 2}, "end": {"line": 46, "column": 8}}, "4": {"start": {"line": 49, "column": 17}, "end": {"line": 62, "column": 1}}, "5": {"start": {"line": 50, "column": 2}, "end": {"line": 61, "column": 8}}, "6": {"start": {"line": 66, "column": 21}, "end": {"line": 199, "column": 1}}, "7": {"start": {"line": 67, "column": 30}, "end": {"line": 67, "column": 51}}, "8": {"start": {"line": 68, "column": 32}, "end": {"line": 68, "column": 46}}, "9": {"start": {"line": 69, "column": 38}, "end": {"line": 69, "column": 53}}, "10": {"start": {"line": 70, "column": 28}, "end": {"line": 70, "column": 57}}, "11": {"start": {"line": 72, "column": 2}, "end": {"line": 74, "column": 9}}, "12": {"start": {"line": 73, "column": 4}, "end": {"line": 73, "column": 17}}, "13": {"start": {"line": 76, "column": 21}, "end": {"line": 88, "column": 3}}, "14": {"start": {"line": 77, "column": 4}, "end": {"line": 87, "column": 5}}, "15": {"start": {"line": 78, "column": 6}, "end": {"line": 78, "column": 23}}, "16": {"start": {"line": 79, "column": 6}, "end": {"line": 79, "column": 21}}, "17": {"start": {"line": 80, "column": 19}, "end": {"line": 80, "column": 49}}, "18": {"start": {"line": 81, "column": 6}, "end": {"line": 81, "column": 22}}, "19": {"start": {"line": 83, "column": 6}, "end": {"line": 83, "column": 51}}, "20": {"start": {"line": 84, "column": 6}, "end": {"line": 84, "column": 66}}, "21": {"start": {"line": 86, "column": 6}, "end": {"line": 86, "column": 24}}, "22": {"start": {"line": 90, "column": 24}, "end": {"line": 102, "column": 3}}, "23": {"start": {"line": 91, "column": 4}, "end": {"line": 101, "column": 5}}, "24": {"start": {"line": 92, "column": 6}, "end": {"line": 92, "column": 26}}, "25": {"start": {"line": 93, "column": 6}, "end": {"line": 93, "column": 21}}, "26": {"start": {"line": 94, "column": 19}, "end": {"line": 94, "column": 49}}, "27": {"start": {"line": 95, "column": 6}, "end": {"line": 95, "column": 22}}, "28": {"start": {"line": 97, "column": 6}, "end": {"line": 97, "column": 54}}, "29": {"start": {"line": 98, "column": 6}, "end": {"line": 98, "column": 69}}, "30": {"start": {"line": 100, "column": 6}, "end": {"line": 100, "column": 27}}, "31": {"start": {"line": 104, "column": 27}, "end": {"line": 107, "column": 3}}, "32": {"start": {"line": 105, "column": 23}, "end": {"line": 105, "column": 50}}, "33": {"start": {"line": 106, "column": 4}, "end": {"line": 106, "column": 43}}, "34": {"start": {"line": 109, "column": 26}, "end": {"line": 123, "column": 3}}, "35": {"start": {"line": 110, "column": 4}, "end": {"line": 122, "column": 23}}, "36": {"start": {"line": 112, "column": 21}, "end": {"line": 112, "column": 46}}, "37": {"start": {"line": 125, "column": 24}, "end": {"line": 169, "column": 3}}, "38": {"start": {"line": 126, "column": 4}, "end": {"line": 132, "column": 5}}, "39": {"start": {"line": 127, "column": 6}, "end": {"line": 131, "column": 8}}, "40": {"start": {"line": 134, "column": 4}, "end": {"line": 143, "column": 5}}, "41": {"start": {"line": 135, "column": 6}, "end": {"line": 142, "column": 8}}, "42": {"start": {"line": 145, "column": 4}, "end": {"line": 168, "column": 6}}, "43": {"start": {"line": 149, "column": 39}, "end": {"line": 149, "column": 46}}, "44": {"start": {"line": 151, "column": 10}, "end": {"line": 151, "column": 66}}, "45": {"start": {"line": 155, "column": 10}, "end": {"line": 157, "column": 17}}, "46": {"start": {"line": 171, "column": 2}, "end": {"line": 198, "column": 4}}, "47": {"start": {"line": 190, "column": 31}, "end": {"line": 190, "column": 58}}, "48": {"start": {"line": 191, "column": 12}, "end": {"line": 191, "column": 36}}, "49": {"start": {"line": 203, "column": 15}, "end": {"line": 317, "column": 2}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 19, "column": 22}, "end": {"line": 19, "column": 23}}, "loc": {"start": {"line": 20, "column": 2}, "end": {"line": 31, "column": 8}}, "line": 20}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 34, "column": 26}, "end": {"line": 34, "column": 27}}, "loc": {"start": {"line": 35, "column": 2}, "end": {"line": 46, "column": 8}}, "line": 35}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 49, "column": 17}, "end": {"line": 49, "column": 18}}, "loc": {"start": {"line": 50, "column": 2}, "end": {"line": 61, "column": 8}}, "line": 50}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 66, "column": 21}, "end": {"line": 66, "column": 22}}, "loc": {"start": {"line": 66, "column": 27}, "end": {"line": 199, "column": 1}}, "line": 66}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 72, "column": 12}, "end": {"line": 72, "column": 13}}, "loc": {"start": {"line": 72, "column": 18}, "end": {"line": 74, "column": 3}}, "line": 72}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 76, "column": 21}, "end": {"line": 76, "column": 22}}, "loc": {"start": {"line": 76, "column": 33}, "end": {"line": 88, "column": 3}}, "line": 76}, "6": {"name": "(anonymous_6)", "decl": {"start": {"line": 90, "column": 24}, "end": {"line": 90, "column": 25}}, "loc": {"start": {"line": 90, "column": 36}, "end": {"line": 102, "column": 3}}, "line": 90}, "7": {"name": "(anonymous_7)", "decl": {"start": {"line": 104, "column": 27}, "end": {"line": 104, "column": 28}}, "loc": {"start": {"line": 104, "column": 54}, "end": {"line": 107, "column": 3}}, "line": 104}, "8": {"name": "(anonymous_8)", "decl": {"start": {"line": 109, "column": 26}, "end": {"line": 109, "column": 27}}, "loc": {"start": {"line": 110, "column": 4}, "end": {"line": 122, "column": 23}}, "line": 110}, "9": {"name": "(anonymous_9)", "decl": {"start": {"line": 112, "column": 15}, "end": {"line": 112, "column": 16}}, "loc": {"start": {"line": 112, "column": 21}, "end": {"line": 112, "column": 46}}, "line": 112}, "10": {"name": "(anonymous_10)", "decl": {"start": {"line": 125, "column": 24}, "end": {"line": 125, "column": 25}}, "loc": {"start": {"line": 125, "column": 30}, "end": {"line": 169, "column": 3}}, "line": 125}, "11": {"name": "(anonymous_11)", "decl": {"start": {"line": 149, "column": 22}, "end": {"line": 149, "column": 23}}, "loc": {"start": {"line": 149, "column": 39}, "end": {"line": 149, "column": 46}}, "line": 149}, "12": {"name": "(anonymous_12)", "decl": {"start": {"line": 150, "column": 29}, "end": {"line": 150, "column": 30}}, "loc": {"start": {"line": 151, "column": 10}, "end": {"line": 151, "column": 66}}, "line": 151}, "13": {"name": "(anonymous_13)", "decl": {"start": {"line": 154, "column": 28}, "end": {"line": 154, "column": 29}}, "loc": {"start": {"line": 155, "column": 10}, "end": {"line": 157, "column": 17}}, "line": 155}, "14": {"name": "(anonymous_14)", "decl": {"start": {"line": 189, "column": 19}, "end": {"line": 189, "column": 20}}, "loc": {"start": {"line": 189, "column": 31}, "end": {"line": 192, "column": 11}}, "line": 189}}, "branchMap": {"0": {"loc": {"start": {"line": 19, "column": 25}, "end": {"line": 19, "column": 42}}, "type": "default-arg", "locations": [{"start": {"line": 19, "column": 33}, "end": {"line": 19, "column": 42}}], "line": 19}, "1": {"loc": {"start": {"line": 19, "column": 44}, "end": {"line": 19, "column": 53}}, "type": "default-arg", "locations": [{"start": {"line": 19, "column": 51}, "end": {"line": 19, "column": 53}}], "line": 19}, "2": {"loc": {"start": {"line": 34, "column": 29}, "end": {"line": 34, "column": 46}}, "type": "default-arg", "locations": [{"start": {"line": 34, "column": 37}, "end": {"line": 34, "column": 46}}], "line": 34}, "3": {"loc": {"start": {"line": 34, "column": 48}, "end": {"line": 34, "column": 57}}, "type": "default-arg", "locations": [{"start": {"line": 34, "column": 55}, "end": {"line": 34, "column": 57}}], "line": 34}, "4": {"loc": {"start": {"line": 49, "column": 20}, "end": {"line": 49, "column": 37}}, "type": "default-arg", "locations": [{"start": {"line": 49, "column": 28}, "end": {"line": 49, "column": 37}}], "line": 49}, "5": {"loc": {"start": {"line": 49, "column": 39}, "end": {"line": 49, "column": 48}}, "type": "default-arg", "locations": [{"start": {"line": 49, "column": 46}, "end": {"line": 49, "column": 48}}], "line": 49}, "6": {"loc": {"start": {"line": 84, "column": 15}, "end": {"line": 84, "column": 64}}, "type": "binary-expr", "locations": [{"start": {"line": 84, "column": 15}, "end": {"line": 84, "column": 37}}, {"start": {"line": 84, "column": 41}, "end": {"line": 84, "column": 64}}], "line": 84}, "7": {"loc": {"start": {"line": 98, "column": 15}, "end": {"line": 98, "column": 67}}, "type": "binary-expr", "locations": [{"start": {"line": 98, "column": 15}, "end": {"line": 98, "column": 37}}, {"start": {"line": 98, "column": 41}, "end": {"line": 98, "column": 67}}], "line": 98}, "8": {"loc": {"start": {"line": 119, "column": 44}, "end": {"line": 119, "column": 73}}, "type": "binary-expr", "locations": [{"start": {"line": 119, "column": 44}, "end": {"line": 119, "column": 60}}, {"start": {"line": 119, "column": 64}, "end": {"line": 119, "column": 73}}], "line": 119}, "9": {"loc": {"start": {"line": 126, "column": 4}, "end": {"line": 132, "column": 5}}, "type": "if", "locations": [{"start": {"line": 126, "column": 4}, "end": {"line": 132, "column": 5}}, {"start": {}, "end": {}}], "line": 126}, "10": {"loc": {"start": {"line": 134, "column": 4}, "end": {"line": 143, "column": 5}}, "type": "if", "locations": [{"start": {"line": 134, "column": 4}, "end": {"line": 143, "column": 5}}, {"start": {}, "end": {}}], "line": 134}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0}, "b": {"0": [0], "1": [0], "2": [0], "3": [0], "4": [0], "5": [0], "6": [0, 0], "7": [0, 0], "8": [0, 0], "9": [0, 0], "10": [0, 0]}}, "/home/<USER>/projects/azmoon-client/app/screens/Explore.tsx": {"path": "/home/<USER>/projects/azmoon-client/app/screens/Explore.tsx", "statementMap": {"0": {"start": {"line": 12, "column": 2}, "end": {"line": 96, "column": 4}}, "1": {"start": {"line": 99, "column": 15}, "end": {"line": 110, "column": 2}}}, "fnMap": {"0": {"name": "ExploreScreen", "decl": {"start": {"line": 11, "column": 24}, "end": {"line": 11, "column": 37}}, "loc": {"start": {"line": 11, "column": 40}, "end": {"line": 97, "column": 1}}, "line": 11}}, "branchMap": {}, "s": {"0": 0, "1": 0}, "f": {"0": 0}, "b": {}}, "/home/<USER>/projects/azmoon-client/app/screens/Home.tsx": {"path": "/home/<USER>/projects/azmoon-client/app/screens/Home.tsx", "statementMap": {"0": {"start": {"line": 17, "column": 17}, "end": {"line": 17, "column": 28}}, "1": {"start": {"line": 18, "column": 2}, "end": {"line": 72, "column": 4}}, "2": {"start": {"line": 36, "column": 25}, "end": {"line": 36, "column": 48}}, "3": {"start": {"line": 75, "column": 15}, "end": {"line": 93, "column": 2}}}, "fnMap": {"0": {"name": "HomeScreen", "decl": {"start": {"line": 16, "column": 24}, "end": {"line": 16, "column": 34}}, "loc": {"start": {"line": 16, "column": 37}, "end": {"line": 73, "column": 1}}, "line": 16}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 36, "column": 19}, "end": {"line": 36, "column": 20}}, "loc": {"start": {"line": 36, "column": 25}, "end": {"line": 36, "column": 48}}, "line": 36}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "/home/<USER>/projects/azmoon-client/app/screens/MajorCourseAdd.tsx": {"path": "/home/<USER>/projects/azmoon-client/app/screens/MajorCourseAdd.tsx", "statementMap": {"0": {"start": {"line": 9, "column": 32}, "end": {"line": 9, "column": 44}}, "1": {"start": {"line": 10, "column": 34}, "end": {"line": 10, "column": 46}}, "2": {"start": {"line": 11, "column": 32}, "end": {"line": 11, "column": 47}}, "3": {"start": {"line": 13, "column": 31}, "end": {"line": 30, "column": 3}}, "4": {"start": {"line": 14, "column": 4}, "end": {"line": 17, "column": 5}}, "5": {"start": {"line": 15, "column": 6}, "end": {"line": 15, "column": 81}}, "6": {"start": {"line": 16, "column": 6}, "end": {"line": 16, "column": 13}}, "7": {"start": {"line": 19, "column": 4}, "end": {"line": 19, "column": 21}}, "8": {"start": {"line": 20, "column": 4}, "end": {"line": 29, "column": 5}}, "9": {"start": {"line": 21, "column": 6}, "end": {"line": 21, "column": 72}}, "10": {"start": {"line": 22, "column": 6}, "end": {"line": 22, "column": 74}}, "11": {"start": {"line": 23, "column": 6}, "end": {"line": 23, "column": 20}}, "12": {"start": {"line": 25, "column": 6}, "end": {"line": 25, "column": 85}}, "13": {"start": {"line": 26, "column": 6}, "end": {"line": 26, "column": 67}}, "14": {"start": {"line": 28, "column": 6}, "end": {"line": 28, "column": 24}}, "15": {"start": {"line": 32, "column": 2}, "end": {"line": 59, "column": 4}}, "16": {"start": {"line": 62, "column": 15}, "end": {"line": 97, "column": 2}}}, "fnMap": {"0": {"name": "MajorCourseAddScreen", "decl": {"start": {"line": 8, "column": 24}, "end": {"line": 8, "column": 44}}, "loc": {"start": {"line": 8, "column": 47}, "end": {"line": 60, "column": 1}}, "line": 8}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 13, "column": 31}, "end": {"line": 13, "column": 32}}, "loc": {"start": {"line": 13, "column": 43}, "end": {"line": 30, "column": 3}}, "line": 13}}, "branchMap": {"0": {"loc": {"start": {"line": 14, "column": 4}, "end": {"line": 17, "column": 5}}, "type": "if", "locations": [{"start": {"line": 14, "column": 4}, "end": {"line": 17, "column": 5}}, {"start": {}, "end": {}}], "line": 14}, "1": {"loc": {"start": {"line": 14, "column": 8}, "end": {"line": 14, "column": 43}}, "type": "binary-expr", "locations": [{"start": {"line": 14, "column": 8}, "end": {"line": 14, "column": 23}}, {"start": {"line": 14, "column": 27}, "end": {"line": 14, "column": 43}}], "line": 14}, "2": {"loc": {"start": {"line": 50, "column": 31}, "end": {"line": 50, "column": 63}}, "type": "binary-expr", "locations": [{"start": {"line": 50, "column": 31}, "end": {"line": 50, "column": 38}}, {"start": {"line": 50, "column": 42}, "end": {"line": 50, "column": 63}}], "line": 50}, "3": {"loc": {"start": {"line": 55, "column": 11}, "end": {"line": 55, "column": 49}}, "type": "cond-expr", "locations": [{"start": {"line": 55, "column": 21}, "end": {"line": 55, "column": 32}}, {"start": {"line": 55, "column": 35}, "end": {"line": 55, "column": 49}}], "line": 55}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0}, "f": {"0": 0, "1": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0], "3": [0, 0]}}, "/home/<USER>/projects/azmoon-client/app/screens/MajorCourseDetails.tsx": {"path": "/home/<USER>/projects/azmoon-client/app/screens/MajorCourseDetails.tsx", "statementMap": {"0": {"start": {"line": 9, "column": 32}, "end": {"line": 9, "column": 54}}, "1": {"start": {"line": 10, "column": 40}, "end": {"line": 10, "column": 74}}, "2": {"start": {"line": 11, "column": 32}, "end": {"line": 11, "column": 46}}, "3": {"start": {"line": 12, "column": 28}, "end": {"line": 12, "column": 57}}, "4": {"start": {"line": 14, "column": 2}, "end": {"line": 32, "column": 26}}, "5": {"start": {"line": 15, "column": 4}, "end": {"line": 31, "column": 5}}, "6": {"start": {"line": 16, "column": 31}, "end": {"line": 26, "column": 7}}, "7": {"start": {"line": 17, "column": 8}, "end": {"line": 25, "column": 9}}, "8": {"start": {"line": 18, "column": 23}, "end": {"line": 18, "column": 101}}, "9": {"start": {"line": 19, "column": 10}, "end": {"line": 19, "column": 31}}, "10": {"start": {"line": 21, "column": 10}, "end": {"line": 21, "column": 60}}, "11": {"start": {"line": 22, "column": 10}, "end": {"line": 22, "column": 29}}, "12": {"start": {"line": 24, "column": 10}, "end": {"line": 24, "column": 28}}, "13": {"start": {"line": 27, "column": 6}, "end": {"line": 27, "column": 25}}, "14": {"start": {"line": 29, "column": 6}, "end": {"line": 29, "column": 54}}, "15": {"start": {"line": 30, "column": 6}, "end": {"line": 30, "column": 24}}, "16": {"start": {"line": 34, "column": 2}, "end": {"line": 41, "column": 3}}, "17": {"start": {"line": 35, "column": 4}, "end": {"line": 40, "column": 6}}, "18": {"start": {"line": 43, "column": 2}, "end": {"line": 49, "column": 3}}, "19": {"start": {"line": 44, "column": 4}, "end": {"line": 48, "column": 6}}, "20": {"start": {"line": 51, "column": 2}, "end": {"line": 57, "column": 3}}, "21": {"start": {"line": 52, "column": 4}, "end": {"line": 56, "column": 6}}, "22": {"start": {"line": 59, "column": 2}, "end": {"line": 77, "column": 4}}, "23": {"start": {"line": 80, "column": 15}, "end": {"line": 107, "column": 2}}}, "fnMap": {"0": {"name": "MajorCourseDetailsScreen", "decl": {"start": {"line": 8, "column": 24}, "end": {"line": 8, "column": 48}}, "loc": {"start": {"line": 8, "column": 51}, "end": {"line": 78, "column": 1}}, "line": 8}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 14, "column": 12}, "end": {"line": 14, "column": 13}}, "loc": {"start": {"line": 14, "column": 18}, "end": {"line": 32, "column": 3}}, "line": 14}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 16, "column": 31}, "end": {"line": 16, "column": 32}}, "loc": {"start": {"line": 16, "column": 43}, "end": {"line": 26, "column": 7}}, "line": 16}}, "branchMap": {"0": {"loc": {"start": {"line": 15, "column": 4}, "end": {"line": 31, "column": 5}}, "type": "if", "locations": [{"start": {"line": 15, "column": 4}, "end": {"line": 31, "column": 5}}, {"start": {"line": 28, "column": 11}, "end": {"line": 31, "column": 5}}], "line": 15}, "1": {"loc": {"start": {"line": 15, "column": 8}, "end": {"line": 15, "column": 27}}, "type": "binary-expr", "locations": [{"start": {"line": 15, "column": 8}, "end": {"line": 15, "column": 15}}, {"start": {"line": 15, "column": 19}, "end": {"line": 15, "column": 27}}], "line": 15}, "2": {"loc": {"start": {"line": 34, "column": 2}, "end": {"line": 41, "column": 3}}, "type": "if", "locations": [{"start": {"line": 34, "column": 2}, "end": {"line": 41, "column": 3}}, {"start": {}, "end": {}}], "line": 34}, "3": {"loc": {"start": {"line": 43, "column": 2}, "end": {"line": 49, "column": 3}}, "type": "if", "locations": [{"start": {"line": 43, "column": 2}, "end": {"line": 49, "column": 3}}, {"start": {}, "end": {}}], "line": 43}, "4": {"loc": {"start": {"line": 51, "column": 2}, "end": {"line": 57, "column": 3}}, "type": "if", "locations": [{"start": {"line": 51, "column": 2}, "end": {"line": 57, "column": 3}}, {"start": {}, "end": {}}], "line": 51}, "5": {"loc": {"start": {"line": 70, "column": 7}, "end": {"line": 75, "column": 7}}, "type": "binary-expr", "locations": [{"start": {"line": 70, "column": 7}, "end": {"line": 70, "column": 28}}, {"start": {"line": 71, "column": 8}, "end": {"line": 74, "column": 21}}], "line": 70}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0}, "f": {"0": 0, "1": 0, "2": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0], "3": [0, 0], "4": [0, 0], "5": [0, 0]}}, "/home/<USER>/projects/azmoon-client/app/screens/MajorCourseEdit.tsx": {"path": "/home/<USER>/projects/azmoon-client/app/screens/MajorCourseEdit.tsx", "statementMap": {"0": {"start": {"line": 9, "column": 32}, "end": {"line": 9, "column": 54}}, "1": {"start": {"line": 10, "column": 46}, "end": {"line": 10, "column": 58}}, "2": {"start": {"line": 11, "column": 48}, "end": {"line": 11, "column": 60}}, "3": {"start": {"line": 12, "column": 32}, "end": {"line": 12, "column": 46}}, "4": {"start": {"line": 13, "column": 30}, "end": {"line": 13, "column": 45}}, "5": {"start": {"line": 14, "column": 28}, "end": {"line": 14, "column": 57}}, "6": {"start": {"line": 16, "column": 2}, "end": {"line": 35, "column": 26}}, "7": {"start": {"line": 17, "column": 4}, "end": {"line": 34, "column": 5}}, "8": {"start": {"line": 18, "column": 31}, "end": {"line": 29, "column": 7}}, "9": {"start": {"line": 19, "column": 8}, "end": {"line": 28, "column": 9}}, "10": {"start": {"line": 20, "column": 23}, "end": {"line": 20, "column": 101}}, "11": {"start": {"line": 21, "column": 10}, "end": {"line": 21, "column": 42}}, "12": {"start": {"line": 22, "column": 10}, "end": {"line": 22, "column": 44}}, "13": {"start": {"line": 24, "column": 10}, "end": {"line": 24, "column": 72}}, "14": {"start": {"line": 25, "column": 10}, "end": {"line": 25, "column": 29}}, "15": {"start": {"line": 27, "column": 10}, "end": {"line": 27, "column": 28}}, "16": {"start": {"line": 30, "column": 6}, "end": {"line": 30, "column": 25}}, "17": {"start": {"line": 32, "column": 6}, "end": {"line": 32, "column": 66}}, "18": {"start": {"line": 33, "column": 6}, "end": {"line": 33, "column": 24}}, "19": {"start": {"line": 37, "column": 34}, "end": {"line": 66, "column": 3}}, "20": {"start": {"line": 38, "column": 4}, "end": {"line": 41, "column": 5}}, "21": {"start": {"line": 39, "column": 6}, "end": {"line": 39, "column": 81}}, "22": {"start": {"line": 40, "column": 6}, "end": {"line": 40, "column": 13}}, "23": {"start": {"line": 42, "column": 4}, "end": {"line": 45, "column": 5}}, "24": {"start": {"line": 43, "column": 6}, "end": {"line": 43, "column": 73}}, "25": {"start": {"line": 44, "column": 6}, "end": {"line": 44, "column": 13}}, "26": {"start": {"line": 47, "column": 4}, "end": {"line": 47, "column": 20}}, "27": {"start": {"line": 48, "column": 4}, "end": {"line": 65, "column": 5}}, "28": {"start": {"line": 54, "column": 6}, "end": {"line": 57, "column": 9}}, "29": {"start": {"line": 58, "column": 6}, "end": {"line": 58, "column": 76}}, "30": {"start": {"line": 59, "column": 6}, "end": {"line": 59, "column": 20}}, "31": {"start": {"line": 61, "column": 6}, "end": {"line": 61, "column": 88}}, "32": {"start": {"line": 62, "column": 6}, "end": {"line": 62, "column": 70}}, "33": {"start": {"line": 64, "column": 6}, "end": {"line": 64, "column": 23}}, "34": {"start": {"line": 68, "column": 2}, "end": {"line": 75, "column": 3}}, "35": {"start": {"line": 69, "column": 4}, "end": {"line": 74, "column": 6}}, "36": {"start": {"line": 77, "column": 2}, "end": {"line": 83, "column": 3}}, "37": {"start": {"line": 78, "column": 4}, "end": {"line": 82, "column": 6}}, "38": {"start": {"line": 85, "column": 2}, "end": {"line": 114, "column": 4}}, "39": {"start": {"line": 117, "column": 15}, "end": {"line": 165, "column": 2}}}, "fnMap": {"0": {"name": "MajorCourseEditScreen", "decl": {"start": {"line": 8, "column": 24}, "end": {"line": 8, "column": 45}}, "loc": {"start": {"line": 8, "column": 48}, "end": {"line": 115, "column": 1}}, "line": 8}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 16, "column": 12}, "end": {"line": 16, "column": 13}}, "loc": {"start": {"line": 16, "column": 18}, "end": {"line": 35, "column": 3}}, "line": 16}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 18, "column": 31}, "end": {"line": 18, "column": 32}}, "loc": {"start": {"line": 18, "column": 43}, "end": {"line": 29, "column": 7}}, "line": 18}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 37, "column": 34}, "end": {"line": 37, "column": 35}}, "loc": {"start": {"line": 37, "column": 46}, "end": {"line": 66, "column": 3}}, "line": 37}}, "branchMap": {"0": {"loc": {"start": {"line": 17, "column": 4}, "end": {"line": 34, "column": 5}}, "type": "if", "locations": [{"start": {"line": 17, "column": 4}, "end": {"line": 34, "column": 5}}, {"start": {"line": 31, "column": 11}, "end": {"line": 34, "column": 5}}], "line": 17}, "1": {"loc": {"start": {"line": 17, "column": 8}, "end": {"line": 17, "column": 27}}, "type": "binary-expr", "locations": [{"start": {"line": 17, "column": 8}, "end": {"line": 17, "column": 15}}, {"start": {"line": 17, "column": 19}, "end": {"line": 17, "column": 27}}], "line": 17}, "2": {"loc": {"start": {"line": 38, "column": 4}, "end": {"line": 41, "column": 5}}, "type": "if", "locations": [{"start": {"line": 38, "column": 4}, "end": {"line": 41, "column": 5}}, {"start": {}, "end": {}}], "line": 38}, "3": {"loc": {"start": {"line": 38, "column": 8}, "end": {"line": 38, "column": 57}}, "type": "binary-expr", "locations": [{"start": {"line": 38, "column": 8}, "end": {"line": 38, "column": 30}}, {"start": {"line": 38, "column": 34}, "end": {"line": 38, "column": 57}}], "line": 38}, "4": {"loc": {"start": {"line": 42, "column": 4}, "end": {"line": 45, "column": 5}}, "type": "if", "locations": [{"start": {"line": 42, "column": 4}, "end": {"line": 45, "column": 5}}, {"start": {}, "end": {}}], "line": 42}, "5": {"loc": {"start": {"line": 42, "column": 8}, "end": {"line": 42, "column": 29}}, "type": "binary-expr", "locations": [{"start": {"line": 42, "column": 8}, "end": {"line": 42, "column": 16}}, {"start": {"line": 42, "column": 20}, "end": {"line": 42, "column": 29}}], "line": 42}, "6": {"loc": {"start": {"line": 68, "column": 2}, "end": {"line": 75, "column": 3}}, "type": "if", "locations": [{"start": {"line": 68, "column": 2}, "end": {"line": 75, "column": 3}}, {"start": {}, "end": {}}], "line": 68}, "7": {"loc": {"start": {"line": 77, "column": 2}, "end": {"line": 83, "column": 3}}, "type": "if", "locations": [{"start": {"line": 77, "column": 2}, "end": {"line": 83, "column": 3}}, {"start": {}, "end": {}}], "line": 77}, "8": {"loc": {"start": {"line": 105, "column": 31}, "end": {"line": 105, "column": 62}}, "type": "binary-expr", "locations": [{"start": {"line": 105, "column": 31}, "end": {"line": 105, "column": 37}}, {"start": {"line": 105, "column": 41}, "end": {"line": 105, "column": 62}}], "line": 105}, "9": {"loc": {"start": {"line": 110, "column": 11}, "end": {"line": 110, "column": 49}}, "type": "cond-expr", "locations": [{"start": {"line": 110, "column": 20}, "end": {"line": 110, "column": 31}}, {"start": {"line": 110, "column": 34}, "end": {"line": 110, "column": 49}}], "line": 110}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0], "3": [0, 0], "4": [0, 0], "5": [0, 0], "6": [0, 0], "7": [0, 0], "8": [0, 0], "9": [0, 0]}}, "/home/<USER>/projects/azmoon-client/app/screens/MajorCoursesList.tsx": {"path": "/home/<USER>/projects/azmoon-client/app/screens/MajorCoursesList.tsx", "statementMap": {"0": {"start": {"line": 9, "column": 42}, "end": {"line": 9, "column": 69}}, "1": {"start": {"line": 10, "column": 32}, "end": {"line": 10, "column": 46}}, "2": {"start": {"line": 11, "column": 38}, "end": {"line": 11, "column": 53}}, "3": {"start": {"line": 12, "column": 28}, "end": {"line": 12, "column": 57}}, "4": {"start": {"line": 14, "column": 27}, "end": {"line": 26, "column": 3}}, "5": {"start": {"line": 15, "column": 4}, "end": {"line": 25, "column": 5}}, "6": {"start": {"line": 16, "column": 6}, "end": {"line": 16, "column": 23}}, "7": {"start": {"line": 17, "column": 6}, "end": {"line": 17, "column": 21}}, "8": {"start": {"line": 18, "column": 19}, "end": {"line": 18, "column": 61}}, "9": {"start": {"line": 19, "column": 6}, "end": {"line": 19, "column": 28}}, "10": {"start": {"line": 21, "column": 6}, "end": {"line": 21, "column": 58}}, "11": {"start": {"line": 22, "column": 6}, "end": {"line": 22, "column": 25}}, "12": {"start": {"line": 24, "column": 6}, "end": {"line": 24, "column": 24}}, "13": {"start": {"line": 28, "column": 24}, "end": {"line": 40, "column": 3}}, "14": {"start": {"line": 29, "column": 4}, "end": {"line": 39, "column": 5}}, "15": {"start": {"line": 30, "column": 6}, "end": {"line": 30, "column": 26}}, "16": {"start": {"line": 31, "column": 6}, "end": {"line": 31, "column": 21}}, "17": {"start": {"line": 32, "column": 19}, "end": {"line": 32, "column": 61}}, "18": {"start": {"line": 33, "column": 6}, "end": {"line": 33, "column": 28}}, "19": {"start": {"line": 35, "column": 6}, "end": {"line": 35, "column": 60}}, "20": {"start": {"line": 36, "column": 6}, "end": {"line": 36, "column": 25}}, "21": {"start": {"line": 38, "column": 6}, "end": {"line": 38, "column": 27}}, "22": {"start": {"line": 42, "column": 2}, "end": {"line": 44, "column": 9}}, "23": {"start": {"line": 43, "column": 4}, "end": {"line": 43, "column": 23}}, "24": {"start": {"line": 46, "column": 2}, "end": {"line": 53, "column": 3}}, "25": {"start": {"line": 47, "column": 4}, "end": {"line": 52, "column": 6}}, "26": {"start": {"line": 55, "column": 2}, "end": {"line": 61, "column": 3}}, "27": {"start": {"line": 56, "column": 4}, "end": {"line": 60, "column": 6}}, "28": {"start": {"line": 63, "column": 2}, "end": {"line": 106, "column": 4}}, "29": {"start": {"line": 77, "column": 34}, "end": {"line": 77, "column": 68}}, "30": {"start": {"line": 79, "column": 12}, "end": {"line": 93, "column": 25}}, "31": {"start": {"line": 109, "column": 15}, "end": {"line": 169, "column": 2}}}, "fnMap": {"0": {"name": "MajorCoursesListScreen", "decl": {"start": {"line": 8, "column": 24}, "end": {"line": 8, "column": 46}}, "loc": {"start": {"line": 8, "column": 49}, "end": {"line": 107, "column": 1}}, "line": 8}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 14, "column": 27}, "end": {"line": 14, "column": 28}}, "loc": {"start": {"line": 14, "column": 39}, "end": {"line": 26, "column": 3}}, "line": 14}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 28, "column": 24}, "end": {"line": 28, "column": 25}}, "loc": {"start": {"line": 28, "column": 36}, "end": {"line": 40, "column": 3}}, "line": 28}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 42, "column": 12}, "end": {"line": 42, "column": 13}}, "loc": {"start": {"line": 42, "column": 18}, "end": {"line": 44, "column": 3}}, "line": 42}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 77, "column": 24}, "end": {"line": 77, "column": 25}}, "loc": {"start": {"line": 77, "column": 34}, "end": {"line": 77, "column": 68}}, "line": 77}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 78, "column": 22}, "end": {"line": 78, "column": 23}}, "loc": {"start": {"line": 79, "column": 12}, "end": {"line": 93, "column": 25}}, "line": 79}}, "branchMap": {"0": {"loc": {"start": {"line": 46, "column": 2}, "end": {"line": 53, "column": 3}}, "type": "if", "locations": [{"start": {"line": 46, "column": 2}, "end": {"line": 53, "column": 3}}, {"start": {}, "end": {}}], "line": 46}, "1": {"loc": {"start": {"line": 55, "column": 2}, "end": {"line": 61, "column": 3}}, "type": "if", "locations": [{"start": {"line": 55, "column": 2}, "end": {"line": 61, "column": 3}}, {"start": {}, "end": {}}], "line": 55}, "2": {"loc": {"start": {"line": 71, "column": 7}, "end": {"line": 104, "column": 7}}, "type": "cond-expr", "locations": [{"start": {"line": 72, "column": 8}, "end": {"line": 72, "column": 91}}, {"start": {"line": 74, "column": 8}, "end": {"line": 103, "column": 10}}], "line": 71}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0]}}, "/home/<USER>/projects/azmoon-client/app/screens/MajorsList.tsx": {"path": "/home/<USER>/projects/azmoon-client/app/screens/MajorsList.tsx", "statementMap": {"0": {"start": {"line": 23, "column": 22}, "end": {"line": 34, "column": 1}}, "1": {"start": {"line": 24, "column": 2}, "end": {"line": 33, "column": 8}}, "2": {"start": {"line": 37, "column": 30}, "end": {"line": 37, "column": 51}}, "3": {"start": {"line": 38, "column": 32}, "end": {"line": 38, "column": 46}}, "4": {"start": {"line": 39, "column": 38}, "end": {"line": 39, "column": 53}}, "5": {"start": {"line": 40, "column": 28}, "end": {"line": 40, "column": 57}}, "6": {"start": {"line": 41, "column": 22}, "end": {"line": 41, "column": 49}}, "7": {"start": {"line": 43, "column": 21}, "end": {"line": 45, "column": 3}}, "8": {"start": {"line": 44, "column": 4}, "end": {"line": 44, "column": 18}}, "9": {"start": {"line": 47, "column": 21}, "end": {"line": 59, "column": 3}}, "10": {"start": {"line": 48, "column": 4}, "end": {"line": 58, "column": 5}}, "11": {"start": {"line": 49, "column": 6}, "end": {"line": 49, "column": 23}}, "12": {"start": {"line": 50, "column": 6}, "end": {"line": 50, "column": 21}}, "13": {"start": {"line": 51, "column": 19}, "end": {"line": 51, "column": 49}}, "14": {"start": {"line": 52, "column": 6}, "end": {"line": 52, "column": 22}}, "15": {"start": {"line": 54, "column": 6}, "end": {"line": 54, "column": 42}}, "16": {"start": {"line": 55, "column": 6}, "end": {"line": 55, "column": 25}}, "17": {"start": {"line": 57, "column": 6}, "end": {"line": 57, "column": 24}}, "18": {"start": {"line": 61, "column": 24}, "end": {"line": 73, "column": 3}}, "19": {"start": {"line": 62, "column": 4}, "end": {"line": 72, "column": 5}}, "20": {"start": {"line": 63, "column": 6}, "end": {"line": 63, "column": 26}}, "21": {"start": {"line": 64, "column": 6}, "end": {"line": 64, "column": 21}}, "22": {"start": {"line": 65, "column": 19}, "end": {"line": 65, "column": 49}}, "23": {"start": {"line": 66, "column": 6}, "end": {"line": 66, "column": 22}}, "24": {"start": {"line": 68, "column": 6}, "end": {"line": 68, "column": 44}}, "25": {"start": {"line": 69, "column": 6}, "end": {"line": 69, "column": 25}}, "26": {"start": {"line": 71, "column": 6}, "end": {"line": 71, "column": 27}}, "27": {"start": {"line": 75, "column": 2}, "end": {"line": 77, "column": 9}}, "28": {"start": {"line": 76, "column": 4}, "end": {"line": 76, "column": 17}}, "29": {"start": {"line": 79, "column": 2}, "end": {"line": 86, "column": 3}}, "30": {"start": {"line": 80, "column": 4}, "end": {"line": 85, "column": 6}}, "31": {"start": {"line": 88, "column": 2}, "end": {"line": 94, "column": 3}}, "32": {"start": {"line": 89, "column": 4}, "end": {"line": 93, "column": 6}}, "33": {"start": {"line": 96, "column": 2}, "end": {"line": 172, "column": 4}}, "34": {"start": {"line": 145, "column": 38}, "end": {"line": 145, "column": 45}}, "35": {"start": {"line": 147, "column": 16}, "end": {"line": 165, "column": 29}}, "36": {"start": {"line": 175, "column": 15}, "end": {"line": 255, "column": 2}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 23, "column": 22}, "end": {"line": 23, "column": 23}}, "loc": {"start": {"line": 24, "column": 2}, "end": {"line": 33, "column": 8}}, "line": 24}, "1": {"name": "MajorsListScreen", "decl": {"start": {"line": 36, "column": 24}, "end": {"line": 36, "column": 40}}, "loc": {"start": {"line": 36, "column": 43}, "end": {"line": 173, "column": 1}}, "line": 36}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 43, "column": 21}, "end": {"line": 43, "column": 22}}, "loc": {"start": {"line": 43, "column": 27}, "end": {"line": 45, "column": 3}}, "line": 43}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 47, "column": 21}, "end": {"line": 47, "column": 22}}, "loc": {"start": {"line": 47, "column": 33}, "end": {"line": 59, "column": 3}}, "line": 47}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 61, "column": 24}, "end": {"line": 61, "column": 25}}, "loc": {"start": {"line": 61, "column": 36}, "end": {"line": 73, "column": 3}}, "line": 61}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 75, "column": 12}, "end": {"line": 75, "column": 13}}, "loc": {"start": {"line": 75, "column": 18}, "end": {"line": 77, "column": 3}}, "line": 75}, "6": {"name": "(anonymous_6)", "decl": {"start": {"line": 145, "column": 28}, "end": {"line": 145, "column": 29}}, "loc": {"start": {"line": 145, "column": 38}, "end": {"line": 145, "column": 45}}, "line": 145}, "7": {"name": "(anonymous_7)", "decl": {"start": {"line": 146, "column": 26}, "end": {"line": 146, "column": 27}}, "loc": {"start": {"line": 147, "column": 16}, "end": {"line": 165, "column": 29}}, "line": 147}}, "branchMap": {"0": {"loc": {"start": {"line": 23, "column": 25}, "end": {"line": 23, "column": 42}}, "type": "default-arg", "locations": [{"start": {"line": 23, "column": 33}, "end": {"line": 23, "column": 42}}], "line": 23}, "1": {"loc": {"start": {"line": 23, "column": 44}, "end": {"line": 23, "column": 53}}, "type": "default-arg", "locations": [{"start": {"line": 23, "column": 51}, "end": {"line": 23, "column": 53}}], "line": 23}, "2": {"loc": {"start": {"line": 41, "column": 22}, "end": {"line": 41, "column": 49}}, "type": "binary-expr", "locations": [{"start": {"line": 41, "column": 22}, "end": {"line": 41, "column": 38}}, {"start": {"line": 41, "column": 42}, "end": {"line": 41, "column": 49}}], "line": 41}, "3": {"loc": {"start": {"line": 79, "column": 2}, "end": {"line": 86, "column": 3}}, "type": "if", "locations": [{"start": {"line": 79, "column": 2}, "end": {"line": 86, "column": 3}}, {"start": {}, "end": {}}], "line": 79}, "4": {"loc": {"start": {"line": 88, "column": 2}, "end": {"line": 94, "column": 3}}, "type": "if", "locations": [{"start": {"line": 88, "column": 2}, "end": {"line": 94, "column": 3}}, {"start": {}, "end": {}}], "line": 88}, "5": {"loc": {"start": {"line": 140, "column": 11}, "end": {"line": 168, "column": 11}}, "type": "cond-expr", "locations": [{"start": {"line": 141, "column": 12}, "end": {"line": 141, "column": 79}}, {"start": {"line": 143, "column": 12}, "end": {"line": 167, "column": 14}}], "line": 140}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0}, "b": {"0": [0], "1": [0], "2": [0, 0], "3": [0, 0], "4": [0, 0], "5": [0, 0]}}, "/home/<USER>/projects/azmoon-client/app/screens/OptionAdd.tsx": {"path": "/home/<USER>/projects/azmoon-client/app/screens/OptionAdd.tsx", "statementMap": {"0": {"start": {"line": 9, "column": 26}, "end": {"line": 9, "column": 38}}, "1": {"start": {"line": 10, "column": 38}, "end": {"line": 10, "column": 50}}, "2": {"start": {"line": 11, "column": 36}, "end": {"line": 11, "column": 51}}, "3": {"start": {"line": 12, "column": 32}, "end": {"line": 12, "column": 47}}, "4": {"start": {"line": 14, "column": 26}, "end": {"line": 32, "column": 3}}, "5": {"start": {"line": 15, "column": 4}, "end": {"line": 18, "column": 5}}, "6": {"start": {"line": 16, "column": 6}, "end": {"line": 16, "column": 86}}, "7": {"start": {"line": 17, "column": 6}, "end": {"line": 17, "column": 13}}, "8": {"start": {"line": 20, "column": 4}, "end": {"line": 20, "column": 21}}, "9": {"start": {"line": 21, "column": 4}, "end": {"line": 31, "column": 5}}, "10": {"start": {"line": 23, "column": 6}, "end": {"line": 23, "column": 72}}, "11": {"start": {"line": 24, "column": 6}, "end": {"line": 24, "column": 59}}, "12": {"start": {"line": 25, "column": 6}, "end": {"line": 25, "column": 20}}, "13": {"start": {"line": 27, "column": 6}, "end": {"line": 27, "column": 70}}, "14": {"start": {"line": 28, "column": 6}, "end": {"line": 28, "column": 52}}, "15": {"start": {"line": 30, "column": 6}, "end": {"line": 30, "column": 24}}, "16": {"start": {"line": 34, "column": 2}, "end": {"line": 70, "column": 4}}, "17": {"start": {"line": 73, "column": 15}, "end": {"line": 117, "column": 2}}}, "fnMap": {"0": {"name": "OptionAddScreen", "decl": {"start": {"line": 8, "column": 24}, "end": {"line": 8, "column": 39}}, "loc": {"start": {"line": 8, "column": 42}, "end": {"line": 71, "column": 1}}, "line": 8}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 14, "column": 26}, "end": {"line": 14, "column": 27}}, "loc": {"start": {"line": 14, "column": 38}, "end": {"line": 32, "column": 3}}, "line": 14}}, "branchMap": {"0": {"loc": {"start": {"line": 15, "column": 4}, "end": {"line": 18, "column": 5}}, "type": "if", "locations": [{"start": {"line": 15, "column": 4}, "end": {"line": 18, "column": 5}}, {"start": {}, "end": {}}], "line": 15}, "1": {"loc": {"start": {"line": 15, "column": 8}, "end": {"line": 15, "column": 42}}, "type": "binary-expr", "locations": [{"start": {"line": 15, "column": 8}, "end": {"line": 15, "column": 20}}, {"start": {"line": 15, "column": 24}, "end": {"line": 15, "column": 42}}], "line": 15}, "2": {"loc": {"start": {"line": 61, "column": 31}, "end": {"line": 61, "column": 63}}, "type": "binary-expr", "locations": [{"start": {"line": 61, "column": 31}, "end": {"line": 61, "column": 38}}, {"start": {"line": 61, "column": 42}, "end": {"line": 61, "column": 63}}], "line": 61}, "3": {"loc": {"start": {"line": 66, "column": 11}, "end": {"line": 66, "column": 47}}, "type": "cond-expr", "locations": [{"start": {"line": 66, "column": 21}, "end": {"line": 66, "column": 32}}, {"start": {"line": 66, "column": 35}, "end": {"line": 66, "column": 47}}], "line": 66}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0}, "f": {"0": 0, "1": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0], "3": [0, 0]}}, "/home/<USER>/projects/azmoon-client/app/screens/OptionDetails.tsx": {"path": "/home/<USER>/projects/azmoon-client/app/screens/OptionDetails.tsx", "statementMap": {"0": {"start": {"line": 9, "column": 17}, "end": {"line": 9, "column": 39}}, "1": {"start": {"line": 10, "column": 30}, "end": {"line": 10, "column": 59}}, "2": {"start": {"line": 11, "column": 32}, "end": {"line": 11, "column": 46}}, "3": {"start": {"line": 12, "column": 28}, "end": {"line": 12, "column": 57}}, "4": {"start": {"line": 14, "column": 2}, "end": {"line": 32, "column": 11}}, "5": {"start": {"line": 15, "column": 4}, "end": {"line": 31, "column": 5}}, "6": {"start": {"line": 16, "column": 26}, "end": {"line": 26, "column": 7}}, "7": {"start": {"line": 17, "column": 8}, "end": {"line": 25, "column": 9}}, "8": {"start": {"line": 18, "column": 23}, "end": {"line": 18, "column": 66}}, "9": {"start": {"line": 19, "column": 10}, "end": {"line": 19, "column": 26}}, "10": {"start": {"line": 21, "column": 10}, "end": {"line": 21, "column": 54}}, "11": {"start": {"line": 22, "column": 10}, "end": {"line": 22, "column": 29}}, "12": {"start": {"line": 24, "column": 10}, "end": {"line": 24, "column": 28}}, "13": {"start": {"line": 27, "column": 6}, "end": {"line": 27, "column": 20}}, "14": {"start": {"line": 29, "column": 6}, "end": {"line": 29, "column": 42}}, "15": {"start": {"line": 30, "column": 6}, "end": {"line": 30, "column": 24}}, "16": {"start": {"line": 34, "column": 2}, "end": {"line": 41, "column": 3}}, "17": {"start": {"line": 35, "column": 4}, "end": {"line": 40, "column": 6}}, "18": {"start": {"line": 43, "column": 2}, "end": {"line": 49, "column": 3}}, "19": {"start": {"line": 44, "column": 4}, "end": {"line": 48, "column": 6}}, "20": {"start": {"line": 51, "column": 2}, "end": {"line": 57, "column": 3}}, "21": {"start": {"line": 52, "column": 4}, "end": {"line": 56, "column": 6}}, "22": {"start": {"line": 59, "column": 2}, "end": {"line": 74, "column": 4}}, "23": {"start": {"line": 77, "column": 15}, "end": {"line": 104, "column": 2}}}, "fnMap": {"0": {"name": "OptionDetailsScreen", "decl": {"start": {"line": 8, "column": 24}, "end": {"line": 8, "column": 43}}, "loc": {"start": {"line": 8, "column": 46}, "end": {"line": 75, "column": 1}}, "line": 8}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 14, "column": 12}, "end": {"line": 14, "column": 13}}, "loc": {"start": {"line": 14, "column": 18}, "end": {"line": 32, "column": 3}}, "line": 14}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 16, "column": 26}, "end": {"line": 16, "column": 27}}, "loc": {"start": {"line": 16, "column": 38}, "end": {"line": 26, "column": 7}}, "line": 16}}, "branchMap": {"0": {"loc": {"start": {"line": 15, "column": 4}, "end": {"line": 31, "column": 5}}, "type": "if", "locations": [{"start": {"line": 15, "column": 4}, "end": {"line": 31, "column": 5}}, {"start": {"line": 28, "column": 11}, "end": {"line": 31, "column": 5}}], "line": 15}, "1": {"loc": {"start": {"line": 34, "column": 2}, "end": {"line": 41, "column": 3}}, "type": "if", "locations": [{"start": {"line": 34, "column": 2}, "end": {"line": 41, "column": 3}}, {"start": {}, "end": {}}], "line": 34}, "2": {"loc": {"start": {"line": 43, "column": 2}, "end": {"line": 49, "column": 3}}, "type": "if", "locations": [{"start": {"line": 43, "column": 2}, "end": {"line": 49, "column": 3}}, {"start": {}, "end": {}}], "line": 43}, "3": {"loc": {"start": {"line": 51, "column": 2}, "end": {"line": 57, "column": 3}}, "type": "if", "locations": [{"start": {"line": 51, "column": 2}, "end": {"line": 57, "column": 3}}, {"start": {}, "end": {}}], "line": 51}, "4": {"loc": {"start": {"line": 67, "column": 7}, "end": {"line": 72, "column": 7}}, "type": "binary-expr", "locations": [{"start": {"line": 67, "column": 7}, "end": {"line": 67, "column": 23}}, {"start": {"line": 68, "column": 8}, "end": {"line": 71, "column": 21}}], "line": 67}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0}, "f": {"0": 0, "1": 0, "2": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0], "3": [0, 0], "4": [0, 0]}}, "/home/<USER>/projects/azmoon-client/app/screens/OptionEdit.tsx": {"path": "/home/<USER>/projects/azmoon-client/app/screens/OptionEdit.tsx", "statementMap": {"0": {"start": {"line": 9, "column": 17}, "end": {"line": 9, "column": 39}}, "1": {"start": {"line": 10, "column": 26}, "end": {"line": 10, "column": 38}}, "2": {"start": {"line": 11, "column": 38}, "end": {"line": 11, "column": 50}}, "3": {"start": {"line": 12, "column": 36}, "end": {"line": 12, "column": 51}}, "4": {"start": {"line": 13, "column": 32}, "end": {"line": 13, "column": 46}}, "5": {"start": {"line": 14, "column": 30}, "end": {"line": 14, "column": 45}}, "6": {"start": {"line": 15, "column": 28}, "end": {"line": 15, "column": 57}}, "7": {"start": {"line": 17, "column": 2}, "end": {"line": 37, "column": 11}}, "8": {"start": {"line": 18, "column": 4}, "end": {"line": 36, "column": 5}}, "9": {"start": {"line": 19, "column": 26}, "end": {"line": 31, "column": 7}}, "10": {"start": {"line": 20, "column": 8}, "end": {"line": 30, "column": 9}}, "11": {"start": {"line": 21, "column": 23}, "end": {"line": 21, "column": 66}}, "12": {"start": {"line": 22, "column": 10}, "end": {"line": 22, "column": 29}}, "13": {"start": {"line": 23, "column": 10}, "end": {"line": 23, "column": 41}}, "14": {"start": {"line": 24, "column": 10}, "end": {"line": 24, "column": 39}}, "15": {"start": {"line": 26, "column": 10}, "end": {"line": 26, "column": 66}}, "16": {"start": {"line": 27, "column": 10}, "end": {"line": 27, "column": 29}}, "17": {"start": {"line": 29, "column": 10}, "end": {"line": 29, "column": 28}}, "18": {"start": {"line": 32, "column": 6}, "end": {"line": 32, "column": 20}}, "19": {"start": {"line": 34, "column": 6}, "end": {"line": 34, "column": 54}}, "20": {"start": {"line": 35, "column": 6}, "end": {"line": 35, "column": 24}}, "21": {"start": {"line": 39, "column": 29}, "end": {"line": 60, "column": 3}}, "22": {"start": {"line": 40, "column": 4}, "end": {"line": 43, "column": 5}}, "23": {"start": {"line": 41, "column": 6}, "end": {"line": 41, "column": 86}}, "24": {"start": {"line": 42, "column": 6}, "end": {"line": 42, "column": 13}}, "25": {"start": {"line": 44, "column": 4}, "end": {"line": 47, "column": 5}}, "26": {"start": {"line": 45, "column": 6}, "end": {"line": 45, "column": 52}}, "27": {"start": {"line": 46, "column": 6}, "end": {"line": 46, "column": 13}}, "28": {"start": {"line": 49, "column": 4}, "end": {"line": 49, "column": 20}}, "29": {"start": {"line": 50, "column": 4}, "end": {"line": 59, "column": 5}}, "30": {"start": {"line": 51, "column": 6}, "end": {"line": 51, "column": 86}}, "31": {"start": {"line": 52, "column": 6}, "end": {"line": 52, "column": 61}}, "32": {"start": {"line": 53, "column": 6}, "end": {"line": 53, "column": 20}}, "33": {"start": {"line": 55, "column": 6}, "end": {"line": 55, "column": 73}}, "34": {"start": {"line": 56, "column": 6}, "end": {"line": 56, "column": 55}}, "35": {"start": {"line": 58, "column": 6}, "end": {"line": 58, "column": 23}}, "36": {"start": {"line": 62, "column": 2}, "end": {"line": 69, "column": 3}}, "37": {"start": {"line": 63, "column": 4}, "end": {"line": 68, "column": 6}}, "38": {"start": {"line": 71, "column": 2}, "end": {"line": 77, "column": 3}}, "39": {"start": {"line": 72, "column": 4}, "end": {"line": 76, "column": 6}}, "40": {"start": {"line": 79, "column": 2}, "end": {"line": 115, "column": 4}}, "41": {"start": {"line": 118, "column": 15}, "end": {"line": 171, "column": 2}}}, "fnMap": {"0": {"name": "OptionEditScreen", "decl": {"start": {"line": 8, "column": 24}, "end": {"line": 8, "column": 40}}, "loc": {"start": {"line": 8, "column": 43}, "end": {"line": 116, "column": 1}}, "line": 8}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 17, "column": 12}, "end": {"line": 17, "column": 13}}, "loc": {"start": {"line": 17, "column": 18}, "end": {"line": 37, "column": 3}}, "line": 17}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 19, "column": 26}, "end": {"line": 19, "column": 27}}, "loc": {"start": {"line": 19, "column": 38}, "end": {"line": 31, "column": 7}}, "line": 19}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 39, "column": 29}, "end": {"line": 39, "column": 30}}, "loc": {"start": {"line": 39, "column": 41}, "end": {"line": 60, "column": 3}}, "line": 39}}, "branchMap": {"0": {"loc": {"start": {"line": 18, "column": 4}, "end": {"line": 36, "column": 5}}, "type": "if", "locations": [{"start": {"line": 18, "column": 4}, "end": {"line": 36, "column": 5}}, {"start": {"line": 33, "column": 11}, "end": {"line": 36, "column": 5}}], "line": 18}, "1": {"loc": {"start": {"line": 40, "column": 4}, "end": {"line": 43, "column": 5}}, "type": "if", "locations": [{"start": {"line": 40, "column": 4}, "end": {"line": 43, "column": 5}}, {"start": {}, "end": {}}], "line": 40}, "2": {"loc": {"start": {"line": 40, "column": 8}, "end": {"line": 40, "column": 42}}, "type": "binary-expr", "locations": [{"start": {"line": 40, "column": 8}, "end": {"line": 40, "column": 20}}, {"start": {"line": 40, "column": 24}, "end": {"line": 40, "column": 42}}], "line": 40}, "3": {"loc": {"start": {"line": 44, "column": 4}, "end": {"line": 47, "column": 5}}, "type": "if", "locations": [{"start": {"line": 44, "column": 4}, "end": {"line": 47, "column": 5}}, {"start": {}, "end": {}}], "line": 44}, "4": {"loc": {"start": {"line": 62, "column": 2}, "end": {"line": 69, "column": 3}}, "type": "if", "locations": [{"start": {"line": 62, "column": 2}, "end": {"line": 69, "column": 3}}, {"start": {}, "end": {}}], "line": 62}, "5": {"loc": {"start": {"line": 71, "column": 2}, "end": {"line": 77, "column": 3}}, "type": "if", "locations": [{"start": {"line": 71, "column": 2}, "end": {"line": 77, "column": 3}}, {"start": {}, "end": {}}], "line": 71}, "6": {"loc": {"start": {"line": 106, "column": 31}, "end": {"line": 106, "column": 62}}, "type": "binary-expr", "locations": [{"start": {"line": 106, "column": 31}, "end": {"line": 106, "column": 37}}, {"start": {"line": 106, "column": 41}, "end": {"line": 106, "column": 62}}], "line": 106}, "7": {"loc": {"start": {"line": 111, "column": 11}, "end": {"line": 111, "column": 49}}, "type": "cond-expr", "locations": [{"start": {"line": 111, "column": 20}, "end": {"line": 111, "column": 31}}, {"start": {"line": 111, "column": 34}, "end": {"line": 111, "column": 49}}], "line": 111}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0], "3": [0, 0], "4": [0, 0], "5": [0, 0], "6": [0, 0], "7": [0, 0]}}, "/home/<USER>/projects/azmoon-client/app/screens/OptionsList.tsx": {"path": "/home/<USER>/projects/azmoon-client/app/screens/OptionsList.tsx", "statementMap": {"0": {"start": {"line": 9, "column": 32}, "end": {"line": 9, "column": 54}}, "1": {"start": {"line": 10, "column": 32}, "end": {"line": 10, "column": 46}}, "2": {"start": {"line": 11, "column": 38}, "end": {"line": 11, "column": 53}}, "3": {"start": {"line": 12, "column": 28}, "end": {"line": 12, "column": 57}}, "4": {"start": {"line": 14, "column": 22}, "end": {"line": 26, "column": 3}}, "5": {"start": {"line": 15, "column": 4}, "end": {"line": 25, "column": 5}}, "6": {"start": {"line": 16, "column": 6}, "end": {"line": 16, "column": 23}}, "7": {"start": {"line": 17, "column": 6}, "end": {"line": 17, "column": 21}}, "8": {"start": {"line": 18, "column": 19}, "end": {"line": 18, "column": 51}}, "9": {"start": {"line": 19, "column": 6}, "end": {"line": 19, "column": 23}}, "10": {"start": {"line": 21, "column": 6}, "end": {"line": 21, "column": 43}}, "11": {"start": {"line": 22, "column": 6}, "end": {"line": 22, "column": 25}}, "12": {"start": {"line": 24, "column": 6}, "end": {"line": 24, "column": 24}}, "13": {"start": {"line": 28, "column": 24}, "end": {"line": 40, "column": 3}}, "14": {"start": {"line": 29, "column": 4}, "end": {"line": 39, "column": 5}}, "15": {"start": {"line": 30, "column": 6}, "end": {"line": 30, "column": 26}}, "16": {"start": {"line": 31, "column": 6}, "end": {"line": 31, "column": 21}}, "17": {"start": {"line": 32, "column": 19}, "end": {"line": 32, "column": 51}}, "18": {"start": {"line": 33, "column": 6}, "end": {"line": 33, "column": 23}}, "19": {"start": {"line": 35, "column": 6}, "end": {"line": 35, "column": 45}}, "20": {"start": {"line": 36, "column": 6}, "end": {"line": 36, "column": 25}}, "21": {"start": {"line": 38, "column": 6}, "end": {"line": 38, "column": 27}}, "22": {"start": {"line": 42, "column": 2}, "end": {"line": 44, "column": 9}}, "23": {"start": {"line": 43, "column": 4}, "end": {"line": 43, "column": 18}}, "24": {"start": {"line": 46, "column": 2}, "end": {"line": 53, "column": 3}}, "25": {"start": {"line": 47, "column": 4}, "end": {"line": 52, "column": 6}}, "26": {"start": {"line": 55, "column": 2}, "end": {"line": 61, "column": 3}}, "27": {"start": {"line": 56, "column": 4}, "end": {"line": 60, "column": 6}}, "28": {"start": {"line": 63, "column": 2}, "end": {"line": 107, "column": 4}}, "29": {"start": {"line": 77, "column": 34}, "end": {"line": 77, "column": 41}}, "30": {"start": {"line": 79, "column": 12}, "end": {"line": 94, "column": 25}}, "31": {"start": {"line": 110, "column": 15}, "end": {"line": 170, "column": 2}}}, "fnMap": {"0": {"name": "OptionsListScreen", "decl": {"start": {"line": 8, "column": 24}, "end": {"line": 8, "column": 41}}, "loc": {"start": {"line": 8, "column": 44}, "end": {"line": 108, "column": 1}}, "line": 8}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 14, "column": 22}, "end": {"line": 14, "column": 23}}, "loc": {"start": {"line": 14, "column": 34}, "end": {"line": 26, "column": 3}}, "line": 14}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 28, "column": 24}, "end": {"line": 28, "column": 25}}, "loc": {"start": {"line": 28, "column": 36}, "end": {"line": 40, "column": 3}}, "line": 28}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 42, "column": 12}, "end": {"line": 42, "column": 13}}, "loc": {"start": {"line": 42, "column": 18}, "end": {"line": 44, "column": 3}}, "line": 42}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 77, "column": 24}, "end": {"line": 77, "column": 25}}, "loc": {"start": {"line": 77, "column": 34}, "end": {"line": 77, "column": 41}}, "line": 77}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 78, "column": 22}, "end": {"line": 78, "column": 23}}, "loc": {"start": {"line": 79, "column": 12}, "end": {"line": 94, "column": 25}}, "line": 79}}, "branchMap": {"0": {"loc": {"start": {"line": 46, "column": 2}, "end": {"line": 53, "column": 3}}, "type": "if", "locations": [{"start": {"line": 46, "column": 2}, "end": {"line": 53, "column": 3}}, {"start": {}, "end": {}}], "line": 46}, "1": {"loc": {"start": {"line": 55, "column": 2}, "end": {"line": 61, "column": 3}}, "type": "if", "locations": [{"start": {"line": 55, "column": 2}, "end": {"line": 61, "column": 3}}, {"start": {}, "end": {}}], "line": 55}, "2": {"loc": {"start": {"line": 71, "column": 7}, "end": {"line": 105, "column": 7}}, "type": "cond-expr", "locations": [{"start": {"line": 72, "column": 8}, "end": {"line": 72, "column": 76}}, {"start": {"line": 74, "column": 8}, "end": {"line": 104, "column": 10}}], "line": 71}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0]}}, "/home/<USER>/projects/azmoon-client/app/screens/Profile.tsx": {"path": "/home/<USER>/projects/azmoon-client/app/screens/Profile.tsx", "statementMap": {"0": {"start": {"line": 20, "column": 22}, "end": {"line": 24, "column": 1}}, "1": {"start": {"line": 21, "column": 2}, "end": {"line": 23, "column": 8}}, "2": {"start": {"line": 26, "column": 17}, "end": {"line": 30, "column": 1}}, "3": {"start": {"line": 27, "column": 2}, "end": {"line": 29, "column": 8}}, "4": {"start": {"line": 32, "column": 17}, "end": {"line": 37, "column": 1}}, "5": {"start": {"line": 33, "column": 2}, "end": {"line": 36, "column": 8}}, "6": {"start": {"line": 39, "column": 17}, "end": {"line": 44, "column": 1}}, "7": {"start": {"line": 40, "column": 2}, "end": {"line": 43, "column": 8}}, "8": {"start": {"line": 47, "column": 22}, "end": {"line": 47, "column": 49}}, "9": {"start": {"line": 48, "column": 36}, "end": {"line": 48, "column": 51}}, "10": {"start": {"line": 49, "column": 26}, "end": {"line": 49, "column": 49}}, "11": {"start": {"line": 50, "column": 28}, "end": {"line": 50, "column": 56}}, "12": {"start": {"line": 51, "column": 28}, "end": {"line": 51, "column": 51}}, "13": {"start": {"line": 52, "column": 24}, "end": {"line": 52, "column": 77}}, "14": {"start": {"line": 54, "column": 21}, "end": {"line": 56, "column": 3}}, "15": {"start": {"line": 55, "column": 4}, "end": {"line": 55, "column": 18}}, "16": {"start": {"line": 58, "column": 21}, "end": {"line": 60, "column": 3}}, "17": {"start": {"line": 59, "column": 4}, "end": {"line": 59, "column": 36}}, "18": {"start": {"line": 62, "column": 21}, "end": {"line": 65, "column": 3}}, "19": {"start": {"line": 63, "column": 4}, "end": {"line": 63, "column": 24}}, "20": {"start": {"line": 64, "column": 4}, "end": {"line": 64, "column": 60}}, "21": {"start": {"line": 67, "column": 23}, "end": {"line": 70, "column": 3}}, "22": {"start": {"line": 68, "column": 4}, "end": {"line": 68, "column": 24}}, "23": {"start": {"line": 72, "column": 23}, "end": {"line": 111, "column": 3}}, "24": {"start": {"line": 83, "column": 4}, "end": {"line": 110, "column": 11}}, "25": {"start": {"line": 113, "column": 2}, "end": {"line": 229, "column": 4}}, "26": {"start": {"line": 149, "column": 29}, "end": {"line": 149, "column": 53}}, "27": {"start": {"line": 232, "column": 15}, "end": {"line": 379, "column": 2}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 20, "column": 22}, "end": {"line": 20, "column": 23}}, "loc": {"start": {"line": 21, "column": 2}, "end": {"line": 23, "column": 8}}, "line": 21}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 26, "column": 17}, "end": {"line": 26, "column": 18}}, "loc": {"start": {"line": 27, "column": 2}, "end": {"line": 29, "column": 8}}, "line": 27}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 32, "column": 17}, "end": {"line": 32, "column": 18}}, "loc": {"start": {"line": 33, "column": 2}, "end": {"line": 36, "column": 8}}, "line": 33}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 39, "column": 17}, "end": {"line": 39, "column": 18}}, "loc": {"start": {"line": 40, "column": 2}, "end": {"line": 43, "column": 8}}, "line": 40}, "4": {"name": "ProfileScreen", "decl": {"start": {"line": 46, "column": 24}, "end": {"line": 46, "column": 37}}, "loc": {"start": {"line": 46, "column": 40}, "end": {"line": 230, "column": 1}}, "line": 46}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 54, "column": 21}, "end": {"line": 54, "column": 22}}, "loc": {"start": {"line": 54, "column": 27}, "end": {"line": 56, "column": 3}}, "line": 54}, "6": {"name": "(anonymous_6)", "decl": {"start": {"line": 58, "column": 21}, "end": {"line": 58, "column": 22}}, "loc": {"start": {"line": 58, "column": 27}, "end": {"line": 60, "column": 3}}, "line": 58}, "7": {"name": "(anonymous_7)", "decl": {"start": {"line": 62, "column": 21}, "end": {"line": 62, "column": 22}}, "loc": {"start": {"line": 62, "column": 27}, "end": {"line": 65, "column": 3}}, "line": 62}, "8": {"name": "(anonymous_8)", "decl": {"start": {"line": 67, "column": 23}, "end": {"line": 67, "column": 24}}, "loc": {"start": {"line": 67, "column": 29}, "end": {"line": 70, "column": 3}}, "line": 67}, "9": {"name": "(anonymous_9)", "decl": {"start": {"line": 72, "column": 23}, "end": {"line": 72, "column": 24}}, "loc": {"start": {"line": 83, "column": 4}, "end": {"line": 110, "column": 11}}, "line": 83}, "10": {"name": "(anonymous_10)", "decl": {"start": {"line": 149, "column": 23}, "end": {"line": 149, "column": 24}}, "loc": {"start": {"line": 149, "column": 29}, "end": {"line": 149, "column": 53}}, "line": 149}}, "branchMap": {"0": {"loc": {"start": {"line": 20, "column": 25}, "end": {"line": 20, "column": 42}}, "type": "default-arg", "locations": [{"start": {"line": 20, "column": 33}, "end": {"line": 20, "column": 42}}], "line": 20}, "1": {"loc": {"start": {"line": 20, "column": 44}, "end": {"line": 20, "column": 53}}, "type": "default-arg", "locations": [{"start": {"line": 20, "column": 51}, "end": {"line": 20, "column": 53}}], "line": 20}, "2": {"loc": {"start": {"line": 26, "column": 20}, "end": {"line": 26, "column": 37}}, "type": "default-arg", "locations": [{"start": {"line": 26, "column": 28}, "end": {"line": 26, "column": 37}}], "line": 26}, "3": {"loc": {"start": {"line": 26, "column": 39}, "end": {"line": 26, "column": 48}}, "type": "default-arg", "locations": [{"start": {"line": 26, "column": 46}, "end": {"line": 26, "column": 48}}], "line": 26}, "4": {"loc": {"start": {"line": 32, "column": 20}, "end": {"line": 32, "column": 37}}, "type": "default-arg", "locations": [{"start": {"line": 32, "column": 28}, "end": {"line": 32, "column": 37}}], "line": 32}, "5": {"loc": {"start": {"line": 32, "column": 39}, "end": {"line": 32, "column": 48}}, "type": "default-arg", "locations": [{"start": {"line": 32, "column": 46}, "end": {"line": 32, "column": 48}}], "line": 32}, "6": {"loc": {"start": {"line": 39, "column": 20}, "end": {"line": 39, "column": 37}}, "type": "default-arg", "locations": [{"start": {"line": 39, "column": 28}, "end": {"line": 39, "column": 37}}], "line": 39}, "7": {"loc": {"start": {"line": 39, "column": 39}, "end": {"line": 39, "column": 48}}, "type": "default-arg", "locations": [{"start": {"line": 39, "column": 46}, "end": {"line": 39, "column": 48}}], "line": 39}, "8": {"loc": {"start": {"line": 47, "column": 22}, "end": {"line": 47, "column": 49}}, "type": "binary-expr", "locations": [{"start": {"line": 47, "column": 22}, "end": {"line": 47, "column": 38}}, {"start": {"line": 47, "column": 42}, "end": {"line": 47, "column": 49}}], "line": 47}, "9": {"loc": {"start": {"line": 76, "column": 4}, "end": {"line": 76, "column": 21}}, "type": "default-arg", "locations": [{"start": {"line": 76, "column": 16}, "end": {"line": 76, "column": 21}}], "line": 76}, "10": {"loc": {"start": {"line": 87, "column": 7}, "end": {"line": 109, "column": 7}}, "type": "cond-expr", "locations": [{"start": {"line": 88, "column": 8}, "end": {"line": 104, "column": 10}}, {"start": {"line": 106, "column": 8}, "end": {"line": 108, "column": 15}}], "line": 87}, "11": {"loc": {"start": {"line": 91, "column": 12}, "end": {"line": 91, "column": 51}}, "type": "binary-expr", "locations": [{"start": {"line": 91, "column": 12}, "end": {"line": 91, "column": 21}}, {"start": {"line": 91, "column": 25}, "end": {"line": 91, "column": 51}}], "line": 91}, "12": {"loc": {"start": {"line": 101, "column": 25}, "end": {"line": 101, "column": 42}}, "type": "cond-expr", "locations": [{"start": {"line": 101, "column": 37}, "end": {"line": 101, "column": 38}}, {"start": {"line": 101, "column": 41}, "end": {"line": 101, "column": 42}}], "line": 101}, "13": {"loc": {"start": {"line": 102, "column": 29}, "end": {"line": 102, "column": 57}}, "type": "cond-expr", "locations": [{"start": {"line": 102, "column": 41}, "end": {"line": 102, "column": 46}}, {"start": {"line": 102, "column": 49}, "end": {"line": 102, "column": 57}}], "line": 102}, "14": {"loc": {"start": {"line": 153, "column": 17}, "end": {"line": 153, "column": 45}}, "type": "cond-expr", "locations": [{"start": {"line": 153, "column": 29}, "end": {"line": 153, "column": 34}}, {"start": {"line": 153, "column": 37}, "end": {"line": 153, "column": 45}}], "line": 153}, "15": {"loc": {"start": {"line": 183, "column": 11}, "end": {"line": 201, "column": 11}}, "type": "binary-expr", "locations": [{"start": {"line": 183, "column": 11}, "end": {"line": 183, "column": 20}}, {"start": {"line": 184, "column": 12}, "end": {"line": 200, "column": 19}}], "line": 183}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0}, "b": {"0": [0], "1": [0], "2": [0], "3": [0], "4": [0], "5": [0], "6": [0], "7": [0], "8": [0, 0], "9": [0], "10": [0, 0], "11": [0, 0], "12": [0, 0], "13": [0, 0], "14": [0, 0], "15": [0, 0]}}, "/home/<USER>/projects/azmoon-client/app/screens/QuestionAdd.tsx": {"path": "/home/<USER>/projects/azmoon-client/app/screens/QuestionAdd.tsx", "statementMap": {"0": {"start": {"line": 9, "column": 42}, "end": {"line": 9, "column": 54}}, "1": {"start": {"line": 10, "column": 36}, "end": {"line": 10, "column": 48}}, "2": {"start": {"line": 11, "column": 32}, "end": {"line": 11, "column": 47}}, "3": {"start": {"line": 13, "column": 28}, "end": {"line": 31, "column": 3}}, "4": {"start": {"line": 14, "column": 4}, "end": {"line": 17, "column": 5}}, "5": {"start": {"line": 15, "column": 6}, "end": {"line": 15, "column": 87}}, "6": {"start": {"line": 16, "column": 6}, "end": {"line": 16, "column": 13}}, "7": {"start": {"line": 19, "column": 4}, "end": {"line": 19, "column": 21}}, "8": {"start": {"line": 20, "column": 4}, "end": {"line": 30, "column": 5}}, "9": {"start": {"line": 22, "column": 6}, "end": {"line": 22, "column": 78}}, "10": {"start": {"line": 23, "column": 6}, "end": {"line": 23, "column": 61}}, "11": {"start": {"line": 24, "column": 6}, "end": {"line": 24, "column": 20}}, "12": {"start": {"line": 26, "column": 6}, "end": {"line": 26, "column": 72}}, "13": {"start": {"line": 27, "column": 6}, "end": {"line": 27, "column": 54}}, "14": {"start": {"line": 29, "column": 6}, "end": {"line": 29, "column": 24}}, "15": {"start": {"line": 33, "column": 2}, "end": {"line": 61, "column": 4}}, "16": {"start": {"line": 64, "column": 15}, "end": {"line": 99, "column": 2}}}, "fnMap": {"0": {"name": "QuestionAddScreen", "decl": {"start": {"line": 8, "column": 24}, "end": {"line": 8, "column": 41}}, "loc": {"start": {"line": 8, "column": 44}, "end": {"line": 62, "column": 1}}, "line": 8}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 13, "column": 28}, "end": {"line": 13, "column": 29}}, "loc": {"start": {"line": 13, "column": 40}, "end": {"line": 31, "column": 3}}, "line": 13}}, "branchMap": {"0": {"loc": {"start": {"line": 14, "column": 4}, "end": {"line": 17, "column": 5}}, "type": "if", "locations": [{"start": {"line": 14, "column": 4}, "end": {"line": 17, "column": 5}}, {"start": {}, "end": {}}], "line": 14}, "1": {"loc": {"start": {"line": 14, "column": 8}, "end": {"line": 14, "column": 49}}, "type": "binary-expr", "locations": [{"start": {"line": 14, "column": 8}, "end": {"line": 14, "column": 28}}, {"start": {"line": 14, "column": 32}, "end": {"line": 14, "column": 49}}], "line": 14}, "2": {"loc": {"start": {"line": 52, "column": 31}, "end": {"line": 52, "column": 63}}, "type": "binary-expr", "locations": [{"start": {"line": 52, "column": 31}, "end": {"line": 52, "column": 38}}, {"start": {"line": 52, "column": 42}, "end": {"line": 52, "column": 63}}], "line": 52}, "3": {"loc": {"start": {"line": 57, "column": 11}, "end": {"line": 57, "column": 49}}, "type": "cond-expr", "locations": [{"start": {"line": 57, "column": 21}, "end": {"line": 57, "column": 32}}, {"start": {"line": 57, "column": 35}, "end": {"line": 57, "column": 49}}], "line": 57}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0}, "f": {"0": 0, "1": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0], "3": [0, 0]}}, "/home/<USER>/projects/azmoon-client/app/screens/QuestionDetails.tsx": {"path": "/home/<USER>/projects/azmoon-client/app/screens/QuestionDetails.tsx", "statementMap": {"0": {"start": {"line": 9, "column": 17}, "end": {"line": 9, "column": 39}}, "1": {"start": {"line": 10, "column": 34}, "end": {"line": 10, "column": 65}}, "2": {"start": {"line": 11, "column": 32}, "end": {"line": 11, "column": 46}}, "3": {"start": {"line": 12, "column": 28}, "end": {"line": 12, "column": 57}}, "4": {"start": {"line": 14, "column": 2}, "end": {"line": 32, "column": 11}}, "5": {"start": {"line": 15, "column": 4}, "end": {"line": 31, "column": 5}}, "6": {"start": {"line": 16, "column": 28}, "end": {"line": 26, "column": 7}}, "7": {"start": {"line": 17, "column": 8}, "end": {"line": 25, "column": 9}}, "8": {"start": {"line": 18, "column": 23}, "end": {"line": 18, "column": 70}}, "9": {"start": {"line": 19, "column": 10}, "end": {"line": 19, "column": 28}}, "10": {"start": {"line": 21, "column": 10}, "end": {"line": 21, "column": 56}}, "11": {"start": {"line": 22, "column": 10}, "end": {"line": 22, "column": 29}}, "12": {"start": {"line": 24, "column": 10}, "end": {"line": 24, "column": 28}}, "13": {"start": {"line": 27, "column": 6}, "end": {"line": 27, "column": 22}}, "14": {"start": {"line": 29, "column": 6}, "end": {"line": 29, "column": 44}}, "15": {"start": {"line": 30, "column": 6}, "end": {"line": 30, "column": 24}}, "16": {"start": {"line": 34, "column": 2}, "end": {"line": 41, "column": 3}}, "17": {"start": {"line": 35, "column": 4}, "end": {"line": 40, "column": 6}}, "18": {"start": {"line": 43, "column": 2}, "end": {"line": 49, "column": 3}}, "19": {"start": {"line": 44, "column": 4}, "end": {"line": 48, "column": 6}}, "20": {"start": {"line": 51, "column": 2}, "end": {"line": 57, "column": 3}}, "21": {"start": {"line": 52, "column": 4}, "end": {"line": 56, "column": 6}}, "22": {"start": {"line": 59, "column": 2}, "end": {"line": 74, "column": 4}}, "23": {"start": {"line": 77, "column": 15}, "end": {"line": 104, "column": 2}}}, "fnMap": {"0": {"name": "QuestionDetailsScreen", "decl": {"start": {"line": 8, "column": 24}, "end": {"line": 8, "column": 45}}, "loc": {"start": {"line": 8, "column": 48}, "end": {"line": 75, "column": 1}}, "line": 8}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 14, "column": 12}, "end": {"line": 14, "column": 13}}, "loc": {"start": {"line": 14, "column": 18}, "end": {"line": 32, "column": 3}}, "line": 14}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 16, "column": 28}, "end": {"line": 16, "column": 29}}, "loc": {"start": {"line": 16, "column": 40}, "end": {"line": 26, "column": 7}}, "line": 16}}, "branchMap": {"0": {"loc": {"start": {"line": 15, "column": 4}, "end": {"line": 31, "column": 5}}, "type": "if", "locations": [{"start": {"line": 15, "column": 4}, "end": {"line": 31, "column": 5}}, {"start": {"line": 28, "column": 11}, "end": {"line": 31, "column": 5}}], "line": 15}, "1": {"loc": {"start": {"line": 34, "column": 2}, "end": {"line": 41, "column": 3}}, "type": "if", "locations": [{"start": {"line": 34, "column": 2}, "end": {"line": 41, "column": 3}}, {"start": {}, "end": {}}], "line": 34}, "2": {"loc": {"start": {"line": 43, "column": 2}, "end": {"line": 49, "column": 3}}, "type": "if", "locations": [{"start": {"line": 43, "column": 2}, "end": {"line": 49, "column": 3}}, {"start": {}, "end": {}}], "line": 43}, "3": {"loc": {"start": {"line": 51, "column": 2}, "end": {"line": 57, "column": 3}}, "type": "if", "locations": [{"start": {"line": 51, "column": 2}, "end": {"line": 57, "column": 3}}, {"start": {}, "end": {}}], "line": 51}, "4": {"loc": {"start": {"line": 67, "column": 7}, "end": {"line": 72, "column": 7}}, "type": "binary-expr", "locations": [{"start": {"line": 67, "column": 7}, "end": {"line": 67, "column": 25}}, {"start": {"line": 68, "column": 8}, "end": {"line": 71, "column": 21}}], "line": 67}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0}, "f": {"0": 0, "1": 0, "2": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0], "3": [0, 0], "4": [0, 0]}}, "/home/<USER>/projects/azmoon-client/app/screens/QuestionEdit.tsx": {"path": "/home/<USER>/projects/azmoon-client/app/screens/QuestionEdit.tsx", "statementMap": {"0": {"start": {"line": 9, "column": 17}, "end": {"line": 9, "column": 39}}, "1": {"start": {"line": 10, "column": 42}, "end": {"line": 10, "column": 54}}, "2": {"start": {"line": 11, "column": 36}, "end": {"line": 11, "column": 48}}, "3": {"start": {"line": 12, "column": 32}, "end": {"line": 12, "column": 46}}, "4": {"start": {"line": 13, "column": 30}, "end": {"line": 13, "column": 45}}, "5": {"start": {"line": 14, "column": 28}, "end": {"line": 14, "column": 57}}, "6": {"start": {"line": 16, "column": 2}, "end": {"line": 35, "column": 11}}, "7": {"start": {"line": 17, "column": 4}, "end": {"line": 34, "column": 5}}, "8": {"start": {"line": 18, "column": 28}, "end": {"line": 29, "column": 7}}, "9": {"start": {"line": 19, "column": 8}, "end": {"line": 28, "column": 9}}, "10": {"start": {"line": 20, "column": 23}, "end": {"line": 20, "column": 70}}, "11": {"start": {"line": 21, "column": 10}, "end": {"line": 21, "column": 37}}, "12": {"start": {"line": 22, "column": 10}, "end": {"line": 22, "column": 39}}, "13": {"start": {"line": 24, "column": 10}, "end": {"line": 24, "column": 68}}, "14": {"start": {"line": 25, "column": 10}, "end": {"line": 25, "column": 29}}, "15": {"start": {"line": 27, "column": 10}, "end": {"line": 27, "column": 28}}, "16": {"start": {"line": 30, "column": 6}, "end": {"line": 30, "column": 22}}, "17": {"start": {"line": 32, "column": 6}, "end": {"line": 32, "column": 56}}, "18": {"start": {"line": 33, "column": 6}, "end": {"line": 33, "column": 24}}, "19": {"start": {"line": 37, "column": 31}, "end": {"line": 58, "column": 3}}, "20": {"start": {"line": 38, "column": 4}, "end": {"line": 41, "column": 5}}, "21": {"start": {"line": 39, "column": 6}, "end": {"line": 39, "column": 87}}, "22": {"start": {"line": 40, "column": 6}, "end": {"line": 40, "column": 13}}, "23": {"start": {"line": 42, "column": 4}, "end": {"line": 45, "column": 5}}, "24": {"start": {"line": 43, "column": 6}, "end": {"line": 43, "column": 54}}, "25": {"start": {"line": 44, "column": 6}, "end": {"line": 44, "column": 13}}, "26": {"start": {"line": 47, "column": 4}, "end": {"line": 47, "column": 20}}, "27": {"start": {"line": 48, "column": 4}, "end": {"line": 57, "column": 5}}, "28": {"start": {"line": 49, "column": 6}, "end": {"line": 49, "column": 92}}, "29": {"start": {"line": 50, "column": 6}, "end": {"line": 50, "column": 63}}, "30": {"start": {"line": 51, "column": 6}, "end": {"line": 51, "column": 20}}, "31": {"start": {"line": 53, "column": 6}, "end": {"line": 53, "column": 75}}, "32": {"start": {"line": 54, "column": 6}, "end": {"line": 54, "column": 57}}, "33": {"start": {"line": 56, "column": 6}, "end": {"line": 56, "column": 23}}, "34": {"start": {"line": 60, "column": 2}, "end": {"line": 67, "column": 3}}, "35": {"start": {"line": 61, "column": 4}, "end": {"line": 66, "column": 6}}, "36": {"start": {"line": 69, "column": 2}, "end": {"line": 75, "column": 3}}, "37": {"start": {"line": 70, "column": 4}, "end": {"line": 74, "column": 6}}, "38": {"start": {"line": 77, "column": 2}, "end": {"line": 105, "column": 4}}, "39": {"start": {"line": 108, "column": 15}, "end": {"line": 152, "column": 2}}}, "fnMap": {"0": {"name": "QuestionEditScreen", "decl": {"start": {"line": 8, "column": 24}, "end": {"line": 8, "column": 42}}, "loc": {"start": {"line": 8, "column": 45}, "end": {"line": 106, "column": 1}}, "line": 8}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 16, "column": 12}, "end": {"line": 16, "column": 13}}, "loc": {"start": {"line": 16, "column": 18}, "end": {"line": 35, "column": 3}}, "line": 16}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 18, "column": 28}, "end": {"line": 18, "column": 29}}, "loc": {"start": {"line": 18, "column": 40}, "end": {"line": 29, "column": 7}}, "line": 18}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 37, "column": 31}, "end": {"line": 37, "column": 32}}, "loc": {"start": {"line": 37, "column": 43}, "end": {"line": 58, "column": 3}}, "line": 37}}, "branchMap": {"0": {"loc": {"start": {"line": 17, "column": 4}, "end": {"line": 34, "column": 5}}, "type": "if", "locations": [{"start": {"line": 17, "column": 4}, "end": {"line": 34, "column": 5}}, {"start": {"line": 31, "column": 11}, "end": {"line": 34, "column": 5}}], "line": 17}, "1": {"loc": {"start": {"line": 38, "column": 4}, "end": {"line": 41, "column": 5}}, "type": "if", "locations": [{"start": {"line": 38, "column": 4}, "end": {"line": 41, "column": 5}}, {"start": {}, "end": {}}], "line": 38}, "2": {"loc": {"start": {"line": 38, "column": 8}, "end": {"line": 38, "column": 49}}, "type": "binary-expr", "locations": [{"start": {"line": 38, "column": 8}, "end": {"line": 38, "column": 28}}, {"start": {"line": 38, "column": 32}, "end": {"line": 38, "column": 49}}], "line": 38}, "3": {"loc": {"start": {"line": 42, "column": 4}, "end": {"line": 45, "column": 5}}, "type": "if", "locations": [{"start": {"line": 42, "column": 4}, "end": {"line": 45, "column": 5}}, {"start": {}, "end": {}}], "line": 42}, "4": {"loc": {"start": {"line": 60, "column": 2}, "end": {"line": 67, "column": 3}}, "type": "if", "locations": [{"start": {"line": 60, "column": 2}, "end": {"line": 67, "column": 3}}, {"start": {}, "end": {}}], "line": 60}, "5": {"loc": {"start": {"line": 69, "column": 2}, "end": {"line": 75, "column": 3}}, "type": "if", "locations": [{"start": {"line": 69, "column": 2}, "end": {"line": 75, "column": 3}}, {"start": {}, "end": {}}], "line": 69}, "6": {"loc": {"start": {"line": 96, "column": 31}, "end": {"line": 96, "column": 62}}, "type": "binary-expr", "locations": [{"start": {"line": 96, "column": 31}, "end": {"line": 96, "column": 37}}, {"start": {"line": 96, "column": 41}, "end": {"line": 96, "column": 62}}], "line": 96}, "7": {"loc": {"start": {"line": 101, "column": 11}, "end": {"line": 101, "column": 49}}, "type": "cond-expr", "locations": [{"start": {"line": 101, "column": 20}, "end": {"line": 101, "column": 31}}, {"start": {"line": 101, "column": 34}, "end": {"line": 101, "column": 49}}], "line": 101}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0], "3": [0, 0], "4": [0, 0], "5": [0, 0], "6": [0, 0], "7": [0, 0]}}, "/home/<USER>/projects/azmoon-client/app/screens/QuestionsList.tsx": {"path": "/home/<USER>/projects/azmoon-client/app/screens/QuestionsList.tsx", "statementMap": {"0": {"start": {"line": 9, "column": 36}, "end": {"line": 9, "column": 60}}, "1": {"start": {"line": 10, "column": 32}, "end": {"line": 10, "column": 46}}, "2": {"start": {"line": 11, "column": 38}, "end": {"line": 11, "column": 53}}, "3": {"start": {"line": 12, "column": 28}, "end": {"line": 12, "column": 57}}, "4": {"start": {"line": 14, "column": 24}, "end": {"line": 26, "column": 3}}, "5": {"start": {"line": 15, "column": 4}, "end": {"line": 25, "column": 5}}, "6": {"start": {"line": 16, "column": 6}, "end": {"line": 16, "column": 23}}, "7": {"start": {"line": 17, "column": 6}, "end": {"line": 17, "column": 21}}, "8": {"start": {"line": 18, "column": 19}, "end": {"line": 18, "column": 55}}, "9": {"start": {"line": 19, "column": 6}, "end": {"line": 19, "column": 25}}, "10": {"start": {"line": 21, "column": 6}, "end": {"line": 21, "column": 45}}, "11": {"start": {"line": 22, "column": 6}, "end": {"line": 22, "column": 25}}, "12": {"start": {"line": 24, "column": 6}, "end": {"line": 24, "column": 24}}, "13": {"start": {"line": 28, "column": 24}, "end": {"line": 40, "column": 3}}, "14": {"start": {"line": 29, "column": 4}, "end": {"line": 39, "column": 5}}, "15": {"start": {"line": 30, "column": 6}, "end": {"line": 30, "column": 26}}, "16": {"start": {"line": 31, "column": 6}, "end": {"line": 31, "column": 21}}, "17": {"start": {"line": 32, "column": 19}, "end": {"line": 32, "column": 55}}, "18": {"start": {"line": 33, "column": 6}, "end": {"line": 33, "column": 25}}, "19": {"start": {"line": 35, "column": 6}, "end": {"line": 35, "column": 47}}, "20": {"start": {"line": 36, "column": 6}, "end": {"line": 36, "column": 25}}, "21": {"start": {"line": 38, "column": 6}, "end": {"line": 38, "column": 27}}, "22": {"start": {"line": 42, "column": 2}, "end": {"line": 44, "column": 9}}, "23": {"start": {"line": 43, "column": 4}, "end": {"line": 43, "column": 20}}, "24": {"start": {"line": 46, "column": 2}, "end": {"line": 53, "column": 3}}, "25": {"start": {"line": 47, "column": 4}, "end": {"line": 52, "column": 6}}, "26": {"start": {"line": 55, "column": 2}, "end": {"line": 61, "column": 3}}, "27": {"start": {"line": 56, "column": 4}, "end": {"line": 60, "column": 6}}, "28": {"start": {"line": 63, "column": 2}, "end": {"line": 107, "column": 4}}, "29": {"start": {"line": 77, "column": 34}, "end": {"line": 77, "column": 41}}, "30": {"start": {"line": 79, "column": 12}, "end": {"line": 94, "column": 25}}, "31": {"start": {"line": 110, "column": 15}, "end": {"line": 170, "column": 2}}}, "fnMap": {"0": {"name": "QuestionsListScreen", "decl": {"start": {"line": 8, "column": 24}, "end": {"line": 8, "column": 43}}, "loc": {"start": {"line": 8, "column": 46}, "end": {"line": 108, "column": 1}}, "line": 8}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 14, "column": 24}, "end": {"line": 14, "column": 25}}, "loc": {"start": {"line": 14, "column": 36}, "end": {"line": 26, "column": 3}}, "line": 14}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 28, "column": 24}, "end": {"line": 28, "column": 25}}, "loc": {"start": {"line": 28, "column": 36}, "end": {"line": 40, "column": 3}}, "line": 28}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 42, "column": 12}, "end": {"line": 42, "column": 13}}, "loc": {"start": {"line": 42, "column": 18}, "end": {"line": 44, "column": 3}}, "line": 42}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 77, "column": 24}, "end": {"line": 77, "column": 25}}, "loc": {"start": {"line": 77, "column": 34}, "end": {"line": 77, "column": 41}}, "line": 77}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 78, "column": 22}, "end": {"line": 78, "column": 23}}, "loc": {"start": {"line": 79, "column": 12}, "end": {"line": 94, "column": 25}}, "line": 79}}, "branchMap": {"0": {"loc": {"start": {"line": 46, "column": 2}, "end": {"line": 53, "column": 3}}, "type": "if", "locations": [{"start": {"line": 46, "column": 2}, "end": {"line": 53, "column": 3}}, {"start": {}, "end": {}}], "line": 46}, "1": {"loc": {"start": {"line": 55, "column": 2}, "end": {"line": 61, "column": 3}}, "type": "if", "locations": [{"start": {"line": 55, "column": 2}, "end": {"line": 61, "column": 3}}, {"start": {}, "end": {}}], "line": 55}, "2": {"loc": {"start": {"line": 71, "column": 7}, "end": {"line": 105, "column": 7}}, "type": "cond-expr", "locations": [{"start": {"line": 72, "column": 8}, "end": {"line": 72, "column": 78}}, {"start": {"line": 74, "column": 8}, "end": {"line": 104, "column": 10}}], "line": 71}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0]}}, "/home/<USER>/projects/azmoon-client/app/screens/SessionAdd.tsx": {"path": "/home/<USER>/projects/azmoon-client/app/screens/SessionAdd.tsx", "statementMap": {"0": {"start": {"line": 9, "column": 30}, "end": {"line": 9, "column": 42}}, "1": {"start": {"line": 10, "column": 30}, "end": {"line": 10, "column": 42}}, "2": {"start": {"line": 11, "column": 28}, "end": {"line": 11, "column": 40}}, "3": {"start": {"line": 12, "column": 32}, "end": {"line": 12, "column": 47}}, "4": {"start": {"line": 14, "column": 27}, "end": {"line": 32, "column": 3}}, "5": {"start": {"line": 15, "column": 4}, "end": {"line": 18, "column": 5}}, "6": {"start": {"line": 16, "column": 6}, "end": {"line": 16, "column": 86}}, "7": {"start": {"line": 17, "column": 6}, "end": {"line": 17, "column": 13}}, "8": {"start": {"line": 20, "column": 4}, "end": {"line": 20, "column": 21}}, "9": {"start": {"line": 21, "column": 4}, "end": {"line": 31, "column": 5}}, "10": {"start": {"line": 23, "column": 6}, "end": {"line": 23, "column": 85}}, "11": {"start": {"line": 24, "column": 6}, "end": {"line": 24, "column": 60}}, "12": {"start": {"line": 25, "column": 6}, "end": {"line": 25, "column": 20}}, "13": {"start": {"line": 27, "column": 6}, "end": {"line": 27, "column": 71}}, "14": {"start": {"line": 28, "column": 6}, "end": {"line": 28, "column": 53}}, "15": {"start": {"line": 30, "column": 6}, "end": {"line": 30, "column": 24}}, "16": {"start": {"line": 34, "column": 2}, "end": {"line": 69, "column": 4}}, "17": {"start": {"line": 72, "column": 15}, "end": {"line": 107, "column": 2}}}, "fnMap": {"0": {"name": "SessionAddScreen", "decl": {"start": {"line": 8, "column": 24}, "end": {"line": 8, "column": 40}}, "loc": {"start": {"line": 8, "column": 43}, "end": {"line": 70, "column": 1}}, "line": 8}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 14, "column": 27}, "end": {"line": 14, "column": 28}}, "loc": {"start": {"line": 14, "column": 39}, "end": {"line": 32, "column": 3}}, "line": 14}}, "branchMap": {"0": {"loc": {"start": {"line": 15, "column": 4}, "end": {"line": 18, "column": 5}}, "type": "if", "locations": [{"start": {"line": 15, "column": 4}, "end": {"line": 18, "column": 5}}, {"start": {}, "end": {}}], "line": 15}, "1": {"loc": {"start": {"line": 15, "column": 8}, "end": {"line": 15, "column": 57}}, "type": "binary-expr", "locations": [{"start": {"line": 15, "column": 8}, "end": {"line": 15, "column": 22}}, {"start": {"line": 15, "column": 26}, "end": {"line": 15, "column": 40}}, {"start": {"line": 15, "column": 44}, "end": {"line": 15, "column": 57}}], "line": 15}, "2": {"loc": {"start": {"line": 60, "column": 31}, "end": {"line": 60, "column": 63}}, "type": "binary-expr", "locations": [{"start": {"line": 60, "column": 31}, "end": {"line": 60, "column": 38}}, {"start": {"line": 60, "column": 42}, "end": {"line": 60, "column": 63}}], "line": 60}, "3": {"loc": {"start": {"line": 65, "column": 11}, "end": {"line": 65, "column": 48}}, "type": "cond-expr", "locations": [{"start": {"line": 65, "column": 21}, "end": {"line": 65, "column": 32}}, {"start": {"line": 65, "column": 35}, "end": {"line": 65, "column": 48}}], "line": 65}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0}, "f": {"0": 0, "1": 0}, "b": {"0": [0, 0], "1": [0, 0, 0], "2": [0, 0], "3": [0, 0]}}, "/home/<USER>/projects/azmoon-client/app/screens/SessionDetails.tsx": {"path": "/home/<USER>/projects/azmoon-client/app/screens/SessionDetails.tsx", "statementMap": {"0": {"start": {"line": 9, "column": 17}, "end": {"line": 9, "column": 39}}, "1": {"start": {"line": 10, "column": 32}, "end": {"line": 10, "column": 62}}, "2": {"start": {"line": 11, "column": 32}, "end": {"line": 11, "column": 46}}, "3": {"start": {"line": 12, "column": 28}, "end": {"line": 12, "column": 57}}, "4": {"start": {"line": 14, "column": 2}, "end": {"line": 32, "column": 11}}, "5": {"start": {"line": 15, "column": 4}, "end": {"line": 31, "column": 5}}, "6": {"start": {"line": 16, "column": 27}, "end": {"line": 26, "column": 7}}, "7": {"start": {"line": 17, "column": 8}, "end": {"line": 25, "column": 9}}, "8": {"start": {"line": 18, "column": 23}, "end": {"line": 18, "column": 68}}, "9": {"start": {"line": 19, "column": 10}, "end": {"line": 19, "column": 27}}, "10": {"start": {"line": 21, "column": 10}, "end": {"line": 21, "column": 55}}, "11": {"start": {"line": 22, "column": 10}, "end": {"line": 22, "column": 29}}, "12": {"start": {"line": 24, "column": 10}, "end": {"line": 24, "column": 28}}, "13": {"start": {"line": 27, "column": 6}, "end": {"line": 27, "column": 21}}, "14": {"start": {"line": 29, "column": 6}, "end": {"line": 29, "column": 43}}, "15": {"start": {"line": 30, "column": 6}, "end": {"line": 30, "column": 24}}, "16": {"start": {"line": 34, "column": 2}, "end": {"line": 41, "column": 3}}, "17": {"start": {"line": 35, "column": 4}, "end": {"line": 40, "column": 6}}, "18": {"start": {"line": 43, "column": 2}, "end": {"line": 49, "column": 3}}, "19": {"start": {"line": 44, "column": 4}, "end": {"line": 48, "column": 6}}, "20": {"start": {"line": 51, "column": 2}, "end": {"line": 57, "column": 3}}, "21": {"start": {"line": 52, "column": 4}, "end": {"line": 56, "column": 6}}, "22": {"start": {"line": 59, "column": 2}, "end": {"line": 74, "column": 4}}, "23": {"start": {"line": 77, "column": 15}, "end": {"line": 104, "column": 2}}}, "fnMap": {"0": {"name": "SessionDetailsScreen", "decl": {"start": {"line": 8, "column": 24}, "end": {"line": 8, "column": 44}}, "loc": {"start": {"line": 8, "column": 47}, "end": {"line": 75, "column": 1}}, "line": 8}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 14, "column": 12}, "end": {"line": 14, "column": 13}}, "loc": {"start": {"line": 14, "column": 18}, "end": {"line": 32, "column": 3}}, "line": 14}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 16, "column": 27}, "end": {"line": 16, "column": 28}}, "loc": {"start": {"line": 16, "column": 39}, "end": {"line": 26, "column": 7}}, "line": 16}}, "branchMap": {"0": {"loc": {"start": {"line": 15, "column": 4}, "end": {"line": 31, "column": 5}}, "type": "if", "locations": [{"start": {"line": 15, "column": 4}, "end": {"line": 31, "column": 5}}, {"start": {"line": 28, "column": 11}, "end": {"line": 31, "column": 5}}], "line": 15}, "1": {"loc": {"start": {"line": 34, "column": 2}, "end": {"line": 41, "column": 3}}, "type": "if", "locations": [{"start": {"line": 34, "column": 2}, "end": {"line": 41, "column": 3}}, {"start": {}, "end": {}}], "line": 34}, "2": {"loc": {"start": {"line": 43, "column": 2}, "end": {"line": 49, "column": 3}}, "type": "if", "locations": [{"start": {"line": 43, "column": 2}, "end": {"line": 49, "column": 3}}, {"start": {}, "end": {}}], "line": 43}, "3": {"loc": {"start": {"line": 51, "column": 2}, "end": {"line": 57, "column": 3}}, "type": "if", "locations": [{"start": {"line": 51, "column": 2}, "end": {"line": 57, "column": 3}}, {"start": {}, "end": {}}], "line": 51}, "4": {"loc": {"start": {"line": 67, "column": 7}, "end": {"line": 72, "column": 7}}, "type": "binary-expr", "locations": [{"start": {"line": 67, "column": 7}, "end": {"line": 67, "column": 24}}, {"start": {"line": 68, "column": 8}, "end": {"line": 71, "column": 21}}], "line": 67}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0}, "f": {"0": 0, "1": 0, "2": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0], "3": [0, 0], "4": [0, 0]}}, "/home/<USER>/projects/azmoon-client/app/screens/SessionEdit.tsx": {"path": "/home/<USER>/projects/azmoon-client/app/screens/SessionEdit.tsx", "statementMap": {"0": {"start": {"line": 9, "column": 17}, "end": {"line": 9, "column": 39}}, "1": {"start": {"line": 10, "column": 30}, "end": {"line": 10, "column": 42}}, "2": {"start": {"line": 11, "column": 30}, "end": {"line": 11, "column": 42}}, "3": {"start": {"line": 12, "column": 28}, "end": {"line": 12, "column": 40}}, "4": {"start": {"line": 13, "column": 32}, "end": {"line": 13, "column": 46}}, "5": {"start": {"line": 14, "column": 30}, "end": {"line": 14, "column": 45}}, "6": {"start": {"line": 15, "column": 28}, "end": {"line": 15, "column": 57}}, "7": {"start": {"line": 17, "column": 2}, "end": {"line": 37, "column": 11}}, "8": {"start": {"line": 18, "column": 4}, "end": {"line": 36, "column": 5}}, "9": {"start": {"line": 19, "column": 27}, "end": {"line": 31, "column": 7}}, "10": {"start": {"line": 20, "column": 8}, "end": {"line": 30, "column": 9}}, "11": {"start": {"line": 21, "column": 23}, "end": {"line": 21, "column": 68}}, "12": {"start": {"line": 22, "column": 10}, "end": {"line": 22, "column": 33}}, "13": {"start": {"line": 23, "column": 10}, "end": {"line": 23, "column": 33}}, "14": {"start": {"line": 24, "column": 10}, "end": {"line": 24, "column": 42}}, "15": {"start": {"line": 26, "column": 10}, "end": {"line": 26, "column": 67}}, "16": {"start": {"line": 27, "column": 10}, "end": {"line": 27, "column": 29}}, "17": {"start": {"line": 29, "column": 10}, "end": {"line": 29, "column": 28}}, "18": {"start": {"line": 32, "column": 6}, "end": {"line": 32, "column": 21}}, "19": {"start": {"line": 34, "column": 6}, "end": {"line": 34, "column": 55}}, "20": {"start": {"line": 35, "column": 6}, "end": {"line": 35, "column": 24}}, "21": {"start": {"line": 39, "column": 30}, "end": {"line": 60, "column": 3}}, "22": {"start": {"line": 40, "column": 4}, "end": {"line": 43, "column": 5}}, "23": {"start": {"line": 41, "column": 6}, "end": {"line": 41, "column": 86}}, "24": {"start": {"line": 42, "column": 6}, "end": {"line": 42, "column": 13}}, "25": {"start": {"line": 44, "column": 4}, "end": {"line": 47, "column": 5}}, "26": {"start": {"line": 45, "column": 6}, "end": {"line": 45, "column": 53}}, "27": {"start": {"line": 46, "column": 6}, "end": {"line": 46, "column": 13}}, "28": {"start": {"line": 49, "column": 4}, "end": {"line": 49, "column": 20}}, "29": {"start": {"line": 50, "column": 4}, "end": {"line": 59, "column": 5}}, "30": {"start": {"line": 51, "column": 6}, "end": {"line": 51, "column": 99}}, "31": {"start": {"line": 52, "column": 6}, "end": {"line": 52, "column": 62}}, "32": {"start": {"line": 53, "column": 6}, "end": {"line": 53, "column": 20}}, "33": {"start": {"line": 55, "column": 6}, "end": {"line": 55, "column": 74}}, "34": {"start": {"line": 56, "column": 6}, "end": {"line": 56, "column": 56}}, "35": {"start": {"line": 58, "column": 6}, "end": {"line": 58, "column": 23}}, "36": {"start": {"line": 62, "column": 2}, "end": {"line": 69, "column": 3}}, "37": {"start": {"line": 63, "column": 4}, "end": {"line": 68, "column": 6}}, "38": {"start": {"line": 71, "column": 2}, "end": {"line": 77, "column": 3}}, "39": {"start": {"line": 72, "column": 4}, "end": {"line": 76, "column": 6}}, "40": {"start": {"line": 79, "column": 2}, "end": {"line": 114, "column": 4}}, "41": {"start": {"line": 117, "column": 15}, "end": {"line": 161, "column": 2}}}, "fnMap": {"0": {"name": "SessionEditScreen", "decl": {"start": {"line": 8, "column": 24}, "end": {"line": 8, "column": 41}}, "loc": {"start": {"line": 8, "column": 44}, "end": {"line": 115, "column": 1}}, "line": 8}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 17, "column": 12}, "end": {"line": 17, "column": 13}}, "loc": {"start": {"line": 17, "column": 18}, "end": {"line": 37, "column": 3}}, "line": 17}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 19, "column": 27}, "end": {"line": 19, "column": 28}}, "loc": {"start": {"line": 19, "column": 39}, "end": {"line": 31, "column": 7}}, "line": 19}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 39, "column": 30}, "end": {"line": 39, "column": 31}}, "loc": {"start": {"line": 39, "column": 42}, "end": {"line": 60, "column": 3}}, "line": 39}}, "branchMap": {"0": {"loc": {"start": {"line": 18, "column": 4}, "end": {"line": 36, "column": 5}}, "type": "if", "locations": [{"start": {"line": 18, "column": 4}, "end": {"line": 36, "column": 5}}, {"start": {"line": 33, "column": 11}, "end": {"line": 36, "column": 5}}], "line": 18}, "1": {"loc": {"start": {"line": 40, "column": 4}, "end": {"line": 43, "column": 5}}, "type": "if", "locations": [{"start": {"line": 40, "column": 4}, "end": {"line": 43, "column": 5}}, {"start": {}, "end": {}}], "line": 40}, "2": {"loc": {"start": {"line": 40, "column": 8}, "end": {"line": 40, "column": 57}}, "type": "binary-expr", "locations": [{"start": {"line": 40, "column": 8}, "end": {"line": 40, "column": 22}}, {"start": {"line": 40, "column": 26}, "end": {"line": 40, "column": 40}}, {"start": {"line": 40, "column": 44}, "end": {"line": 40, "column": 57}}], "line": 40}, "3": {"loc": {"start": {"line": 44, "column": 4}, "end": {"line": 47, "column": 5}}, "type": "if", "locations": [{"start": {"line": 44, "column": 4}, "end": {"line": 47, "column": 5}}, {"start": {}, "end": {}}], "line": 44}, "4": {"loc": {"start": {"line": 62, "column": 2}, "end": {"line": 69, "column": 3}}, "type": "if", "locations": [{"start": {"line": 62, "column": 2}, "end": {"line": 69, "column": 3}}, {"start": {}, "end": {}}], "line": 62}, "5": {"loc": {"start": {"line": 71, "column": 2}, "end": {"line": 77, "column": 3}}, "type": "if", "locations": [{"start": {"line": 71, "column": 2}, "end": {"line": 77, "column": 3}}, {"start": {}, "end": {}}], "line": 71}, "6": {"loc": {"start": {"line": 105, "column": 31}, "end": {"line": 105, "column": 62}}, "type": "binary-expr", "locations": [{"start": {"line": 105, "column": 31}, "end": {"line": 105, "column": 37}}, {"start": {"line": 105, "column": 41}, "end": {"line": 105, "column": 62}}], "line": 105}, "7": {"loc": {"start": {"line": 110, "column": 11}, "end": {"line": 110, "column": 49}}, "type": "cond-expr", "locations": [{"start": {"line": 110, "column": 20}, "end": {"line": 110, "column": 31}}, {"start": {"line": 110, "column": 34}, "end": {"line": 110, "column": 49}}], "line": 110}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0, 0], "3": [0, 0], "4": [0, 0], "5": [0, 0], "6": [0, 0], "7": [0, 0]}}, "/home/<USER>/projects/azmoon-client/app/screens/SessionsList.tsx": {"path": "/home/<USER>/projects/azmoon-client/app/screens/SessionsList.tsx", "statementMap": {"0": {"start": {"line": 9, "column": 34}, "end": {"line": 9, "column": 57}}, "1": {"start": {"line": 10, "column": 32}, "end": {"line": 10, "column": 46}}, "2": {"start": {"line": 11, "column": 38}, "end": {"line": 11, "column": 53}}, "3": {"start": {"line": 12, "column": 28}, "end": {"line": 12, "column": 57}}, "4": {"start": {"line": 14, "column": 23}, "end": {"line": 26, "column": 3}}, "5": {"start": {"line": 15, "column": 4}, "end": {"line": 25, "column": 5}}, "6": {"start": {"line": 16, "column": 6}, "end": {"line": 16, "column": 23}}, "7": {"start": {"line": 17, "column": 6}, "end": {"line": 17, "column": 21}}, "8": {"start": {"line": 18, "column": 19}, "end": {"line": 18, "column": 53}}, "9": {"start": {"line": 19, "column": 6}, "end": {"line": 19, "column": 24}}, "10": {"start": {"line": 21, "column": 6}, "end": {"line": 21, "column": 44}}, "11": {"start": {"line": 22, "column": 6}, "end": {"line": 22, "column": 25}}, "12": {"start": {"line": 24, "column": 6}, "end": {"line": 24, "column": 24}}, "13": {"start": {"line": 28, "column": 24}, "end": {"line": 40, "column": 3}}, "14": {"start": {"line": 29, "column": 4}, "end": {"line": 39, "column": 5}}, "15": {"start": {"line": 30, "column": 6}, "end": {"line": 30, "column": 26}}, "16": {"start": {"line": 31, "column": 6}, "end": {"line": 31, "column": 21}}, "17": {"start": {"line": 32, "column": 19}, "end": {"line": 32, "column": 53}}, "18": {"start": {"line": 33, "column": 6}, "end": {"line": 33, "column": 24}}, "19": {"start": {"line": 35, "column": 6}, "end": {"line": 35, "column": 46}}, "20": {"start": {"line": 36, "column": 6}, "end": {"line": 36, "column": 25}}, "21": {"start": {"line": 38, "column": 6}, "end": {"line": 38, "column": 27}}, "22": {"start": {"line": 42, "column": 2}, "end": {"line": 44, "column": 9}}, "23": {"start": {"line": 43, "column": 4}, "end": {"line": 43, "column": 19}}, "24": {"start": {"line": 46, "column": 2}, "end": {"line": 53, "column": 3}}, "25": {"start": {"line": 47, "column": 4}, "end": {"line": 52, "column": 6}}, "26": {"start": {"line": 55, "column": 2}, "end": {"line": 61, "column": 3}}, "27": {"start": {"line": 56, "column": 4}, "end": {"line": 60, "column": 6}}, "28": {"start": {"line": 63, "column": 2}, "end": {"line": 107, "column": 4}}, "29": {"start": {"line": 77, "column": 34}, "end": {"line": 77, "column": 41}}, "30": {"start": {"line": 79, "column": 12}, "end": {"line": 94, "column": 25}}, "31": {"start": {"line": 110, "column": 15}, "end": {"line": 170, "column": 2}}}, "fnMap": {"0": {"name": "SessionsListScreen", "decl": {"start": {"line": 8, "column": 24}, "end": {"line": 8, "column": 42}}, "loc": {"start": {"line": 8, "column": 45}, "end": {"line": 108, "column": 1}}, "line": 8}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 14, "column": 23}, "end": {"line": 14, "column": 24}}, "loc": {"start": {"line": 14, "column": 35}, "end": {"line": 26, "column": 3}}, "line": 14}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 28, "column": 24}, "end": {"line": 28, "column": 25}}, "loc": {"start": {"line": 28, "column": 36}, "end": {"line": 40, "column": 3}}, "line": 28}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 42, "column": 12}, "end": {"line": 42, "column": 13}}, "loc": {"start": {"line": 42, "column": 18}, "end": {"line": 44, "column": 3}}, "line": 42}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 77, "column": 24}, "end": {"line": 77, "column": 25}}, "loc": {"start": {"line": 77, "column": 34}, "end": {"line": 77, "column": 41}}, "line": 77}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 78, "column": 22}, "end": {"line": 78, "column": 23}}, "loc": {"start": {"line": 79, "column": 12}, "end": {"line": 94, "column": 25}}, "line": 79}}, "branchMap": {"0": {"loc": {"start": {"line": 46, "column": 2}, "end": {"line": 53, "column": 3}}, "type": "if", "locations": [{"start": {"line": 46, "column": 2}, "end": {"line": 53, "column": 3}}, {"start": {}, "end": {}}], "line": 46}, "1": {"loc": {"start": {"line": 55, "column": 2}, "end": {"line": 61, "column": 3}}, "type": "if", "locations": [{"start": {"line": 55, "column": 2}, "end": {"line": 61, "column": 3}}, {"start": {}, "end": {}}], "line": 55}, "2": {"loc": {"start": {"line": 71, "column": 7}, "end": {"line": 105, "column": 7}}, "type": "cond-expr", "locations": [{"start": {"line": 72, "column": 8}, "end": {"line": 72, "column": 77}}, {"start": {"line": 74, "column": 8}, "end": {"line": 104, "column": 10}}], "line": 71}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0]}}, "/home/<USER>/projects/azmoon-client/app/screens/Settings.tsx": {"path": "/home/<USER>/projects/azmoon-client/app/screens/Settings.tsx", "statementMap": {"0": {"start": {"line": 21, "column": 22}, "end": {"line": 32, "column": 1}}, "1": {"start": {"line": 22, "column": 2}, "end": {"line": 31, "column": 8}}, "2": {"start": {"line": 35, "column": 22}, "end": {"line": 35, "column": 49}}, "3": {"start": {"line": 36, "column": 44}, "end": {"line": 36, "column": 58}}, "4": {"start": {"line": 37, "column": 34}, "end": {"line": 37, "column": 66}}, "5": {"start": {"line": 38, "column": 34}, "end": {"line": 38, "column": 48}}, "6": {"start": {"line": 40, "column": 21}, "end": {"line": 42, "column": 3}}, "7": {"start": {"line": 41, "column": 4}, "end": {"line": 41, "column": 18}}, "8": {"start": {"line": 44, "column": 27}, "end": {"line": 57, "column": 3}}, "9": {"start": {"line": 45, "column": 4}, "end": {"line": 56, "column": 6}}, "10": {"start": {"line": 53, "column": 25}, "end": {"line": 53, "column": 67}}, "11": {"start": {"line": 59, "column": 22}, "end": {"line": 104, "column": 3}}, "12": {"start": {"line": 72, "column": 4}, "end": {"line": 103, "column": 11}}, "13": {"start": {"line": 106, "column": 2}, "end": {"line": 228, "column": 4}}, "14": {"start": {"line": 231, "column": 15}, "end": {"line": 287, "column": 2}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 21, "column": 22}, "end": {"line": 21, "column": 23}}, "loc": {"start": {"line": 22, "column": 2}, "end": {"line": 31, "column": 8}}, "line": 22}, "1": {"name": "SettingsScreen", "decl": {"start": {"line": 34, "column": 24}, "end": {"line": 34, "column": 38}}, "loc": {"start": {"line": 34, "column": 41}, "end": {"line": 229, "column": 1}}, "line": 34}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 40, "column": 21}, "end": {"line": 40, "column": 22}}, "loc": {"start": {"line": 40, "column": 27}, "end": {"line": 42, "column": 3}}, "line": 40}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 44, "column": 27}, "end": {"line": 44, "column": 28}}, "loc": {"start": {"line": 44, "column": 33}, "end": {"line": 57, "column": 3}}, "line": 44}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 53, "column": 19}, "end": {"line": 53, "column": 20}}, "loc": {"start": {"line": 53, "column": 25}, "end": {"line": 53, "column": 67}}, "line": 53}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 59, "column": 22}, "end": {"line": 59, "column": 23}}, "loc": {"start": {"line": 72, "column": 4}, "end": {"line": 103, "column": 11}}, "line": 72}}, "branchMap": {"0": {"loc": {"start": {"line": 21, "column": 25}, "end": {"line": 21, "column": 42}}, "type": "default-arg", "locations": [{"start": {"line": 21, "column": 33}, "end": {"line": 21, "column": 42}}], "line": 21}, "1": {"loc": {"start": {"line": 21, "column": 44}, "end": {"line": 21, "column": 53}}, "type": "default-arg", "locations": [{"start": {"line": 21, "column": 51}, "end": {"line": 21, "column": 53}}], "line": 21}, "2": {"loc": {"start": {"line": 35, "column": 22}, "end": {"line": 35, "column": 49}}, "type": "binary-expr", "locations": [{"start": {"line": 35, "column": 22}, "end": {"line": 35, "column": 38}}, {"start": {"line": 35, "column": 42}, "end": {"line": 35, "column": 49}}], "line": 35}, "3": {"loc": {"start": {"line": 64, "column": 4}, "end": {"line": 64, "column": 19}}, "type": "default-arg", "locations": [{"start": {"line": 64, "column": 11}, "end": {"line": 64, "column": 19}}], "line": 64}, "4": {"loc": {"start": {"line": 84, "column": 9}, "end": {"line": 93, "column": 9}}, "type": "binary-expr", "locations": [{"start": {"line": 84, "column": 9}, "end": {"line": 84, "column": 17}}, {"start": {"line": 85, "column": 10}, "end": {"line": 92, "column": 17}}], "line": 84}, "5": {"loc": {"start": {"line": 95, "column": 7}, "end": {"line": 102, "column": 7}}, "type": "binary-expr", "locations": [{"start": {"line": 95, "column": 7}, "end": {"line": 95, "column": 24}}, {"start": {"line": 96, "column": 8}, "end": {"line": 101, "column": 10}}], "line": 95}, "6": {"loc": {"start": {"line": 100, "column": 22}, "end": {"line": 100, "column": 51}}, "type": "cond-expr", "locations": [{"start": {"line": 100, "column": 30}, "end": {"line": 100, "column": 39}}, {"start": {"line": 100, "column": 42}, "end": {"line": 100, "column": 51}}], "line": 100}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0}, "b": {"0": [0], "1": [0], "2": [0, 0], "3": [0], "4": [0, 0], "5": [0, 0], "6": [0, 0]}}, "/home/<USER>/projects/azmoon-client/app/screens/UserAdd.tsx": {"path": "/home/<USER>/projects/azmoon-client/app/screens/UserAdd.tsx", "statementMap": {"0": {"start": {"line": 9, "column": 28}, "end": {"line": 9, "column": 40}}, "1": {"start": {"line": 10, "column": 30}, "end": {"line": 10, "column": 42}}, "2": {"start": {"line": 11, "column": 42}, "end": {"line": 11, "column": 54}}, "3": {"start": {"line": 12, "column": 36}, "end": {"line": 12, "column": 48}}, "4": {"start": {"line": 13, "column": 34}, "end": {"line": 13, "column": 46}}, "5": {"start": {"line": 14, "column": 26}, "end": {"line": 14, "column": 38}}, "6": {"start": {"line": 15, "column": 32}, "end": {"line": 15, "column": 47}}, "7": {"start": {"line": 17, "column": 24}, "end": {"line": 41, "column": 3}}, "8": {"start": {"line": 18, "column": 4}, "end": {"line": 21, "column": 5}}, "9": {"start": {"line": 19, "column": 6}, "end": {"line": 19, "column": 66}}, "10": {"start": {"line": 20, "column": 6}, "end": {"line": 20, "column": 13}}, "11": {"start": {"line": 23, "column": 4}, "end": {"line": 23, "column": 21}}, "12": {"start": {"line": 24, "column": 4}, "end": {"line": 40, "column": 5}}, "13": {"start": {"line": 25, "column": 6}, "end": {"line": 32, "column": 9}}, "14": {"start": {"line": 33, "column": 6}, "end": {"line": 33, "column": 57}}, "15": {"start": {"line": 34, "column": 6}, "end": {"line": 34, "column": 20}}, "16": {"start": {"line": 36, "column": 6}, "end": {"line": 36, "column": 68}}, "17": {"start": {"line": 37, "column": 6}, "end": {"line": 37, "column": 50}}, "18": {"start": {"line": 39, "column": 6}, "end": {"line": 39, "column": 24}}, "19": {"start": {"line": 43, "column": 2}, "end": {"line": 102, "column": 4}}, "20": {"start": {"line": 105, "column": 15}, "end": {"line": 140, "column": 2}}}, "fnMap": {"0": {"name": "UserAddScreen", "decl": {"start": {"line": 8, "column": 24}, "end": {"line": 8, "column": 37}}, "loc": {"start": {"line": 8, "column": 40}, "end": {"line": 103, "column": 1}}, "line": 8}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 17, "column": 24}, "end": {"line": 17, "column": 25}}, "loc": {"start": {"line": 17, "column": 36}, "end": {"line": 41, "column": 3}}, "line": 17}}, "branchMap": {"0": {"loc": {"start": {"line": 18, "column": 4}, "end": {"line": 21, "column": 5}}, "type": "if", "locations": [{"start": {"line": 18, "column": 4}, "end": {"line": 21, "column": 5}}, {"start": {}, "end": {}}], "line": 18}, "1": {"loc": {"start": {"line": 18, "column": 8}, "end": {"line": 18, "column": 120}}, "type": "binary-expr", "locations": [{"start": {"line": 18, "column": 8}, "end": {"line": 18, "column": 21}}, {"start": {"line": 18, "column": 25}, "end": {"line": 18, "column": 39}}, {"start": {"line": 18, "column": 43}, "end": {"line": 18, "column": 63}}, {"start": {"line": 18, "column": 67}, "end": {"line": 18, "column": 84}}, {"start": {"line": 18, "column": 88}, "end": {"line": 18, "column": 104}}, {"start": {"line": 18, "column": 108}, "end": {"line": 18, "column": 120}}], "line": 18}, "2": {"loc": {"start": {"line": 93, "column": 31}, "end": {"line": 93, "column": 63}}, "type": "binary-expr", "locations": [{"start": {"line": 93, "column": 31}, "end": {"line": 93, "column": 38}}, {"start": {"line": 93, "column": 42}, "end": {"line": 93, "column": 63}}], "line": 93}, "3": {"loc": {"start": {"line": 98, "column": 11}, "end": {"line": 98, "column": 45}}, "type": "cond-expr", "locations": [{"start": {"line": 98, "column": 21}, "end": {"line": 98, "column": 32}}, {"start": {"line": 98, "column": 35}, "end": {"line": 98, "column": 45}}], "line": 98}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0}, "f": {"0": 0, "1": 0}, "b": {"0": [0, 0], "1": [0, 0, 0, 0, 0, 0], "2": [0, 0], "3": [0, 0]}}, "/home/<USER>/projects/azmoon-client/app/screens/UserDetails.tsx": {"path": "/home/<USER>/projects/azmoon-client/app/screens/UserDetails.tsx", "statementMap": {"0": {"start": {"line": 9, "column": 17}, "end": {"line": 9, "column": 39}}, "1": {"start": {"line": 10, "column": 26}, "end": {"line": 10, "column": 53}}, "2": {"start": {"line": 11, "column": 32}, "end": {"line": 11, "column": 46}}, "3": {"start": {"line": 12, "column": 28}, "end": {"line": 12, "column": 57}}, "4": {"start": {"line": 14, "column": 2}, "end": {"line": 32, "column": 11}}, "5": {"start": {"line": 15, "column": 4}, "end": {"line": 31, "column": 5}}, "6": {"start": {"line": 16, "column": 24}, "end": {"line": 26, "column": 7}}, "7": {"start": {"line": 17, "column": 8}, "end": {"line": 25, "column": 9}}, "8": {"start": {"line": 18, "column": 23}, "end": {"line": 18, "column": 62}}, "9": {"start": {"line": 19, "column": 10}, "end": {"line": 19, "column": 24}}, "10": {"start": {"line": 21, "column": 10}, "end": {"line": 21, "column": 52}}, "11": {"start": {"line": 22, "column": 10}, "end": {"line": 22, "column": 29}}, "12": {"start": {"line": 24, "column": 10}, "end": {"line": 24, "column": 28}}, "13": {"start": {"line": 27, "column": 6}, "end": {"line": 27, "column": 18}}, "14": {"start": {"line": 29, "column": 6}, "end": {"line": 29, "column": 40}}, "15": {"start": {"line": 30, "column": 6}, "end": {"line": 30, "column": 24}}, "16": {"start": {"line": 34, "column": 2}, "end": {"line": 41, "column": 3}}, "17": {"start": {"line": 35, "column": 4}, "end": {"line": 40, "column": 6}}, "18": {"start": {"line": 43, "column": 2}, "end": {"line": 49, "column": 3}}, "19": {"start": {"line": 44, "column": 4}, "end": {"line": 48, "column": 6}}, "20": {"start": {"line": 51, "column": 2}, "end": {"line": 57, "column": 3}}, "21": {"start": {"line": 52, "column": 4}, "end": {"line": 56, "column": 6}}, "22": {"start": {"line": 59, "column": 2}, "end": {"line": 74, "column": 4}}, "23": {"start": {"line": 77, "column": 15}, "end": {"line": 104, "column": 2}}}, "fnMap": {"0": {"name": "UserDetailsScreen", "decl": {"start": {"line": 8, "column": 24}, "end": {"line": 8, "column": 41}}, "loc": {"start": {"line": 8, "column": 44}, "end": {"line": 75, "column": 1}}, "line": 8}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 14, "column": 12}, "end": {"line": 14, "column": 13}}, "loc": {"start": {"line": 14, "column": 18}, "end": {"line": 32, "column": 3}}, "line": 14}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 16, "column": 24}, "end": {"line": 16, "column": 25}}, "loc": {"start": {"line": 16, "column": 36}, "end": {"line": 26, "column": 7}}, "line": 16}}, "branchMap": {"0": {"loc": {"start": {"line": 15, "column": 4}, "end": {"line": 31, "column": 5}}, "type": "if", "locations": [{"start": {"line": 15, "column": 4}, "end": {"line": 31, "column": 5}}, {"start": {"line": 28, "column": 11}, "end": {"line": 31, "column": 5}}], "line": 15}, "1": {"loc": {"start": {"line": 34, "column": 2}, "end": {"line": 41, "column": 3}}, "type": "if", "locations": [{"start": {"line": 34, "column": 2}, "end": {"line": 41, "column": 3}}, {"start": {}, "end": {}}], "line": 34}, "2": {"loc": {"start": {"line": 43, "column": 2}, "end": {"line": 49, "column": 3}}, "type": "if", "locations": [{"start": {"line": 43, "column": 2}, "end": {"line": 49, "column": 3}}, {"start": {}, "end": {}}], "line": 43}, "3": {"loc": {"start": {"line": 51, "column": 2}, "end": {"line": 57, "column": 3}}, "type": "if", "locations": [{"start": {"line": 51, "column": 2}, "end": {"line": 57, "column": 3}}, {"start": {}, "end": {}}], "line": 51}, "4": {"loc": {"start": {"line": 67, "column": 7}, "end": {"line": 72, "column": 7}}, "type": "binary-expr", "locations": [{"start": {"line": 67, "column": 7}, "end": {"line": 67, "column": 21}}, {"start": {"line": 68, "column": 8}, "end": {"line": 71, "column": 21}}], "line": 67}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0}, "f": {"0": 0, "1": 0, "2": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0], "3": [0, 0], "4": [0, 0]}}, "/home/<USER>/projects/azmoon-client/app/screens/UserEdit.tsx": {"path": "/home/<USER>/projects/azmoon-client/app/screens/UserEdit.tsx", "statementMap": {"0": {"start": {"line": 9, "column": 17}, "end": {"line": 9, "column": 39}}, "1": {"start": {"line": 10, "column": 28}, "end": {"line": 10, "column": 40}}, "2": {"start": {"line": 11, "column": 30}, "end": {"line": 11, "column": 42}}, "3": {"start": {"line": 12, "column": 42}, "end": {"line": 12, "column": 54}}, "4": {"start": {"line": 13, "column": 36}, "end": {"line": 13, "column": 48}}, "5": {"start": {"line": 14, "column": 34}, "end": {"line": 14, "column": 46}}, "6": {"start": {"line": 15, "column": 26}, "end": {"line": 15, "column": 38}}, "7": {"start": {"line": 16, "column": 32}, "end": {"line": 16, "column": 46}}, "8": {"start": {"line": 17, "column": 30}, "end": {"line": 17, "column": 45}}, "9": {"start": {"line": 18, "column": 28}, "end": {"line": 18, "column": 57}}, "10": {"start": {"line": 20, "column": 2}, "end": {"line": 44, "column": 11}}, "11": {"start": {"line": 21, "column": 4}, "end": {"line": 43, "column": 5}}, "12": {"start": {"line": 22, "column": 24}, "end": {"line": 38, "column": 7}}, "13": {"start": {"line": 23, "column": 8}, "end": {"line": 37, "column": 9}}, "14": {"start": {"line": 24, "column": 23}, "end": {"line": 24, "column": 62}}, "15": {"start": {"line": 25, "column": 10}, "end": {"line": 25, "column": 31}}, "16": {"start": {"line": 26, "column": 10}, "end": {"line": 26, "column": 33}}, "17": {"start": {"line": 29, "column": 10}, "end": {"line": 29, "column": 40}}, "18": {"start": {"line": 30, "column": 10}, "end": {"line": 30, "column": 38}}, "19": {"start": {"line": 31, "column": 10}, "end": {"line": 31, "column": 29}}, "20": {"start": {"line": 33, "column": 10}, "end": {"line": 33, "column": 64}}, "21": {"start": {"line": 34, "column": 10}, "end": {"line": 34, "column": 29}}, "22": {"start": {"line": 36, "column": 10}, "end": {"line": 36, "column": 28}}, "23": {"start": {"line": 39, "column": 6}, "end": {"line": 39, "column": 18}}, "24": {"start": {"line": 41, "column": 6}, "end": {"line": 41, "column": 52}}, "25": {"start": {"line": 42, "column": 6}, "end": {"line": 42, "column": 24}}, "26": {"start": {"line": 46, "column": 27}, "end": {"line": 78, "column": 3}}, "27": {"start": {"line": 47, "column": 4}, "end": {"line": 50, "column": 5}}, "28": {"start": {"line": 48, "column": 6}, "end": {"line": 48, "column": 87}}, "29": {"start": {"line": 49, "column": 6}, "end": {"line": 49, "column": 13}}, "30": {"start": {"line": 51, "column": 4}, "end": {"line": 54, "column": 5}}, "31": {"start": {"line": 52, "column": 6}, "end": {"line": 52, "column": 50}}, "32": {"start": {"line": 53, "column": 6}, "end": {"line": 53, "column": 13}}, "33": {"start": {"line": 56, "column": 4}, "end": {"line": 56, "column": 20}}, "34": {"start": {"line": 57, "column": 4}, "end": {"line": 77, "column": 5}}, "35": {"start": {"line": 58, "column": 38}, "end": {"line": 64, "column": 7}}, "36": {"start": {"line": 65, "column": 6}, "end": {"line": 67, "column": 7}}, "37": {"start": {"line": 66, "column": 8}, "end": {"line": 66, "column": 46}}, "38": {"start": {"line": 69, "column": 6}, "end": {"line": 69, "column": 59}}, "39": {"start": {"line": 70, "column": 6}, "end": {"line": 70, "column": 59}}, "40": {"start": {"line": 71, "column": 6}, "end": {"line": 71, "column": 20}}, "41": {"start": {"line": 73, "column": 6}, "end": {"line": 73, "column": 71}}, "42": {"start": {"line": 74, "column": 6}, "end": {"line": 74, "column": 53}}, "43": {"start": {"line": 76, "column": 6}, "end": {"line": 76, "column": 23}}, "44": {"start": {"line": 80, "column": 2}, "end": {"line": 87, "column": 3}}, "45": {"start": {"line": 81, "column": 4}, "end": {"line": 86, "column": 6}}, "46": {"start": {"line": 89, "column": 2}, "end": {"line": 95, "column": 3}}, "47": {"start": {"line": 90, "column": 4}, "end": {"line": 94, "column": 6}}, "48": {"start": {"line": 97, "column": 2}, "end": {"line": 156, "column": 4}}, "49": {"start": {"line": 159, "column": 15}, "end": {"line": 203, "column": 2}}}, "fnMap": {"0": {"name": "UserEditScreen", "decl": {"start": {"line": 8, "column": 24}, "end": {"line": 8, "column": 38}}, "loc": {"start": {"line": 8, "column": 41}, "end": {"line": 157, "column": 1}}, "line": 8}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 20, "column": 12}, "end": {"line": 20, "column": 13}}, "loc": {"start": {"line": 20, "column": 18}, "end": {"line": 44, "column": 3}}, "line": 20}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 22, "column": 24}, "end": {"line": 22, "column": 25}}, "loc": {"start": {"line": 22, "column": 36}, "end": {"line": 38, "column": 7}}, "line": 22}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 46, "column": 27}, "end": {"line": 46, "column": 28}}, "loc": {"start": {"line": 46, "column": 39}, "end": {"line": 78, "column": 3}}, "line": 46}}, "branchMap": {"0": {"loc": {"start": {"line": 21, "column": 4}, "end": {"line": 43, "column": 5}}, "type": "if", "locations": [{"start": {"line": 21, "column": 4}, "end": {"line": 43, "column": 5}}, {"start": {"line": 40, "column": 11}, "end": {"line": 43, "column": 5}}], "line": 21}, "1": {"loc": {"start": {"line": 47, "column": 4}, "end": {"line": 50, "column": 5}}, "type": "if", "locations": [{"start": {"line": 47, "column": 4}, "end": {"line": 50, "column": 5}}, {"start": {}, "end": {}}], "line": 47}, "2": {"loc": {"start": {"line": 47, "column": 8}, "end": {"line": 47, "column": 96}}, "type": "binary-expr", "locations": [{"start": {"line": 47, "column": 8}, "end": {"line": 47, "column": 21}}, {"start": {"line": 47, "column": 25}, "end": {"line": 47, "column": 39}}, {"start": {"line": 47, "column": 43}, "end": {"line": 47, "column": 60}}, {"start": {"line": 47, "column": 64}, "end": {"line": 47, "column": 80}}, {"start": {"line": 47, "column": 84}, "end": {"line": 47, "column": 96}}], "line": 47}, "3": {"loc": {"start": {"line": 51, "column": 4}, "end": {"line": 54, "column": 5}}, "type": "if", "locations": [{"start": {"line": 51, "column": 4}, "end": {"line": 54, "column": 5}}, {"start": {}, "end": {}}], "line": 51}, "4": {"loc": {"start": {"line": 65, "column": 6}, "end": {"line": 67, "column": 7}}, "type": "if", "locations": [{"start": {"line": 65, "column": 6}, "end": {"line": 67, "column": 7}}, {"start": {}, "end": {}}], "line": 65}, "5": {"loc": {"start": {"line": 80, "column": 2}, "end": {"line": 87, "column": 3}}, "type": "if", "locations": [{"start": {"line": 80, "column": 2}, "end": {"line": 87, "column": 3}}, {"start": {}, "end": {}}], "line": 80}, "6": {"loc": {"start": {"line": 89, "column": 2}, "end": {"line": 95, "column": 3}}, "type": "if", "locations": [{"start": {"line": 89, "column": 2}, "end": {"line": 95, "column": 3}}, {"start": {}, "end": {}}], "line": 89}, "7": {"loc": {"start": {"line": 147, "column": 31}, "end": {"line": 147, "column": 62}}, "type": "binary-expr", "locations": [{"start": {"line": 147, "column": 31}, "end": {"line": 147, "column": 37}}, {"start": {"line": 147, "column": 41}, "end": {"line": 147, "column": 62}}], "line": 147}, "8": {"loc": {"start": {"line": 152, "column": 11}, "end": {"line": 152, "column": 49}}, "type": "cond-expr", "locations": [{"start": {"line": 152, "column": 20}, "end": {"line": 152, "column": 31}}, {"start": {"line": 152, "column": 34}, "end": {"line": 152, "column": 49}}], "line": 152}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0, 0, 0, 0], "3": [0, 0], "4": [0, 0], "5": [0, 0], "6": [0, 0], "7": [0, 0], "8": [0, 0]}}, "/home/<USER>/projects/azmoon-client/app/screens/UsersList.tsx": {"path": "/home/<USER>/projects/azmoon-client/app/screens/UsersList.tsx", "statementMap": {"0": {"start": {"line": 9, "column": 28}, "end": {"line": 9, "column": 48}}, "1": {"start": {"line": 10, "column": 32}, "end": {"line": 10, "column": 46}}, "2": {"start": {"line": 11, "column": 38}, "end": {"line": 11, "column": 53}}, "3": {"start": {"line": 12, "column": 28}, "end": {"line": 12, "column": 57}}, "4": {"start": {"line": 14, "column": 20}, "end": {"line": 26, "column": 3}}, "5": {"start": {"line": 15, "column": 4}, "end": {"line": 25, "column": 5}}, "6": {"start": {"line": 16, "column": 6}, "end": {"line": 16, "column": 23}}, "7": {"start": {"line": 17, "column": 6}, "end": {"line": 17, "column": 21}}, "8": {"start": {"line": 18, "column": 19}, "end": {"line": 18, "column": 47}}, "9": {"start": {"line": 19, "column": 6}, "end": {"line": 19, "column": 21}}, "10": {"start": {"line": 21, "column": 6}, "end": {"line": 21, "column": 41}}, "11": {"start": {"line": 22, "column": 6}, "end": {"line": 22, "column": 25}}, "12": {"start": {"line": 24, "column": 6}, "end": {"line": 24, "column": 24}}, "13": {"start": {"line": 28, "column": 24}, "end": {"line": 40, "column": 3}}, "14": {"start": {"line": 29, "column": 4}, "end": {"line": 39, "column": 5}}, "15": {"start": {"line": 30, "column": 6}, "end": {"line": 30, "column": 26}}, "16": {"start": {"line": 31, "column": 6}, "end": {"line": 31, "column": 21}}, "17": {"start": {"line": 32, "column": 19}, "end": {"line": 32, "column": 47}}, "18": {"start": {"line": 33, "column": 6}, "end": {"line": 33, "column": 21}}, "19": {"start": {"line": 35, "column": 6}, "end": {"line": 35, "column": 43}}, "20": {"start": {"line": 36, "column": 6}, "end": {"line": 36, "column": 25}}, "21": {"start": {"line": 38, "column": 6}, "end": {"line": 38, "column": 27}}, "22": {"start": {"line": 42, "column": 2}, "end": {"line": 44, "column": 9}}, "23": {"start": {"line": 43, "column": 4}, "end": {"line": 43, "column": 16}}, "24": {"start": {"line": 46, "column": 27}, "end": {"line": 72, "column": 3}}, "25": {"start": {"line": 47, "column": 4}, "end": {"line": 71, "column": 6}}, "26": {"start": {"line": 58, "column": 12}, "end": {"line": 65, "column": 13}}, "27": {"start": {"line": 59, "column": 14}, "end": {"line": 59, "column": 47}}, "28": {"start": {"line": 60, "column": 14}, "end": {"line": 60, "column": 67}}, "29": {"start": {"line": 61, "column": 14}, "end": {"line": 61, "column": 26}}, "30": {"start": {"line": 63, "column": 14}, "end": {"line": 63, "column": 79}}, "31": {"start": {"line": 64, "column": 14}, "end": {"line": 64, "column": 61}}, "32": {"start": {"line": 74, "column": 2}, "end": {"line": 81, "column": 3}}, "33": {"start": {"line": 75, "column": 4}, "end": {"line": 80, "column": 6}}, "34": {"start": {"line": 83, "column": 2}, "end": {"line": 89, "column": 3}}, "35": {"start": {"line": 84, "column": 4}, "end": {"line": 88, "column": 6}}, "36": {"start": {"line": 91, "column": 2}, "end": {"line": 140, "column": 4}}, "37": {"start": {"line": 105, "column": 34}, "end": {"line": 105, "column": 41}}, "38": {"start": {"line": 107, "column": 12}, "end": {"line": 127, "column": 25}}, "39": {"start": {"line": 108, "column": 40}, "end": {"line": 108, "column": 81}}, "40": {"start": {"line": 122, "column": 33}, "end": {"line": 122, "column": 58}}, "41": {"start": {"line": 143, "column": 15}, "end": {"line": 206, "column": 2}}}, "fnMap": {"0": {"name": "UsersListScreen", "decl": {"start": {"line": 8, "column": 24}, "end": {"line": 8, "column": 39}}, "loc": {"start": {"line": 8, "column": 42}, "end": {"line": 141, "column": 1}}, "line": 8}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 14, "column": 20}, "end": {"line": 14, "column": 21}}, "loc": {"start": {"line": 14, "column": 32}, "end": {"line": 26, "column": 3}}, "line": 14}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 28, "column": 24}, "end": {"line": 28, "column": 25}}, "loc": {"start": {"line": 28, "column": 36}, "end": {"line": 40, "column": 3}}, "line": 28}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 42, "column": 12}, "end": {"line": 42, "column": 13}}, "loc": {"start": {"line": 42, "column": 18}, "end": {"line": 44, "column": 3}}, "line": 42}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 46, "column": 27}, "end": {"line": 46, "column": 28}}, "loc": {"start": {"line": 46, "column": 49}, "end": {"line": 72, "column": 3}}, "line": 46}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 57, "column": 19}, "end": {"line": 57, "column": 20}}, "loc": {"start": {"line": 57, "column": 31}, "end": {"line": 66, "column": 11}}, "line": 57}, "6": {"name": "(anonymous_6)", "decl": {"start": {"line": 105, "column": 24}, "end": {"line": 105, "column": 25}}, "loc": {"start": {"line": 105, "column": 34}, "end": {"line": 105, "column": 41}}, "line": 105}, "7": {"name": "(anonymous_7)", "decl": {"start": {"line": 106, "column": 22}, "end": {"line": 106, "column": 23}}, "loc": {"start": {"line": 107, "column": 12}, "end": {"line": 127, "column": 25}}, "line": 107}, "8": {"name": "(anonymous_8)", "decl": {"start": {"line": 108, "column": 34}, "end": {"line": 108, "column": 35}}, "loc": {"start": {"line": 108, "column": 40}, "end": {"line": 108, "column": 81}}, "line": 108}, "9": {"name": "(anonymous_9)", "decl": {"start": {"line": 122, "column": 27}, "end": {"line": 122, "column": 28}}, "loc": {"start": {"line": 122, "column": 33}, "end": {"line": 122, "column": 58}}, "line": 122}}, "branchMap": {"0": {"loc": {"start": {"line": 74, "column": 2}, "end": {"line": 81, "column": 3}}, "type": "if", "locations": [{"start": {"line": 74, "column": 2}, "end": {"line": 81, "column": 3}}, {"start": {}, "end": {}}], "line": 74}, "1": {"loc": {"start": {"line": 83, "column": 2}, "end": {"line": 89, "column": 3}}, "type": "if", "locations": [{"start": {"line": 83, "column": 2}, "end": {"line": 89, "column": 3}}, {"start": {}, "end": {}}], "line": 83}, "2": {"loc": {"start": {"line": 99, "column": 7}, "end": {"line": 138, "column": 7}}, "type": "cond-expr", "locations": [{"start": {"line": 100, "column": 8}, "end": {"line": 100, "column": 74}}, {"start": {"line": 102, "column": 8}, "end": {"line": 137, "column": 10}}], "line": 99}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0]}}, "/home/<USER>/projects/azmoon-client/components/Collapsible.tsx": {"path": "/home/<USER>/projects/azmoon-client/components/Collapsible.tsx", "statementMap": {"0": {"start": {"line": 11, "column": 30}, "end": {"line": 11, "column": 45}}, "1": {"start": {"line": 12, "column": 16}, "end": {"line": 12, "column": 43}}, "2": {"start": {"line": 14, "column": 2}, "end": {"line": 32, "column": 4}}, "3": {"start": {"line": 18, "column": 23}, "end": {"line": 18, "column": 51}}, "4": {"start": {"line": 18, "column": 44}, "end": {"line": 18, "column": 50}}, "5": {"start": {"line": 35, "column": 15}, "end": {"line": 45, "column": 2}}}, "fnMap": {"0": {"name": "Collapsible", "decl": {"start": {"line": 10, "column": 16}, "end": {"line": 10, "column": 27}}, "loc": {"start": {"line": 10, "column": 88}, "end": {"line": 33, "column": 1}}, "line": 10}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 18, "column": 17}, "end": {"line": 18, "column": 18}}, "loc": {"start": {"line": 18, "column": 23}, "end": {"line": 18, "column": 51}}, "line": 18}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 18, "column": 33}, "end": {"line": 18, "column": 34}}, "loc": {"start": {"line": 18, "column": 44}, "end": {"line": 18, "column": 50}}, "line": 18}}, "branchMap": {"0": {"loc": {"start": {"line": 12, "column": 16}, "end": {"line": 12, "column": 43}}, "type": "binary-expr", "locations": [{"start": {"line": 12, "column": 16}, "end": {"line": 12, "column": 32}}, {"start": {"line": 12, "column": 36}, "end": {"line": 12, "column": 43}}], "line": 12}, "1": {"loc": {"start": {"line": 24, "column": 17}, "end": {"line": 24, "column": 73}}, "type": "cond-expr", "locations": [{"start": {"line": 24, "column": 37}, "end": {"line": 24, "column": 54}}, {"start": {"line": 24, "column": 57}, "end": {"line": 24, "column": 73}}], "line": 24}, "2": {"loc": {"start": {"line": 25, "column": 41}, "end": {"line": 25, "column": 66}}, "type": "cond-expr", "locations": [{"start": {"line": 25, "column": 50}, "end": {"line": 25, "column": 57}}, {"start": {"line": 25, "column": 60}, "end": {"line": 25, "column": 66}}], "line": 25}, "3": {"loc": {"start": {"line": 30, "column": 7}, "end": {"line": 30, "column": 75}}, "type": "binary-expr", "locations": [{"start": {"line": 30, "column": 7}, "end": {"line": 30, "column": 13}}, {"start": {"line": 30, "column": 17}, "end": {"line": 30, "column": 75}}], "line": 30}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0}, "f": {"0": 0, "1": 0, "2": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0], "3": [0, 0]}}, "/home/<USER>/projects/azmoon-client/components/CustomDrawerContent.tsx": {"path": "/home/<USER>/projects/azmoon-client/components/CustomDrawerContent.tsx", "statementMap": {"0": {"start": {"line": 24, "column": 17}, "end": {"line": 36, "column": 1}}, "1": {"start": {"line": 25, "column": 2}, "end": {"line": 35, "column": 8}}, "2": {"start": {"line": 38, "column": 21}, "end": {"line": 50, "column": 1}}, "3": {"start": {"line": 39, "column": 2}, "end": {"line": 49, "column": 8}}, "4": {"start": {"line": 52, "column": 17}, "end": {"line": 64, "column": 1}}, "5": {"start": {"line": 53, "column": 2}, "end": {"line": 63, "column": 8}}, "6": {"start": {"line": 66, "column": 17}, "end": {"line": 79, "column": 1}}, "7": {"start": {"line": 67, "column": 2}, "end": {"line": 78, "column": 8}}, "8": {"start": {"line": 81, "column": 19}, "end": {"line": 94, "column": 1}}, "9": {"start": {"line": 82, "column": 2}, "end": {"line": 93, "column": 8}}, "10": {"start": {"line": 104, "column": 46}, "end": {"line": 130, "column": 1}}, "11": {"start": {"line": 111, "column": 2}, "end": {"line": 129, "column": 21}}, "12": {"start": {"line": 133, "column": 22}, "end": {"line": 133, "column": 49}}, "13": {"start": {"line": 134, "column": 17}, "end": {"line": 134, "column": 36}}, "14": {"start": {"line": 136, "column": 23}, "end": {"line": 155, "column": 3}}, "15": {"start": {"line": 137, "column": 4}, "end": {"line": 154, "column": 6}}, "16": {"start": {"line": 150, "column": 12}, "end": {"line": 150, "column": 55}}, "17": {"start": {"line": 157, "column": 2}, "end": {"line": 307, "column": 4}}, "18": {"start": {"line": 201, "column": 25}, "end": {"line": 201, "column": 56}}, "19": {"start": {"line": 207, "column": 25}, "end": {"line": 207, "column": 59}}, "20": {"start": {"line": 213, "column": 25}, "end": {"line": 213, "column": 60}}, "21": {"start": {"line": 225, "column": 25}, "end": {"line": 225, "column": 65}}, "22": {"start": {"line": 231, "column": 25}, "end": {"line": 231, "column": 62}}, "23": {"start": {"line": 237, "column": 25}, "end": {"line": 237, "column": 60}}, "24": {"start": {"line": 243, "column": 25}, "end": {"line": 243, "column": 58}}, "25": {"start": {"line": 249, "column": 25}, "end": {"line": 249, "column": 61}}, "26": {"start": {"line": 264, "column": 25}, "end": {"line": 264, "column": 56}}, "27": {"start": {"line": 271, "column": 25}, "end": {"line": 271, "column": 57}}, "28": {"start": {"line": 278, "column": 25}, "end": {"line": 278, "column": 54}}, "29": {"start": {"line": 310, "column": 15}, "end": {"line": 382, "column": 2}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 24, "column": 17}, "end": {"line": 24, "column": 18}}, "loc": {"start": {"line": 25, "column": 2}, "end": {"line": 35, "column": 8}}, "line": 25}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 38, "column": 21}, "end": {"line": 38, "column": 22}}, "loc": {"start": {"line": 39, "column": 2}, "end": {"line": 49, "column": 8}}, "line": 39}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 52, "column": 17}, "end": {"line": 52, "column": 18}}, "loc": {"start": {"line": 53, "column": 2}, "end": {"line": 63, "column": 8}}, "line": 53}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 66, "column": 17}, "end": {"line": 66, "column": 18}}, "loc": {"start": {"line": 67, "column": 2}, "end": {"line": 78, "column": 8}}, "line": 67}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 81, "column": 19}, "end": {"line": 81, "column": 20}}, "loc": {"start": {"line": 82, "column": 2}, "end": {"line": 93, "column": 8}}, "line": 82}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 104, "column": 46}, "end": {"line": 104, "column": 47}}, "loc": {"start": {"line": 111, "column": 2}, "end": {"line": 129, "column": 21}}, "line": 111}, "6": {"name": "CustomDrawerContent", "decl": {"start": {"line": 132, "column": 16}, "end": {"line": 132, "column": 35}}, "loc": {"start": {"line": 132, "column": 72}, "end": {"line": 308, "column": 1}}, "line": 132}, "7": {"name": "(anonymous_7)", "decl": {"start": {"line": 136, "column": 23}, "end": {"line": 136, "column": 24}}, "loc": {"start": {"line": 136, "column": 29}, "end": {"line": 155, "column": 3}}, "line": 136}, "8": {"name": "(anonymous_8)", "decl": {"start": {"line": 148, "column": 19}, "end": {"line": 148, "column": 20}}, "loc": {"start": {"line": 148, "column": 25}, "end": {"line": 151, "column": 11}}, "line": 148}, "9": {"name": "(anonymous_9)", "decl": {"start": {"line": 201, "column": 19}, "end": {"line": 201, "column": 20}}, "loc": {"start": {"line": 201, "column": 25}, "end": {"line": 201, "column": 56}}, "line": 201}, "10": {"name": "(anonymous_10)", "decl": {"start": {"line": 207, "column": 19}, "end": {"line": 207, "column": 20}}, "loc": {"start": {"line": 207, "column": 25}, "end": {"line": 207, "column": 59}}, "line": 207}, "11": {"name": "(anonymous_11)", "decl": {"start": {"line": 213, "column": 19}, "end": {"line": 213, "column": 20}}, "loc": {"start": {"line": 213, "column": 25}, "end": {"line": 213, "column": 60}}, "line": 213}, "12": {"name": "(anonymous_12)", "decl": {"start": {"line": 225, "column": 19}, "end": {"line": 225, "column": 20}}, "loc": {"start": {"line": 225, "column": 25}, "end": {"line": 225, "column": 65}}, "line": 225}, "13": {"name": "(anonymous_13)", "decl": {"start": {"line": 231, "column": 19}, "end": {"line": 231, "column": 20}}, "loc": {"start": {"line": 231, "column": 25}, "end": {"line": 231, "column": 62}}, "line": 231}, "14": {"name": "(anonymous_14)", "decl": {"start": {"line": 237, "column": 19}, "end": {"line": 237, "column": 20}}, "loc": {"start": {"line": 237, "column": 25}, "end": {"line": 237, "column": 60}}, "line": 237}, "15": {"name": "(anonymous_15)", "decl": {"start": {"line": 243, "column": 19}, "end": {"line": 243, "column": 20}}, "loc": {"start": {"line": 243, "column": 25}, "end": {"line": 243, "column": 58}}, "line": 243}, "16": {"name": "(anonymous_16)", "decl": {"start": {"line": 249, "column": 19}, "end": {"line": 249, "column": 20}}, "loc": {"start": {"line": 249, "column": 25}, "end": {"line": 249, "column": 61}}, "line": 249}, "17": {"name": "(anonymous_17)", "decl": {"start": {"line": 264, "column": 19}, "end": {"line": 264, "column": 20}}, "loc": {"start": {"line": 264, "column": 25}, "end": {"line": 264, "column": 56}}, "line": 264}, "18": {"name": "(anonymous_18)", "decl": {"start": {"line": 271, "column": 19}, "end": {"line": 271, "column": 20}}, "loc": {"start": {"line": 271, "column": 25}, "end": {"line": 271, "column": 57}}, "line": 271}, "19": {"name": "(anonymous_19)", "decl": {"start": {"line": 278, "column": 19}, "end": {"line": 278, "column": 20}}, "loc": {"start": {"line": 278, "column": 25}, "end": {"line": 278, "column": 54}}, "line": 278}}, "branchMap": {"0": {"loc": {"start": {"line": 24, "column": 20}, "end": {"line": 24, "column": 37}}, "type": "default-arg", "locations": [{"start": {"line": 24, "column": 28}, "end": {"line": 24, "column": 37}}], "line": 24}, "1": {"loc": {"start": {"line": 24, "column": 39}, "end": {"line": 24, "column": 48}}, "type": "default-arg", "locations": [{"start": {"line": 24, "column": 46}, "end": {"line": 24, "column": 48}}], "line": 24}, "2": {"loc": {"start": {"line": 38, "column": 24}, "end": {"line": 38, "column": 41}}, "type": "default-arg", "locations": [{"start": {"line": 38, "column": 32}, "end": {"line": 38, "column": 41}}], "line": 38}, "3": {"loc": {"start": {"line": 38, "column": 43}, "end": {"line": 38, "column": 52}}, "type": "default-arg", "locations": [{"start": {"line": 38, "column": 50}, "end": {"line": 38, "column": 52}}], "line": 38}, "4": {"loc": {"start": {"line": 52, "column": 20}, "end": {"line": 52, "column": 37}}, "type": "default-arg", "locations": [{"start": {"line": 52, "column": 28}, "end": {"line": 52, "column": 37}}], "line": 52}, "5": {"loc": {"start": {"line": 52, "column": 39}, "end": {"line": 52, "column": 48}}, "type": "default-arg", "locations": [{"start": {"line": 52, "column": 46}, "end": {"line": 52, "column": 48}}], "line": 52}, "6": {"loc": {"start": {"line": 66, "column": 20}, "end": {"line": 66, "column": 37}}, "type": "default-arg", "locations": [{"start": {"line": 66, "column": 28}, "end": {"line": 66, "column": 37}}], "line": 66}, "7": {"loc": {"start": {"line": 66, "column": 39}, "end": {"line": 66, "column": 48}}, "type": "default-arg", "locations": [{"start": {"line": 66, "column": 46}, "end": {"line": 66, "column": 48}}], "line": 66}, "8": {"loc": {"start": {"line": 81, "column": 22}, "end": {"line": 81, "column": 39}}, "type": "default-arg", "locations": [{"start": {"line": 81, "column": 30}, "end": {"line": 81, "column": 39}}], "line": 81}, "9": {"loc": {"start": {"line": 81, "column": 41}, "end": {"line": 81, "column": 50}}, "type": "default-arg", "locations": [{"start": {"line": 81, "column": 48}, "end": {"line": 81, "column": 50}}], "line": 81}, "10": {"loc": {"start": {"line": 114, "column": 6}, "end": {"line": 114, "column": 70}}, "type": "binary-expr", "locations": [{"start": {"line": 114, "column": 6}, "end": {"line": 114, "column": 14}}, {"start": {"line": 114, "column": 18}, "end": {"line": 114, "column": 70}}], "line": 114}, "11": {"loc": {"start": {"line": 123, "column": 17}, "end": {"line": 123, "column": 79}}, "type": "cond-expr", "locations": [{"start": {"line": 123, "column": 28}, "end": {"line": 123, "column": 52}}, {"start": {"line": 123, "column": 55}, "end": {"line": 123, "column": 79}}], "line": 123}, "12": {"loc": {"start": {"line": 133, "column": 22}, "end": {"line": 133, "column": 49}}, "type": "binary-expr", "locations": [{"start": {"line": 133, "column": 22}, "end": {"line": 133, "column": 38}}, {"start": {"line": 133, "column": 42}, "end": {"line": 133, "column": 49}}], "line": 133}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0}, "b": {"0": [0], "1": [0], "2": [0], "3": [0], "4": [0], "5": [0], "6": [0], "7": [0], "8": [0], "9": [0], "10": [0, 0], "11": [0, 0], "12": [0, 0]}}, "/home/<USER>/projects/azmoon-client/components/ExternalLink.tsx": {"path": "/home/<USER>/projects/azmoon-client/components/ExternalLink.tsx", "statementMap": {"0": {"start": {"line": 9, "column": 2}, "end": {"line": 23, "column": 4}}, "1": {"start": {"line": 15, "column": 8}, "end": {"line": 20, "column": 9}}, "2": {"start": {"line": 17, "column": 10}, "end": {"line": 17, "column": 33}}, "3": {"start": {"line": 19, "column": 10}, "end": {"line": 19, "column": 39}}}, "fnMap": {"0": {"name": "ExternalLink", "decl": {"start": {"line": 8, "column": 16}, "end": {"line": 8, "column": 28}}, "loc": {"start": {"line": 8, "column": 55}, "end": {"line": 24, "column": 1}}, "line": 8}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 14, "column": 15}, "end": {"line": 14, "column": 16}}, "loc": {"start": {"line": 14, "column": 32}, "end": {"line": 21, "column": 7}}, "line": 14}}, "branchMap": {"0": {"loc": {"start": {"line": 15, "column": 8}, "end": {"line": 20, "column": 9}}, "type": "if", "locations": [{"start": {"line": 15, "column": 8}, "end": {"line": 20, "column": 9}}, {"start": {}, "end": {}}], "line": 15}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0}, "f": {"0": 0, "1": 0}, "b": {"0": [0, 0]}}, "/home/<USER>/projects/azmoon-client/components/HapticTab.tsx": {"path": "/home/<USER>/projects/azmoon-client/components/HapticTab.tsx", "statementMap": {"0": {"start": {"line": 6, "column": 2}, "end": {"line": 17, "column": 4}}, "1": {"start": {"line": 10, "column": 8}, "end": {"line": 13, "column": 9}}, "2": {"start": {"line": 12, "column": 10}, "end": {"line": 12, "column": 65}}, "3": {"start": {"line": 14, "column": 8}, "end": {"line": 14, "column": 30}}}, "fnMap": {"0": {"name": "HapticTab", "decl": {"start": {"line": 5, "column": 16}, "end": {"line": 5, "column": 25}}, "loc": {"start": {"line": 5, "column": 58}, "end": {"line": 18, "column": 1}}, "line": 5}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 9, "column": 17}, "end": {"line": 9, "column": 18}}, "loc": {"start": {"line": 9, "column": 25}, "end": {"line": 15, "column": 7}}, "line": 9}}, "branchMap": {"0": {"loc": {"start": {"line": 10, "column": 8}, "end": {"line": 13, "column": 9}}, "type": "if", "locations": [{"start": {"line": 10, "column": 8}, "end": {"line": 13, "column": 9}}, {"start": {}, "end": {}}], "line": 10}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0}, "f": {"0": 0, "1": 0}, "b": {"0": [0, 0]}}, "/home/<USER>/projects/azmoon-client/components/HelloWave.tsx": {"path": "/home/<USER>/projects/azmoon-client/components/HelloWave.tsx", "statementMap": {"0": {"start": {"line": 14, "column": 28}, "end": {"line": 14, "column": 45}}, "1": {"start": {"line": 16, "column": 2}, "end": {"line": 21, "column": 26}}, "2": {"start": {"line": 17, "column": 4}, "end": {"line": 20, "column": 6}}, "3": {"start": {"line": 23, "column": 24}, "end": {"line": 25, "column": 5}}, "4": {"start": {"line": 23, "column": 48}, "end": {"line": 25, "column": 3}}, "5": {"start": {"line": 27, "column": 2}, "end": {"line": 31, "column": 4}}, "6": {"start": {"line": 34, "column": 15}, "end": {"line": 40, "column": 2}}}, "fnMap": {"0": {"name": "HelloWave", "decl": {"start": {"line": 13, "column": 16}, "end": {"line": 13, "column": 25}}, "loc": {"start": {"line": 13, "column": 28}, "end": {"line": 32, "column": 1}}, "line": 13}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 16, "column": 12}, "end": {"line": 16, "column": 13}}, "loc": {"start": {"line": 16, "column": 18}, "end": {"line": 21, "column": 3}}, "line": 16}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 23, "column": 41}, "end": {"line": 23, "column": 42}}, "loc": {"start": {"line": 23, "column": 48}, "end": {"line": 25, "column": 3}}, "line": 23}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0}, "f": {"0": 0, "1": 0, "2": 0}, "b": {}}, "/home/<USER>/projects/azmoon-client/components/ParallaxScrollView.tsx": {"path": "/home/<USER>/projects/azmoon-client/components/ParallaxScrollView.tsx", "statementMap": {"0": {"start": {"line": 14, "column": 22}, "end": {"line": 14, "column": 25}}, "1": {"start": {"line": 26, "column": 22}, "end": {"line": 26, "column": 49}}, "2": {"start": {"line": 27, "column": 20}, "end": {"line": 27, "column": 57}}, "3": {"start": {"line": 28, "column": 23}, "end": {"line": 28, "column": 53}}, "4": {"start": {"line": 29, "column": 17}, "end": {"line": 29, "column": 39}}, "5": {"start": {"line": 30, "column": 30}, "end": {"line": 45, "column": 4}}, "6": {"start": {"line": 31, "column": 4}, "end": {"line": 44, "column": 6}}, "7": {"start": {"line": 47, "column": 2}, "end": {"line": 65, "column": 4}}, "8": {"start": {"line": 68, "column": 15}, "end": {"line": 82, "column": 2}}}, "fnMap": {"0": {"name": "ParallaxScrollView", "decl": {"start": {"line": 21, "column": 24}, "end": {"line": 21, "column": 42}}, "loc": {"start": {"line": 25, "column": 10}, "end": {"line": 66, "column": 1}}, "line": 25}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 30, "column": 47}, "end": {"line": 30, "column": 48}}, "loc": {"start": {"line": 30, "column": 53}, "end": {"line": 45, "column": 3}}, "line": 30}}, "branchMap": {"0": {"loc": {"start": {"line": 26, "column": 22}, "end": {"line": 26, "column": 49}}, "type": "binary-expr", "locations": [{"start": {"line": 26, "column": 22}, "end": {"line": 26, "column": 38}}, {"start": {"line": 26, "column": 42}, "end": {"line": 26, "column": 49}}], "line": 26}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0}, "f": {"0": 0, "1": 0}, "b": {"0": [0, 0]}}, "/home/<USER>/projects/azmoon-client/components/ThemedText.tsx": {"path": "/home/<USER>/projects/azmoon-client/components/ThemedText.tsx", "statementMap": {"0": {"start": {"line": 18, "column": 16}, "end": {"line": 18, "column": 77}}, "1": {"start": {"line": 20, "column": 2}, "end": {"line": 33, "column": 4}}, "2": {"start": {"line": 36, "column": 15}, "end": {"line": 75, "column": 2}}}, "fnMap": {"0": {"name": "ThemedText", "decl": {"start": {"line": 11, "column": 16}, "end": {"line": 11, "column": 26}}, "loc": {"start": {"line": 17, "column": 20}, "end": {"line": 34, "column": 1}}, "line": 17}}, "branchMap": {"0": {"loc": {"start": {"line": 15, "column": 2}, "end": {"line": 15, "column": 18}}, "type": "default-arg", "locations": [{"start": {"line": 15, "column": 9}, "end": {"line": 15, "column": 18}}], "line": 15}, "1": {"loc": {"start": {"line": 24, "column": 8}, "end": {"line": 24, "column": 55}}, "type": "cond-expr", "locations": [{"start": {"line": 24, "column": 29}, "end": {"line": 24, "column": 43}}, {"start": {"line": 24, "column": 46}, "end": {"line": 24, "column": 55}}], "line": 24}, "2": {"loc": {"start": {"line": 25, "column": 8}, "end": {"line": 25, "column": 51}}, "type": "cond-expr", "locations": [{"start": {"line": 25, "column": 27}, "end": {"line": 25, "column": 39}}, {"start": {"line": 25, "column": 42}, "end": {"line": 25, "column": 51}}], "line": 25}, "3": {"loc": {"start": {"line": 26, "column": 8}, "end": {"line": 26, "column": 71}}, "type": "cond-expr", "locations": [{"start": {"line": 26, "column": 37}, "end": {"line": 26, "column": 59}}, {"start": {"line": 26, "column": 62}, "end": {"line": 26, "column": 71}}], "line": 26}, "4": {"loc": {"start": {"line": 27, "column": 8}, "end": {"line": 27, "column": 57}}, "type": "cond-expr", "locations": [{"start": {"line": 27, "column": 30}, "end": {"line": 27, "column": 45}}, {"start": {"line": 27, "column": 48}, "end": {"line": 27, "column": 57}}], "line": 27}, "5": {"loc": {"start": {"line": 28, "column": 8}, "end": {"line": 28, "column": 49}}, "type": "cond-expr", "locations": [{"start": {"line": 28, "column": 26}, "end": {"line": 28, "column": 37}}, {"start": {"line": 28, "column": 40}, "end": {"line": 28, "column": 49}}], "line": 28}}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0}, "b": {"0": [0], "1": [0, 0], "2": [0, 0], "3": [0, 0], "4": [0, 0], "5": [0, 0]}}, "/home/<USER>/projects/azmoon-client/components/ThemedView.tsx": {"path": "/home/<USER>/projects/azmoon-client/components/ThemedView.tsx", "statementMap": {"0": {"start": {"line": 16, "column": 26}, "end": {"line": 19, "column": 3}}, "1": {"start": {"line": 21, "column": 2}, "end": {"line": 26, "column": 4}}}, "fnMap": {"0": {"name": "ThemedView", "decl": {"start": {"line": 10, "column": 16}, "end": {"line": 10, "column": 26}}, "loc": {"start": {"line": 15, "column": 20}, "end": {"line": 27, "column": 1}}, "line": 15}}, "branchMap": {}, "s": {"0": 0, "1": 0}, "f": {"0": 0}, "b": {}}, "/home/<USER>/projects/azmoon-client/components/ui/IconSymbol.ios.tsx": {"path": "/home/<USER>/projects/azmoon-client/components/ui/IconSymbol.ios.tsx", "statementMap": {"0": {"start": {"line": 17, "column": 2}, "end": {"line": 31, "column": 4}}}, "fnMap": {"0": {"name": "IconSymbol", "decl": {"start": {"line": 4, "column": 16}, "end": {"line": 4, "column": 26}}, "loc": {"start": {"line": 16, "column": 3}, "end": {"line": 32, "column": 1}}, "line": 16}}, "branchMap": {"0": {"loc": {"start": {"line": 6, "column": 2}, "end": {"line": 6, "column": 11}}, "type": "default-arg", "locations": [{"start": {"line": 6, "column": 9}, "end": {"line": 6, "column": 11}}], "line": 6}, "1": {"loc": {"start": {"line": 9, "column": 2}, "end": {"line": 9, "column": 20}}, "type": "default-arg", "locations": [{"start": {"line": 9, "column": 11}, "end": {"line": 9, "column": 20}}], "line": 9}}, "s": {"0": 0}, "f": {"0": 0}, "b": {"0": [0], "1": [0]}}, "/home/<USER>/projects/azmoon-client/components/ui/IconSymbol.tsx": {"path": "/home/<USER>/projects/azmoon-client/components/ui/IconSymbol.tsx", "statementMap": {"0": {"start": {"line": 16, "column": 16}, "end": {"line": 21, "column": 16}}, "1": {"start": {"line": 40, "column": 2}, "end": {"line": 40, "column": 88}}}, "fnMap": {"0": {"name": "IconSymbol", "decl": {"start": {"line": 28, "column": 16}, "end": {"line": 28, "column": 26}}, "loc": {"start": {"line": 39, "column": 3}, "end": {"line": 41, "column": 1}}, "line": 39}}, "branchMap": {"0": {"loc": {"start": {"line": 30, "column": 2}, "end": {"line": 30, "column": 11}}, "type": "default-arg", "locations": [{"start": {"line": 30, "column": 9}, "end": {"line": 30, "column": 11}}], "line": 30}}, "s": {"0": 0, "1": 0}, "f": {"0": 0}, "b": {"0": [0]}}, "/home/<USER>/projects/azmoon-client/components/ui/MenuButton.tsx": {"path": "/home/<USER>/projects/azmoon-client/components/ui/MenuButton.tsx", "statementMap": {"0": {"start": {"line": 7, "column": 22}, "end": {"line": 7, "column": 49}}, "1": {"start": {"line": 8, "column": 21}, "end": {"line": 8, "column": 36}}, "2": {"start": {"line": 9, "column": 21}, "end": {"line": 11, "column": 3}}, "3": {"start": {"line": 10, "column": 4}, "end": {"line": 10, "column": 28}}, "4": {"start": {"line": 13, "column": 19}, "end": {"line": 24, "column": 3}}, "5": {"start": {"line": 14, "column": 4}, "end": {"line": 23, "column": 10}}, "6": {"start": {"line": 25, "column": 2}, "end": {"line": 29, "column": 4}}, "7": {"start": {"line": 32, "column": 15}, "end": {"line": 41, "column": 2}}}, "fnMap": {"0": {"name": "MenuButton", "decl": {"start": {"line": 6, "column": 16}, "end": {"line": 6, "column": 26}}, "loc": {"start": {"line": 6, "column": 29}, "end": {"line": 30, "column": 1}}, "line": 6}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 9, "column": 21}, "end": {"line": 9, "column": 22}}, "loc": {"start": {"line": 9, "column": 27}, "end": {"line": 11, "column": 3}}, "line": 9}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 13, "column": 19}, "end": {"line": 13, "column": 20}}, "loc": {"start": {"line": 14, "column": 4}, "end": {"line": 23, "column": 10}}, "line": 14}}, "branchMap": {"0": {"loc": {"start": {"line": 7, "column": 22}, "end": {"line": 7, "column": 49}}, "type": "binary-expr", "locations": [{"start": {"line": 7, "column": 22}, "end": {"line": 7, "column": 38}}, {"start": {"line": 7, "column": 42}, "end": {"line": 7, "column": 49}}], "line": 7}, "1": {"loc": {"start": {"line": 13, "column": 22}, "end": {"line": 13, "column": 39}}, "type": "default-arg", "locations": [{"start": {"line": 13, "column": 30}, "end": {"line": 13, "column": 39}}], "line": 13}, "2": {"loc": {"start": {"line": 13, "column": 41}, "end": {"line": 13, "column": 50}}, "type": "default-arg", "locations": [{"start": {"line": 13, "column": 48}, "end": {"line": 13, "column": 50}}], "line": 13}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0}, "f": {"0": 0, "1": 0, "2": 0}, "b": {"0": [0, 0], "1": [0], "2": [0]}}, "/home/<USER>/projects/azmoon-client/components/ui/TabBarBackground.ios.tsx": {"path": "/home/<USER>/projects/azmoon-client/components/ui/TabBarBackground.ios.tsx", "statementMap": {"0": {"start": {"line": 6, "column": 2}, "end": {"line": 14, "column": 4}}, "1": {"start": {"line": 18, "column": 2}, "end": {"line": 18, "column": 33}}}, "fnMap": {"0": {"name": "BlurTabBarBackground", "decl": {"start": {"line": 5, "column": 24}, "end": {"line": 5, "column": 44}}, "loc": {"start": {"line": 5, "column": 47}, "end": {"line": 15, "column": 1}}, "line": 5}, "1": {"name": "useBottomTabOverflow", "decl": {"start": {"line": 17, "column": 16}, "end": {"line": 17, "column": 36}}, "loc": {"start": {"line": 17, "column": 39}, "end": {"line": 19, "column": 1}}, "line": 17}}, "branchMap": {}, "s": {"0": 0, "1": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "/home/<USER>/projects/azmoon-client/components/ui/TabBarBackground.tsx": {"path": "/home/<USER>/projects/azmoon-client/components/ui/TabBarBackground.tsx", "statementMap": {"0": {"start": {"line": 5, "column": 2}, "end": {"line": 5, "column": 11}}}, "fnMap": {"0": {"name": "useBottomTabOverflow", "decl": {"start": {"line": 4, "column": 16}, "end": {"line": 4, "column": 36}}, "loc": {"start": {"line": 4, "column": 39}, "end": {"line": 6, "column": 1}}, "line": 4}}, "branchMap": {}, "s": {"0": 0}, "f": {"0": 0}, "b": {}}, "/home/<USER>/projects/azmoon-client/services/api.ts": {"path": "/home/<USER>/projects/azmoon-client/services/api.ts", "statementMap": {"0": {"start": {"line": 16, "column": 4}, "end": {"line": 16, "column": 19}}, "1": {"start": {"line": 17, "column": 4}, "end": {"line": 17, "column": 27}}, "2": {"start": {"line": 18, "column": 4}, "end": {"line": 18, "column": 25}}, "3": {"start": {"line": 19, "column": 4}, "end": {"line": 19, "column": 21}}, "4": {"start": {"line": 29, "column": 4}, "end": {"line": 29, "column": 84}}, "5": {"start": {"line": 30, "column": 4}, "end": {"line": 33, "column": 6}}, "6": {"start": {"line": 38, "column": 4}, "end": {"line": 42, "column": 5}}, "7": {"start": {"line": 39, "column": 6}, "end": {"line": 39, "column": 63}}, "8": {"start": {"line": 41, "column": 6}, "end": {"line": 41, "column": 50}}, "9": {"start": {"line": 52, "column": 16}, "end": {"line": 52, "column": 44}}, "10": {"start": {"line": 54, "column": 20}, "end": {"line": 57, "column": 5}}, "11": {"start": {"line": 59, "column": 32}, "end": {"line": 63, "column": 5}}, "12": {"start": {"line": 65, "column": 4}, "end": {"line": 91, "column": 5}}, "13": {"start": {"line": 66, "column": 23}, "end": {"line": 66, "column": 47}}, "14": {"start": {"line": 67, "column": 27}, "end": {"line": 67, "column": 48}}, "15": {"start": {"line": 69, "column": 6}, "end": {"line": 75, "column": 7}}, "16": {"start": {"line": 70, "column": 8}, "end": {"line": 74, "column": 10}}, "17": {"start": {"line": 77, "column": 6}, "end": {"line": 81, "column": 8}}, "18": {"start": {"line": 83, "column": 6}, "end": {"line": 85, "column": 7}}, "19": {"start": {"line": 84, "column": 8}, "end": {"line": 84, "column": 20}}, "20": {"start": {"line": 87, "column": 6}, "end": {"line": 90, "column": 8}}, "21": {"start": {"line": 96, "column": 4}, "end": {"line": 96, "column": 70}}, "22": {"start": {"line": 100, "column": 4}, "end": {"line": 100, "column": 66}}, "23": {"start": {"line": 104, "column": 4}, "end": {"line": 104, "column": 65}}, "24": {"start": {"line": 108, "column": 4}, "end": {"line": 108, "column": 67}}, "25": {"start": {"line": 112, "column": 4}, "end": {"line": 112, "column": 73}}, "26": {"start": {"line": 117, "column": 19}, "end": {"line": 117, "column": 35}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 15, "column": 2}, "end": {"line": 15, "column": 3}}, "loc": {"start": {"line": 15, "column": 59}, "end": {"line": 20, "column": 3}}, "line": 15}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 27, "column": 2}, "end": {"line": 27, "column": 3}}, "loc": {"start": {"line": 27, "column": 16}, "end": {"line": 34, "column": 3}}, "line": 27}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 37, "column": 2}, "end": {"line": 37, "column": 3}}, "loc": {"start": {"line": 37, "column": 37}, "end": {"line": 43, "column": 3}}, "line": 37}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 46, "column": 2}, "end": {"line": 46, "column": 3}}, "loc": {"start": {"line": 51, "column": 29}, "end": {"line": 92, "column": 3}}, "line": 51}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 95, "column": 2}, "end": {"line": 95, "column": 3}}, "loc": {"start": {"line": 95, "column": 98}, "end": {"line": 97, "column": 3}}, "line": 95}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 99, "column": 2}, "end": {"line": 99, "column": 3}}, "loc": {"start": {"line": 99, "column": 111}, "end": {"line": 101, "column": 3}}, "line": 99}, "6": {"name": "(anonymous_6)", "decl": {"start": {"line": 103, "column": 2}, "end": {"line": 103, "column": 3}}, "loc": {"start": {"line": 103, "column": 110}, "end": {"line": 105, "column": 3}}, "line": 103}, "7": {"name": "(anonymous_7)", "decl": {"start": {"line": 107, "column": 2}, "end": {"line": 107, "column": 3}}, "loc": {"start": {"line": 107, "column": 112}, "end": {"line": 109, "column": 3}}, "line": 107}, "8": {"name": "(anonymous_8)", "decl": {"start": {"line": 111, "column": 2}, "end": {"line": 111, "column": 3}}, "loc": {"start": {"line": 111, "column": 101}, "end": {"line": 113, "column": 3}}, "line": 111}}, "branchMap": {"0": {"loc": {"start": {"line": 29, "column": 19}, "end": {"line": 29, "column": 83}}, "type": "binary-expr", "locations": [{"start": {"line": 29, "column": 19}, "end": {"line": 29, "column": 54}}, {"start": {"line": 29, "column": 58}, "end": {"line": 29, "column": 83}}], "line": 29}, "1": {"loc": {"start": {"line": 38, "column": 4}, "end": {"line": 42, "column": 5}}, "type": "if", "locations": [{"start": {"line": 38, "column": 4}, "end": {"line": 42, "column": 5}}, {"start": {"line": 40, "column": 11}, "end": {"line": 42, "column": 5}}], "line": 38}, "2": {"loc": {"start": {"line": 62, "column": 12}, "end": {"line": 62, "column": 51}}, "type": "cond-expr", "locations": [{"start": {"line": 62, "column": 19}, "end": {"line": 62, "column": 39}}, {"start": {"line": 62, "column": 42}, "end": {"line": 62, "column": 51}}], "line": 62}, "3": {"loc": {"start": {"line": 69, "column": 6}, "end": {"line": 75, "column": 7}}, "type": "if", "locations": [{"start": {"line": 69, "column": 6}, "end": {"line": 75, "column": 7}}, {"start": {}, "end": {}}], "line": 69}, "4": {"loc": {"start": {"line": 71, "column": 10}, "end": {"line": 71, "column": 53}}, "type": "binary-expr", "locations": [{"start": {"line": 71, "column": 10}, "end": {"line": 71, "column": 30}}, {"start": {"line": 71, "column": 34}, "end": {"line": 71, "column": 53}}], "line": 71}, "5": {"loc": {"start": {"line": 83, "column": 6}, "end": {"line": 85, "column": 7}}, "type": "if", "locations": [{"start": {"line": 83, "column": 6}, "end": {"line": 85, "column": 7}}, {"start": {}, "end": {}}], "line": 83}, "6": {"loc": {"start": {"line": 88, "column": 8}, "end": {"line": 88, "column": 64}}, "type": "cond-expr", "locations": [{"start": {"line": 88, "column": 33}, "end": {"line": 88, "column": 46}}, {"start": {"line": 88, "column": 49}, "end": {"line": 88, "column": 64}}], "line": 88}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0], "3": [0, 0], "4": [0, 0], "5": [0, 0], "6": [0, 0]}}, "/home/<USER>/projects/azmoon-client/services/chapterService.ts": {"path": "/home/<USER>/projects/azmoon-client/services/chapterService.ts", "statementMap": {"0": {"start": {"line": 17, "column": 30}, "end": {"line": 46, "column": 1}}, "1": {"start": {"line": 20, "column": 21}, "end": {"line": 20, "column": 58}}, "2": {"start": {"line": 21, "column": 4}, "end": {"line": 21, "column": 25}}, "3": {"start": {"line": 26, "column": 21}, "end": {"line": 26, "column": 62}}, "4": {"start": {"line": 27, "column": 4}, "end": {"line": 27, "column": 25}}, "5": {"start": {"line": 32, "column": 21}, "end": {"line": 32, "column": 70}}, "6": {"start": {"line": 33, "column": 4}, "end": {"line": 33, "column": 25}}, "7": {"start": {"line": 38, "column": 21}, "end": {"line": 38, "column": 77}}, "8": {"start": {"line": 39, "column": 4}, "end": {"line": 39, "column": 25}}, "9": {"start": {"line": 44, "column": 4}, "end": {"line": 44, "column": 40}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 19, "column": 2}, "end": {"line": 19, "column": 3}}, "loc": {"start": {"line": 19, "column": 42}, "end": {"line": 22, "column": 3}}, "line": 19}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 25, "column": 2}, "end": {"line": 25, "column": 3}}, "loc": {"start": {"line": 25, "column": 49}, "end": {"line": 28, "column": 3}}, "line": 25}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 31, "column": 2}, "end": {"line": 31, "column": 3}}, "loc": {"start": {"line": 31, "column": 71}, "end": {"line": 34, "column": 3}}, "line": 31}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 37, "column": 2}, "end": {"line": 37, "column": 3}}, "loc": {"start": {"line": 37, "column": 83}, "end": {"line": 40, "column": 3}}, "line": 37}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 43, "column": 2}, "end": {"line": 43, "column": 3}}, "loc": {"start": {"line": 43, "column": 49}, "end": {"line": 45, "column": 3}}, "line": 43}}, "branchMap": {}, "s": {"0": 2, "1": 9, "2": 6, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 1}, "f": {"0": 9, "1": 0, "2": 0, "3": 0, "4": 1}, "b": {}, "_coverageSchema": "1a1c01bbd47fc00a2c39e90264f33305004495a9", "hash": "1c61565694af14455511d15d1d97a52cc68d2327"}, "/home/<USER>/projects/azmoon-client/services/courseService.ts": {"path": "/home/<USER>/projects/azmoon-client/services/courseService.ts", "statementMap": {"0": {"start": {"line": 11, "column": 29}, "end": {"line": 40, "column": 1}}, "1": {"start": {"line": 14, "column": 21}, "end": {"line": 14, "column": 56}}, "2": {"start": {"line": 15, "column": 4}, "end": {"line": 15, "column": 25}}, "3": {"start": {"line": 20, "column": 21}, "end": {"line": 20, "column": 60}}, "4": {"start": {"line": 21, "column": 4}, "end": {"line": 21, "column": 25}}, "5": {"start": {"line": 26, "column": 21}, "end": {"line": 26, "column": 67}}, "6": {"start": {"line": 27, "column": 4}, "end": {"line": 27, "column": 25}}, "7": {"start": {"line": 32, "column": 21}, "end": {"line": 32, "column": 74}}, "8": {"start": {"line": 33, "column": 4}, "end": {"line": 33, "column": 25}}, "9": {"start": {"line": 38, "column": 4}, "end": {"line": 38, "column": 39}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 13, "column": 2}, "end": {"line": 13, "column": 3}}, "loc": {"start": {"line": 13, "column": 40}, "end": {"line": 16, "column": 3}}, "line": 13}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 19, "column": 2}, "end": {"line": 19, "column": 3}}, "loc": {"start": {"line": 19, "column": 47}, "end": {"line": 22, "column": 3}}, "line": 19}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 25, "column": 2}, "end": {"line": 25, "column": 3}}, "loc": {"start": {"line": 25, "column": 70}, "end": {"line": 28, "column": 3}}, "line": 25}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 31, "column": 2}, "end": {"line": 31, "column": 3}}, "loc": {"start": {"line": 31, "column": 79}, "end": {"line": 34, "column": 3}}, "line": 31}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 37, "column": 2}, "end": {"line": 37, "column": 3}}, "loc": {"start": {"line": 37, "column": 48}, "end": {"line": 39, "column": 3}}, "line": 37}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0}, "b": {}}, "/home/<USER>/projects/azmoon-client/services/majorCourseService.ts": {"path": "/home/<USER>/projects/azmoon-client/services/majorCourseService.ts", "statementMap": {"0": {"start": {"line": 11, "column": 34}, "end": {"line": 40, "column": 1}}, "1": {"start": {"line": 14, "column": 21}, "end": {"line": 14, "column": 67}}, "2": {"start": {"line": 15, "column": 4}, "end": {"line": 15, "column": 25}}, "3": {"start": {"line": 20, "column": 21}, "end": {"line": 20, "column": 88}}, "4": {"start": {"line": 21, "column": 4}, "end": {"line": 21, "column": 25}}, "5": {"start": {"line": 26, "column": 21}, "end": {"line": 26, "column": 83}}, "6": {"start": {"line": 27, "column": 4}, "end": {"line": 27, "column": 25}}, "7": {"start": {"line": 32, "column": 21}, "end": {"line": 32, "column": 107}}, "8": {"start": {"line": 33, "column": 4}, "end": {"line": 33, "column": 25}}, "9": {"start": {"line": 38, "column": 4}, "end": {"line": 38, "column": 62}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 13, "column": 2}, "end": {"line": 13, "column": 3}}, "loc": {"start": {"line": 13, "column": 50}, "end": {"line": 16, "column": 3}}, "line": 13}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 19, "column": 2}, "end": {"line": 19, "column": 3}}, "loc": {"start": {"line": 19, "column": 80}, "end": {"line": 22, "column": 3}}, "line": 19}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 25, "column": 2}, "end": {"line": 25, "column": 3}}, "loc": {"start": {"line": 25, "column": 78}, "end": {"line": 28, "column": 3}}, "line": 25}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 31, "column": 2}, "end": {"line": 31, "column": 3}}, "loc": {"start": {"line": 31, "column": 122}, "end": {"line": 34, "column": 3}}, "line": 31}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 37, "column": 2}, "end": {"line": 37, "column": 3}}, "loc": {"start": {"line": 37, "column": 76}, "end": {"line": 39, "column": 3}}, "line": 37}}, "branchMap": {}, "s": {"0": 1, "1": 2, "2": 1, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0}, "f": {"0": 2, "1": 0, "2": 0, "3": 0, "4": 0}, "b": {}, "_coverageSchema": "1a1c01bbd47fc00a2c39e90264f33305004495a9", "hash": "f7a00a9c98194eff0ba8fbcb3bf86acfd5f26198"}, "/home/<USER>/projects/azmoon-client/services/majorService.ts": {"path": "/home/<USER>/projects/azmoon-client/services/majorService.ts", "statementMap": {"0": {"start": {"line": 12, "column": 28}, "end": {"line": 41, "column": 1}}, "1": {"start": {"line": 15, "column": 21}, "end": {"line": 15, "column": 54}}, "2": {"start": {"line": 16, "column": 4}, "end": {"line": 16, "column": 25}}, "3": {"start": {"line": 21, "column": 21}, "end": {"line": 21, "column": 58}}, "4": {"start": {"line": 22, "column": 4}, "end": {"line": 22, "column": 25}}, "5": {"start": {"line": 27, "column": 21}, "end": {"line": 27, "column": 64}}, "6": {"start": {"line": 28, "column": 4}, "end": {"line": 28, "column": 25}}, "7": {"start": {"line": 33, "column": 21}, "end": {"line": 33, "column": 69}}, "8": {"start": {"line": 34, "column": 4}, "end": {"line": 34, "column": 25}}, "9": {"start": {"line": 39, "column": 4}, "end": {"line": 39, "column": 38}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 14, "column": 2}, "end": {"line": 14, "column": 3}}, "loc": {"start": {"line": 14, "column": 38}, "end": {"line": 17, "column": 3}}, "line": 14}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 20, "column": 2}, "end": {"line": 20, "column": 3}}, "loc": {"start": {"line": 20, "column": 45}, "end": {"line": 23, "column": 3}}, "line": 20}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 26, "column": 2}, "end": {"line": 26, "column": 3}}, "loc": {"start": {"line": 26, "column": 66}, "end": {"line": 29, "column": 3}}, "line": 26}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 32, "column": 2}, "end": {"line": 32, "column": 3}}, "loc": {"start": {"line": 32, "column": 75}, "end": {"line": 35, "column": 3}}, "line": 32}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 38, "column": 2}, "end": {"line": 38, "column": 3}}, "loc": {"start": {"line": 38, "column": 47}, "end": {"line": 40, "column": 3}}, "line": 38}}, "branchMap": {}, "s": {"0": 2, "1": 8, "2": 6, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0}, "f": {"0": 8, "1": 0, "2": 0, "3": 0, "4": 0}, "b": {}, "_coverageSchema": "1a1c01bbd47fc00a2c39e90264f33305004495a9", "hash": "c9f30388f0511ac598c7ca2cf8e14f3da5961129"}, "/home/<USER>/projects/azmoon-client/services/optionService.ts": {"path": "/home/<USER>/projects/azmoon-client/services/optionService.ts", "statementMap": {"0": {"start": {"line": 13, "column": 29}, "end": {"line": 42, "column": 1}}, "1": {"start": {"line": 16, "column": 21}, "end": {"line": 16, "column": 56}}, "2": {"start": {"line": 17, "column": 4}, "end": {"line": 17, "column": 25}}, "3": {"start": {"line": 22, "column": 21}, "end": {"line": 22, "column": 60}}, "4": {"start": {"line": 23, "column": 4}, "end": {"line": 23, "column": 25}}, "5": {"start": {"line": 28, "column": 21}, "end": {"line": 28, "column": 67}}, "6": {"start": {"line": 29, "column": 4}, "end": {"line": 29, "column": 25}}, "7": {"start": {"line": 34, "column": 21}, "end": {"line": 34, "column": 74}}, "8": {"start": {"line": 35, "column": 4}, "end": {"line": 35, "column": 25}}, "9": {"start": {"line": 40, "column": 4}, "end": {"line": 40, "column": 39}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 15, "column": 2}, "end": {"line": 15, "column": 3}}, "loc": {"start": {"line": 15, "column": 40}, "end": {"line": 18, "column": 3}}, "line": 15}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 21, "column": 2}, "end": {"line": 21, "column": 3}}, "loc": {"start": {"line": 21, "column": 47}, "end": {"line": 24, "column": 3}}, "line": 21}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 27, "column": 2}, "end": {"line": 27, "column": 3}}, "loc": {"start": {"line": 27, "column": 70}, "end": {"line": 30, "column": 3}}, "line": 27}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 33, "column": 2}, "end": {"line": 33, "column": 3}}, "loc": {"start": {"line": 33, "column": 79}, "end": {"line": 36, "column": 3}}, "line": 33}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 39, "column": 2}, "end": {"line": 39, "column": 3}}, "loc": {"start": {"line": 39, "column": 48}, "end": {"line": 41, "column": 3}}, "line": 39}}, "branchMap": {}, "s": {"0": 1, "1": 2, "2": 1, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0}, "f": {"0": 2, "1": 0, "2": 0, "3": 0, "4": 0}, "b": {}, "_coverageSchema": "1a1c01bbd47fc00a2c39e90264f33305004495a9", "hash": "e3b9a568cec4a6fcf413a72970d12719c17434a5"}, "/home/<USER>/projects/azmoon-client/services/questionService.ts": {"path": "/home/<USER>/projects/azmoon-client/services/questionService.ts", "statementMap": {"0": {"start": {"line": 12, "column": 31}, "end": {"line": 41, "column": 1}}, "1": {"start": {"line": 15, "column": 21}, "end": {"line": 15, "column": 60}}, "2": {"start": {"line": 16, "column": 4}, "end": {"line": 16, "column": 25}}, "3": {"start": {"line": 21, "column": 21}, "end": {"line": 21, "column": 64}}, "4": {"start": {"line": 22, "column": 4}, "end": {"line": 22, "column": 25}}, "5": {"start": {"line": 27, "column": 21}, "end": {"line": 27, "column": 73}}, "6": {"start": {"line": 28, "column": 4}, "end": {"line": 28, "column": 25}}, "7": {"start": {"line": 33, "column": 21}, "end": {"line": 33, "column": 80}}, "8": {"start": {"line": 34, "column": 4}, "end": {"line": 34, "column": 25}}, "9": {"start": {"line": 39, "column": 4}, "end": {"line": 39, "column": 41}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 14, "column": 2}, "end": {"line": 14, "column": 3}}, "loc": {"start": {"line": 14, "column": 44}, "end": {"line": 17, "column": 3}}, "line": 14}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 20, "column": 2}, "end": {"line": 20, "column": 3}}, "loc": {"start": {"line": 20, "column": 51}, "end": {"line": 23, "column": 3}}, "line": 20}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 26, "column": 2}, "end": {"line": 26, "column": 3}}, "loc": {"start": {"line": 26, "column": 78}, "end": {"line": 29, "column": 3}}, "line": 26}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 32, "column": 2}, "end": {"line": 32, "column": 3}}, "loc": {"start": {"line": 32, "column": 87}, "end": {"line": 35, "column": 3}}, "line": 32}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 38, "column": 2}, "end": {"line": 38, "column": 3}}, "loc": {"start": {"line": 38, "column": 50}, "end": {"line": 40, "column": 3}}, "line": 38}}, "branchMap": {}, "s": {"0": 2, "1": 9, "2": 4, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0}, "f": {"0": 9, "1": 0, "2": 0, "3": 0, "4": 0}, "b": {}, "_coverageSchema": "1a1c01bbd47fc00a2c39e90264f33305004495a9", "hash": "7e1d697fce52470241b3d8c7d6cb8127a609312d"}, "/home/<USER>/projects/azmoon-client/services/sessionService.ts": {"path": "/home/<USER>/projects/azmoon-client/services/sessionService.ts", "statementMap": {"0": {"start": {"line": 13, "column": 30}, "end": {"line": 42, "column": 1}}, "1": {"start": {"line": 16, "column": 21}, "end": {"line": 16, "column": 58}}, "2": {"start": {"line": 17, "column": 4}, "end": {"line": 17, "column": 25}}, "3": {"start": {"line": 22, "column": 21}, "end": {"line": 22, "column": 62}}, "4": {"start": {"line": 23, "column": 4}, "end": {"line": 23, "column": 25}}, "5": {"start": {"line": 28, "column": 21}, "end": {"line": 28, "column": 70}}, "6": {"start": {"line": 29, "column": 4}, "end": {"line": 29, "column": 25}}, "7": {"start": {"line": 34, "column": 21}, "end": {"line": 34, "column": 77}}, "8": {"start": {"line": 35, "column": 4}, "end": {"line": 35, "column": 25}}, "9": {"start": {"line": 40, "column": 4}, "end": {"line": 40, "column": 40}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 15, "column": 2}, "end": {"line": 15, "column": 3}}, "loc": {"start": {"line": 15, "column": 42}, "end": {"line": 18, "column": 3}}, "line": 15}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 21, "column": 2}, "end": {"line": 21, "column": 3}}, "loc": {"start": {"line": 21, "column": 49}, "end": {"line": 24, "column": 3}}, "line": 21}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 27, "column": 2}, "end": {"line": 27, "column": 3}}, "loc": {"start": {"line": 27, "column": 74}, "end": {"line": 30, "column": 3}}, "line": 27}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 33, "column": 2}, "end": {"line": 33, "column": 3}}, "loc": {"start": {"line": 33, "column": 83}, "end": {"line": 36, "column": 3}}, "line": 33}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 39, "column": 2}, "end": {"line": 39, "column": 3}}, "loc": {"start": {"line": 39, "column": 49}, "end": {"line": 41, "column": 3}}, "line": 39}}, "branchMap": {}, "s": {"0": 1, "1": 2, "2": 1, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0}, "f": {"0": 2, "1": 0, "2": 0, "3": 0, "4": 0}, "b": {}, "_coverageSchema": "1a1c01bbd47fc00a2c39e90264f33305004495a9", "hash": "34f31d68a336b94b29a42bc9cb39317259915bba"}, "/home/<USER>/projects/azmoon-client/services/userService.ts": {"path": "/home/<USER>/projects/azmoon-client/services/userService.ts", "statementMap": {"0": {"start": {"line": 19, "column": 27}, "end": {"line": 48, "column": 1}}, "1": {"start": {"line": 22, "column": 21}, "end": {"line": 22, "column": 52}}, "2": {"start": {"line": 23, "column": 4}, "end": {"line": 23, "column": 25}}, "3": {"start": {"line": 28, "column": 21}, "end": {"line": 28, "column": 56}}, "4": {"start": {"line": 29, "column": 4}, "end": {"line": 29, "column": 25}}, "5": {"start": {"line": 34, "column": 21}, "end": {"line": 34, "column": 61}}, "6": {"start": {"line": 35, "column": 4}, "end": {"line": 35, "column": 25}}, "7": {"start": {"line": 40, "column": 21}, "end": {"line": 40, "column": 68}}, "8": {"start": {"line": 41, "column": 4}, "end": {"line": 41, "column": 25}}, "9": {"start": {"line": 46, "column": 4}, "end": {"line": 46, "column": 37}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 21, "column": 2}, "end": {"line": 21, "column": 3}}, "loc": {"start": {"line": 21, "column": 36}, "end": {"line": 24, "column": 3}}, "line": 21}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 27, "column": 2}, "end": {"line": 27, "column": 3}}, "loc": {"start": {"line": 27, "column": 43}, "end": {"line": 30, "column": 3}}, "line": 27}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 33, "column": 2}, "end": {"line": 33, "column": 3}}, "loc": {"start": {"line": 33, "column": 59}, "end": {"line": 36, "column": 3}}, "line": 33}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 39, "column": 2}, "end": {"line": 39, "column": 3}}, "loc": {"start": {"line": 39, "column": 71}, "end": {"line": 42, "column": 3}}, "line": 39}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 45, "column": 2}, "end": {"line": 45, "column": 3}}, "loc": {"start": {"line": 45, "column": 46}, "end": {"line": 47, "column": 3}}, "line": 45}}, "branchMap": {}, "s": {"0": 2, "1": 8, "2": 4, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 1}, "f": {"0": 8, "1": 0, "2": 0, "3": 0, "4": 1}, "b": {}, "_coverageSchema": "1a1c01bbd47fc00a2c39e90264f33305004495a9", "hash": "a9f46d72213d0243d3e555c58d97b720a9b77af3"}}