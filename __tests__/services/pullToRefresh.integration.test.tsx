import { chapterService } from '../../services/chapterService';
import { questionService } from '../../services/questionService';
import { majorService } from '../../services/majorService';
import { userService } from '../../services/userService';
import { majorCourseService } from '../../services/majorCourseService';
import { optionService } from '../../services/optionService';
import { sessionService } from '../../services/sessionService';

// Mock the API service
jest.mock('../../services/api', () => ({
  api: {
    get: jest.fn(),
    post: jest.fn(),
    put: jest.fn(),
    delete: jest.fn(),
  },
}));

import { api } from '../../services/api';

describe('Pull-to-Refresh Integration Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('ChapterService', () => {
    it('should fetch chapters successfully', async () => {
      const mockChapters = [
        { id: '1', name: 'Chapter 1', courseId: 'course-1' },
        { id: '2', name: 'Chapter 2', courseId: 'course-1' },
      ];
      
      (api.get as jest.Mock).mockResolvedValue({ data: mockChapters });
      
      const result = await chapterService.getChapters();
      
      expect(api.get).toHaveBeenCalledWith('/chapters');
      expect(result).toEqual(mockChapters);
    });

    it('should handle fetch chapters error', async () => {
      (api.get as jest.Mock).mockRejectedValue(new Error('Network error'));
      
      await expect(chapterService.getChapters()).rejects.toThrow('Network error');
    });
  });

  describe('QuestionService', () => {
    it('should fetch questions successfully', async () => {
      const mockQuestions = [
        { id: '1', text: 'Question 1', chapterId: 'chapter-1' },
        { id: '2', text: 'Question 2', chapterId: 'chapter-1' },
      ];
      
      (api.get as jest.Mock).mockResolvedValue({ data: mockQuestions });
      
      const result = await questionService.getQuestions();
      
      expect(api.get).toHaveBeenCalledWith('/questions');
      expect(result).toEqual(mockQuestions);
    });

    it('should handle fetch questions error', async () => {
      (api.get as jest.Mock).mockRejectedValue(new Error('Network error'));
      
      await expect(questionService.getQuestions()).rejects.toThrow('Network error');
    });
  });

  describe('MajorService', () => {
    it('should fetch majors successfully', async () => {
      const mockMajors = [
        { id: '1', name: 'Computer Science' },
        { id: '2', name: 'Mathematics' },
      ];
      
      (api.get as jest.Mock).mockResolvedValue({ data: mockMajors });
      
      const result = await majorService.getMajors();
      
      expect(api.get).toHaveBeenCalledWith('/majors');
      expect(result).toEqual(mockMajors);
    });

    it('should handle fetch majors error', async () => {
      (api.get as jest.Mock).mockRejectedValue(new Error('Network error'));
      
      await expect(majorService.getMajors()).rejects.toThrow('Network error');
    });
  });

  describe('UserService', () => {
    it('should fetch users successfully', async () => {
      const mockUsers = [
        { id: '1', name: 'John Doe', email: '<EMAIL>' },
        { id: '2', name: 'Jane Smith', email: '<EMAIL>' },
      ];
      
      (api.get as jest.Mock).mockResolvedValue({ data: mockUsers });
      
      const result = await userService.getUsers();
      
      expect(api.get).toHaveBeenCalledWith('/users');
      expect(result).toEqual(mockUsers);
    });

    it('should handle fetch users error', async () => {
      (api.get as jest.Mock).mockRejectedValue(new Error('Network error'));
      
      await expect(userService.getUsers()).rejects.toThrow('Network error');
    });
  });

  describe('MajorCourseService', () => {
    it('should fetch major courses successfully', async () => {
      const mockMajorCourses = [
        { majorId: '1', courseId: '1' },
        { majorId: '1', courseId: '2' },
      ];
      
      (api.get as jest.Mock).mockResolvedValue({ data: mockMajorCourses });
      
      const result = await majorCourseService.getMajorCourses();
      
      expect(api.get).toHaveBeenCalledWith('/major-courses');
      expect(result).toEqual(mockMajorCourses);
    });

    it('should handle fetch major courses error', async () => {
      (api.get as jest.Mock).mockRejectedValue(new Error('Network error'));
      
      await expect(majorCourseService.getMajorCourses()).rejects.toThrow('Network error');
    });
  });

  describe('OptionService', () => {
    it('should fetch options successfully', async () => {
      const mockOptions = [
        { id: '1', text: 'Option A', questionId: 'question-1' },
        { id: '2', text: 'Option B', questionId: 'question-1' },
      ];
      
      (api.get as jest.Mock).mockResolvedValue({ data: mockOptions });
      
      const result = await optionService.getOptions();
      
      expect(api.get).toHaveBeenCalledWith('/options');
      expect(result).toEqual(mockOptions);
    });

    it('should handle fetch options error', async () => {
      (api.get as jest.Mock).mockRejectedValue(new Error('Network error'));
      
      await expect(optionService.getOptions()).rejects.toThrow('Network error');
    });
  });

  describe('SessionService', () => {
    it('should fetch sessions successfully', async () => {
      const mockSessions = [
        { id: '1', name: 'Session 1', userId: 'user-1' },
        { id: '2', name: 'Session 2', userId: 'user-1' },
      ];
      
      (api.get as jest.Mock).mockResolvedValue({ data: mockSessions });
      
      const result = await sessionService.getSessions();
      
      expect(api.get).toHaveBeenCalledWith('/sessions');
      expect(result).toEqual(mockSessions);
    });

    it('should handle fetch sessions error', async () => {
      (api.get as jest.Mock).mockRejectedValue(new Error('Network error'));
      
      await expect(sessionService.getSessions()).rejects.toThrow('Network error');
    });
  });

  describe('Concurrent Refresh Operations', () => {
    it('should handle multiple simultaneous refresh operations', async () => {
      const mockData = { data: [] };
      (api.get as jest.Mock).mockResolvedValue(mockData);

      // Simulate multiple refresh operations happening simultaneously
      const promises = [
        chapterService.getChapters(),
        questionService.getQuestions(),
        majorService.getMajors(),
        userService.getUsers(),
      ];

      const results = await Promise.all(promises);

      expect(results).toHaveLength(4);
      expect(api.get).toHaveBeenCalledTimes(4);
    });

    it('should handle partial failures in concurrent operations', async () => {
      (api.get as jest.Mock)
        .mockResolvedValueOnce({ data: [] }) // chapters success
        .mockRejectedValueOnce(new Error('Questions failed')) // questions fail
        .mockResolvedValueOnce({ data: [] }) // majors success
        .mockRejectedValueOnce(new Error('Users failed')); // users fail

      const results = await Promise.allSettled([
        chapterService.getChapters(),
        questionService.getQuestions(),
        majorService.getMajors(),
        userService.getUsers(),
      ]);

      expect(results[0].status).toBe('fulfilled');
      expect(results[1].status).toBe('rejected');
      expect(results[2].status).toBe('fulfilled');
      expect(results[3].status).toBe('rejected');
    });
  });
});
