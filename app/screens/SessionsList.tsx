import React, { useEffect, useState } from "react";
import {
  FlatList,
  ActivityIndicator,
  StyleSheet,
  Pressable,
  RefreshControl,
} from "react-native";
import { ThemedText } from "@/components/ThemedText";
import { ThemedView } from "@/components/ThemedView";
import { Session, sessionService } from "@/services/sessionService";
import { Link } from "expo-router";
import { textDirection } from "@/utils/rtl";

export default function SessionsListScreen() {
  const [sessions, setSessions] = useState<Session[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const loadSessions = async () => {
    try {
      setLoading(true);
      setError(null);
      const data = await sessionService.getSessions();
      setSessions(data);
    } catch (err) {
      setError("خطا در دریافت جلسات.");
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = async () => {
    try {
      setRefreshing(true);
      setError(null);
      const data = await sessionService.getSessions();
      setSessions(data);
    } catch (err) {
      setError("خطا در به‌روزرسانی جلسات.");
      console.error(err);
    } finally {
      setRefreshing(false);
    }
  };

  useEffect(() => {
    loadSessions();
  }, []);

  if (loading) {
    return (
      <ThemedView style={styles.centered}>
        <ActivityIndicator size="large" />
        <ThemedText>در حال بارگذاری جلسات...</ThemedText>
      </ThemedView>
    );
  }

  if (error) {
    return (
      <ThemedView style={styles.centered}>
        <ThemedText style={styles.errorText}>{error}</ThemedText>
      </ThemedView>
    );
  }

  return (
    <ThemedView style={styles.container}>
      <ThemedText style={styles.title}>جلسات</ThemedText>
      <Link href="/screens/SessionAdd" asChild>
        <Pressable style={styles.addButton}>
          <ThemedText style={styles.addButtonText}>افزودن جلسه جدید</ThemedText>
        </Pressable>
      </Link>
      {sessions.length === 0 ? (
        <ThemedText style={styles.noDataText}>هیچ جلسه‌ای یافت نشد.</ThemedText>
      ) : (
        <FlatList
          testID="sessions-flatlist"
          data={sessions}
          keyExtractor={(item) => item.id}
          renderItem={({ item }) => (
            <ThemedView style={styles.sessionItem}>
              <ThemedView style={styles.sessionItemContent}>
                <Link href={`/screens/SessionDetails?id=${item.id}`} asChild>
                  <Pressable>
                    <ThemedText style={styles.sessionName}>
                      #{item.id}
                    </ThemedText>
                    <ThemedText style={styles.sessionName}>
                      آزمون: {item.exam?.name}
                    </ThemedText>
                    <ThemedText style={styles.sessionName}>
                      دانشجو: {item.user?.first_name} {item.user?.last_name}
                    </ThemedText>
                  </Pressable>
                </Link>
              </ThemedView>
              <ThemedView style={styles.actions}>
                <Link href={`/screens/SessionDetails?id=${item.id}`} asChild>
                  <Pressable style={styles.actionButton}>
                    <ThemedText style={styles.actionButtonText}>
                      مشاهده
                    </ThemedText>
                  </Pressable>
                </Link>
                {/* Delete functionality would be implemented here */}
              </ThemedView>
            </ThemedView>
          )}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={handleRefresh}
              colors={["#007bff"]}
              tintColor="#007bff"
            />
          }
        />
      )}
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  centered: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  title: {
    fontSize: 24,
    fontWeight: "bold",
    marginBottom: 16,
  },
  addButton: {
    backgroundColor: "#007bff",
    padding: 10,
    borderRadius: 5,
    alignItems: "center",
    marginBottom: 16,
  },
  addButtonText: {
    color: "#fff",
    fontWeight: "bold",
  },
  sessionItem: {
    flexDirection: "col",
    justifyContent: "space-between",
    alignItems: "end",
    // alignItems: 'right',
    // textDirection: 'rtl',
    padding: 12,
    marginVertical: 8,
    borderWidth: 1,
    borderColor: "#ccc",
    borderRadius: 8,
  },
  sessionItemContent: {
    textAlign: "right",
    textDirection: "rtl",
    marginVertical: 4,
  },
  sessionName: {
    fontSize: 16,
  },
  actions: {
    flexDirection: "row-reverse",
  },
  actionButton: {
    backgroundColor: "#28a745",
    padding: 8,
    borderRadius: 5,
    marginLeft: 8,
  },
  actionButtonText: {
    color: "#fff",
  },
  errorText: {
    color: "red",
    fontSize: 16,
  },
  noDataText: {
    fontSize: 18,
    textAlign: "center",
    marginTop: 20,
  },
});
