import React from 'react';
import { Dialog, Portal, useTheme } from 'react-native-paper';
import { StyleSheet, type ViewStyle } from 'react-native';
import { PaperTextComponent } from './PaperText';
import { PaperButtonComponent } from './PaperButton';

export interface PaperDialogProps {
  visible: boolean;
  onDismiss: () => void;
  title?: string;
  children?: React.ReactNode;
  actions?: React.ReactNode;
  style?: ViewStyle;
  dismissable?: boolean;
}

export function PaperDialogComponent({
  visible,
  onDismiss,
  title,
  children,
  actions,
  style,
  dismissable = true,
}: PaperDialogProps) {
  return (
    <Portal>
      <Dialog
        visible={visible}
        onDismiss={onDismiss}
        style={[styles.dialog, style]}
        dismissable={dismissable}
      >
        {title && (
          <Dialog.Title style={styles.dialogTitle}>
            {title}
          </Dialog.Title>
        )}
        {children && (
          <Dialog.Content style={styles.dialogContent}>
            {children}
          </Dialog.Content>
        )}
        {actions && (
          <Dialog.Actions style={styles.dialogActions}>
            {actions}
          </Dialog.Actions>
        )}
      </Dialog>
    </Portal>
  );
}

// Confirmation Dialog
export function PaperConfirmDialog({
  visible,
  onDismiss,
  onConfirm,
  title = 'تأیید',
  message = 'آیا مطمئن هستید؟',
  confirmText = 'تأیید',
  cancelText = 'لغو',
  style,
}: {
  visible: boolean;
  onDismiss: () => void;
  onConfirm: () => void;
  title?: string;
  message?: string;
  confirmText?: string;
  cancelText?: string;
  style?: ViewStyle;
}) {
  return (
    <PaperDialogComponent
      visible={visible}
      onDismiss={onDismiss}
      title={title}
      style={style}
      actions={
        <>
          <PaperButtonComponent
            title={cancelText}
            onPress={onDismiss}
            mode="text"
          />
          <PaperButtonComponent
            title={confirmText}
            onPress={() => {
              onConfirm();
              onDismiss();
            }}
            mode="contained"
          />
        </>
      }
    >
      <PaperTextComponent variant="bodyMedium">
        {message}
      </PaperTextComponent>
    </PaperDialogComponent>
  );
}

// Delete Confirmation Dialog
export function PaperDeleteDialog({
  visible,
  onDismiss,
  onConfirm,
  itemName,
  style,
}: {
  visible: boolean;
  onDismiss: () => void;
  onConfirm: () => void;
  itemName?: string;
  style?: ViewStyle;
}) {
  const message = itemName 
    ? `آیا مطمئن هستید که می‌خواهید "${itemName}" را حذف کنید؟`
    : 'آیا مطمئن هستید که می‌خواهید این مورد را حذف کنید؟';

  return (
    <PaperConfirmDialog
      visible={visible}
      onDismiss={onDismiss}
      onConfirm={onConfirm}
      title="حذف"
      message={message}
      confirmText="حذف"
      cancelText="لغو"
      style={style}
    />
  );
}

// Alert Dialog
export function PaperAlertDialog({
  visible,
  onDismiss,
  title = 'اطلاع',
  message,
  buttonText = 'تأیید',
  style,
}: {
  visible: boolean;
  onDismiss: () => void;
  title?: string;
  message: string;
  buttonText?: string;
  style?: ViewStyle;
}) {
  return (
    <PaperDialogComponent
      visible={visible}
      onDismiss={onDismiss}
      title={title}
      style={style}
      actions={
        <PaperButtonComponent
          title={buttonText}
          onPress={onDismiss}
          mode="contained"
        />
      }
    >
      <PaperTextComponent variant="bodyMedium">
        {message}
      </PaperTextComponent>
    </PaperDialogComponent>
  );
}

// Loading Dialog
export function PaperLoadingDialog({
  visible,
  message = 'در حال بارگذاری...',
  style,
}: {
  visible: boolean;
  message?: string;
  style?: ViewStyle;
}) {
  return (
    <PaperDialogComponent
      visible={visible}
      onDismiss={() => {}} // Non-dismissable
      style={style}
      dismissable={false}
    >
      <Dialog.Content style={styles.loadingContent}>
        <PaperTextComponent variant="bodyMedium" style={styles.loadingText}>
          {message}
        </PaperTextComponent>
      </Dialog.Content>
    </PaperDialogComponent>
  );
}

const styles = StyleSheet.create({
  dialog: {
    marginHorizontal: 20,
  },
  dialogTitle: {
    fontFamily: 'Vazirmatn',
    fontSize: 20,
    fontWeight: '600',
    textAlign: 'right',
  },
  dialogContent: {
    paddingVertical: 16,
  },
  dialogActions: {
    justifyContent: 'flex-end',
    paddingHorizontal: 8,
    paddingBottom: 8,
  },
  loadingContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 24,
  },
  loadingText: {
    marginLeft: 16,
    textAlign: 'center',
  },
});

// Export as default for easier importing
export default PaperDialogComponent;
