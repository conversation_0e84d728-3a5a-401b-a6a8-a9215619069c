import React, { useEffect, useState } from "react";
import { StyleSheet, SafeAreaView } from "react-native";
import { Surface } from "react-native-paper";
import { Question, questionService } from "@/services/questionService";
import { useLocalSearchParams, router } from "expo-router";
import {
  PaperListAppBar,
  PaperRefreshableFlatList,
  PaperCard,
  PaperCardTitle,
  PaperCardActions,
  PaperButton,
  PaperTitle,
  PaperBody,
  PaperAddFAB,
  useRefresh,
} from "@/components/paper";
import { t } from "@/constants/Localization";

export default function QuestionsListScreen() {
  const { chapterId, courseId, courseName } = useLocalSearchParams<{ chapterId: string, courseId: string, courseName: string }>();
  const [questions, setQuestions] = useState<Question[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const loadQuestions = async () => {
    try {
      setError(null);
      const data = await questionService.getQuestions();
      if (chapterId) {
        const filteredQuestions = data.filter(
          (question) => question.chapter_id === chapterId
        );
        setQuestions(filteredQuestions);
      } else {
        setQuestions(data);
      }
    } catch (err) {
      setError(t('networkError') || "خطا در دریافت سوالات.");
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  const { refreshing, onRefresh } = useRefresh(loadQuestions);

  useEffect(() => {
    loadQuestions();
  }, [chapterId]);

  const handleAddQuestion = () => {
    router.push({
      pathname: "/screens/QuestionAdd",
      params: { chapter_id: chapterId, course_id: courseId, courseName: courseName }
    });
  };

  const handleQuestionPress = (question: Question) => {
    router.push(`/screens/QuestionDetails?id=${question.id}`);
  };

  const handleEditQuestion = (question: Question) => {
    router.push(`/screens/QuestionEdit?id=${question.id}`);
  };

  const handleViewOptions = (question: Question) => {
    router.push(`/screens/OptionsList?questionId=${question.id}`);
  };

  const renderQuestionItem = ({ item }: { item: Question }) => (
    <PaperCard
      style={styles.questionCard}
      onPress={() => handleQuestionPress(item)}
    >
      <PaperCardTitle
        title={`#${item.id}`}
        subtitle={item.question_text}
        left={(props) => (
          <Surface {...props} style={styles.questionIcon}>
            <PaperBody size="small">Q</PaperBody>
          </Surface>
        )}
      />
      <PaperCardActions>
        <PaperButton
          title={t('edit')}
          mode="outlined"
          onPress={() => handleEditQuestion(item)}
          icon="pencil"
          compact
        />
        <PaperButton
          title={t('options') || 'گزینه‌ها'}
          mode="outlined"
          onPress={() => handleViewOptions(item)}
          icon="format-list-bulleted"
          compact
        />
      </PaperCardActions>
    </PaperCard>
  );

  return (
    <SafeAreaView style={styles.container}>
      {/* App Bar */}
      <PaperListAppBar
        title={t('questions')}
        onAddPress={handleAddQuestion}
      />

      {/* Content */}
      <Surface style={styles.content}>
        <PaperRefreshableFlatList
          data={questions}
          renderItem={renderQuestionItem}
          keyExtractor={(item) => item.id}
          refreshing={refreshing}
          onRefresh={onRefresh}
          loading={loading}
          style={styles.list}
          contentContainerStyle={styles.listContent}
          emptyTitle={t('noQuestionsFound') || 'سوالی یافت نشد'}
          emptyDescription={t('noQuestionsDescription') || 'برای افزودن سوال جدید از دکمه + استفاده کنید'}
          emptyIcon="help-circle"
          emptyAction={
            <PaperButton
              title={t('addQuestion') || 'افزودن سوال'}
              mode="contained"
              onPress={handleAddQuestion}
              icon="plus"
            />
          }
        />
      </Surface>

      {/* Floating Action Button */}
      <PaperAddFAB
        onPress={handleAddQuestion}
        visible={!loading}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
  },
  list: {
    flex: 1,
  },
  listContent: {
    padding: 8,
  },
  questionCard: {
    marginVertical: 4,
    marginHorizontal: 8,
  },
  questionIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
});
