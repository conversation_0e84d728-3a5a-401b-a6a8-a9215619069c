import { BottomTabBarButtonProps } from '@react-navigation/bottom-tabs';
import { TouchableRipple } from 'react-native-paper';
import * as Haptics from 'expo-haptics';

export function HapticTab(props: BottomTabBarButtonProps) {
  return (
    <TouchableRipple
      {...props}
      onPressIn={(ev) => {
        if (process.env.EXPO_OS === 'ios') {
          // Add a soft haptic feedback when pressing down on the tabs.
          Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
        }
        props.onPressIn?.(ev);
      }}
      rippleColor="rgba(0, 0, 0, .32)"
      borderless
    />
  );
}
