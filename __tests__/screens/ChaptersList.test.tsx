import React from 'react';
import { render, fireEvent, waitFor } from '@testing-library/react-native';
import { Alert } from 'react-native';
import ChaptersListScreen from '../../app/screens/ChaptersList';
import { chapterService } from '../../services/chapterService';

// Mock the chapter service
jest.mock('../../services/chapterService', () => ({
  chapterService: {
    getChapters: jest.fn(),
    deleteChapter: jest.fn(),
  },
}));

// Mock useLocalSearchParams
jest.mock('expo-router', () => ({
  ...jest.requireActual('expo-router'),
  useLocalSearchParams: () => ({ courseId: 'test-course-id' }),
}));

const mockChapters = [
  {
    id: '1',
    name: 'Chapter 1',
    courseId: 'test-course-id',
    createdAt: '2023-01-01T00:00:00Z',
  },
  {
    id: '2', 
    name: 'Chapter 2',
    courseId: 'test-course-id',
    createdAt: '2023-01-02T00:00:00Z',
  },
];

describe('ChaptersListScreen', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    (chapterService.getChapters as jest.Mock).mockResolvedValue(mockChapters);
  });

  it('renders correctly with chapters', async () => {
    const { getByText, getByTestId } = render(<ChaptersListScreen />);
    
    await waitFor(() => {
      expect(getByText('Chapters')).toBeTruthy();
      expect(getByText('Chapter 1')).toBeTruthy();
      expect(getByText('Chapter 2')).toBeTruthy();
    });
  });

  it('shows loading state initially', () => {
    const { getByText } = render(<ChaptersListScreen />);
    expect(getByText('Loading chapters...')).toBeTruthy();
  });

  it('handles pull-to-refresh functionality', async () => {
    const { getByTestId } = render(<ChaptersListScreen />);
    
    // Wait for initial load
    await waitFor(() => {
      expect(chapterService.getChapters).toHaveBeenCalledTimes(1);
    });

    // Find the FlatList and trigger refresh
    const flatList = getByTestId('chapters-flatlist');
    
    // Simulate pull-to-refresh
    fireEvent(flatList, 'refresh');
    
    await waitFor(() => {
      expect(chapterService.getChapters).toHaveBeenCalledTimes(2);
    });
  });

  it('displays error message when fetch fails', async () => {
    (chapterService.getChapters as jest.Mock).mockRejectedValue(new Error('Network error'));
    
    const { getByText } = render(<ChaptersListScreen />);
    
    await waitFor(() => {
      expect(getByText('Failed to fetch chapters.')).toBeTruthy();
    });
  });

  it('shows no data message when chapters list is empty', async () => {
    (chapterService.getChapters as jest.Mock).mockResolvedValue([]);
    
    const { getByText } = render(<ChaptersListScreen />);
    
    await waitFor(() => {
      expect(getByText('No chapters found.')).toBeTruthy();
    });
  });

  it('handles delete chapter functionality', async () => {
    (chapterService.deleteChapter as jest.Mock).mockResolvedValue(undefined);
    const alertSpy = jest.spyOn(Alert, 'alert');
    
    const { getByText } = render(<ChaptersListScreen />);
    
    await waitFor(() => {
      expect(getByText('Chapter 1')).toBeTruthy();
    });

    // Find and press delete button
    const deleteButton = getByText('Delete');
    fireEvent.press(deleteButton);

    // Verify Alert.alert was called
    expect(alertSpy).toHaveBeenCalledWith(
      'Delete Chapter',
      'Are you sure you want to delete this chapter?',
      expect.any(Array),
      expect.any(Object)
    );
  });

  it('refreshes data after successful delete', async () => {
    (chapterService.deleteChapter as jest.Mock).mockResolvedValue(undefined);
    const alertSpy = jest.spyOn(Alert, 'alert').mockImplementation((title, message, buttons) => {
      // Simulate user pressing "Delete" button
      if (buttons && buttons[1] && buttons[1].onPress) {
        buttons[1].onPress();
      }
    });
    
    const { getByText } = render(<ChaptersListScreen />);
    
    await waitFor(() => {
      expect(getByText('Chapter 1')).toBeTruthy();
    });

    // Trigger delete
    const deleteButton = getByText('Delete');
    fireEvent.press(deleteButton);

    await waitFor(() => {
      // Should call getChapters again after delete (initial + refresh)
      expect(chapterService.getChapters).toHaveBeenCalledTimes(2);
    });

    alertSpy.mockRestore();
  });
});
