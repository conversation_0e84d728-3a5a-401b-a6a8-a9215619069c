import { t } from '@/constants/Localization';
import { ApiError } from '@/services/api';

// Error types for categorization
export enum ErrorType {
  NETWORK = 'NETWORK',
  SERVER = 'SERVER',
  VALIDATION = 'VALIDATION',
  TIMEOUT = 'TIMEOUT',
  UNKNOWN = 'UNKNOWN',
}

// Error severity levels
export enum ErrorSeverity {
  LOW = 'LOW',
  MEDIUM = 'MEDIUM',
  HIGH = 'HIGH',
  CRITICAL = 'CRITICAL',
}

// Structured error information
export interface ErrorInfo {
  type: ErrorType;
  severity: ErrorSeverity;
  message: string;
  userMessage: string;
  canRetry: boolean;
  statusCode?: number;
  originalError?: any;
}

// Network error detection patterns
const NETWORK_ERROR_PATTERNS = [
  'network request failed',
  'network error',
  'fetch failed',
  'connection refused',
  'connection timeout',
  'no internet connection',
  'dns lookup failed',
  'socket timeout',
];

const TIMEOUT_ERROR_PATTERNS = [
  'timeout',
  'request timeout',
  'connection timeout',
  'read timeout',
];

/**
 * Check if error is a network-related error
 */
function isNetworkError(error: any): boolean {
  if (!error) return false;
  
  const errorMessage = (error.message || '').toLowerCase();
  const errorName = (error.name || '').toLowerCase();
  
  return NETWORK_ERROR_PATTERNS.some(pattern => 
    errorMessage.includes(pattern) || errorName.includes(pattern)
  );
}

/**
 * Check if error is a timeout error
 */
function isTimeoutError(error: any): boolean {
  if (!error) return false;
  
  const errorMessage = (error.message || '').toLowerCase();
  const errorName = (error.name || '').toLowerCase();
  
  return TIMEOUT_ERROR_PATTERNS.some(pattern => 
    errorMessage.includes(pattern) || errorName.includes(pattern)
  );
}

/**
 * Get user-friendly message based on HTTP status code
 */
function getStatusCodeMessage(statusCode: number): string {
  switch (statusCode) {
    case 400:
      return t('badRequest');
    case 401:
      return t('unauthorized');
    case 403:
      return t('forbidden');
    case 404:
      return t('notFound');
    case 409:
      return t('conflict');
    case 500:
      return t('internalServerError');
    case 503:
      return t('serviceUnavailable');
    default:
      if (statusCode >= 400 && statusCode < 500) {
        return t('validationError');
      } else if (statusCode >= 500) {
        return t('serverError');
      }
      return t('unknownError');
  }
}

/**
 * Determine if an error can be retried
 */
function canRetryError(error: any, statusCode?: number): boolean {
  // Network errors can usually be retried
  if (isNetworkError(error) || isTimeoutError(error)) {
    return true;
  }
  
  // Server errors (5xx) can be retried
  if (statusCode && statusCode >= 500) {
    return true;
  }
  
  // Rate limiting (429) can be retried
  if (statusCode === 429) {
    return true;
  }
  
  // Client errors (4xx) usually shouldn't be retried
  if (statusCode && statusCode >= 400 && statusCode < 500) {
    return false;
  }
  
  return false;
}

/**
 * Determine error severity
 */
function getErrorSeverity(error: any, statusCode?: number): ErrorSeverity {
  // Critical errors
  if (statusCode === 500 || statusCode === 503) {
    return ErrorSeverity.CRITICAL;
  }
  
  // High severity errors
  if (statusCode === 401 || statusCode === 403) {
    return ErrorSeverity.HIGH;
  }
  
  // Medium severity errors
  if (statusCode && statusCode >= 400 && statusCode < 500) {
    return ErrorSeverity.MEDIUM;
  }
  
  // Network errors are medium severity
  if (isNetworkError(error)) {
    return ErrorSeverity.MEDIUM;
  }
  
  return ErrorSeverity.LOW;
}

/**
 * Main error handler function
 */
export function handleError(error: any): ErrorInfo {
  let errorType = ErrorType.UNKNOWN;
  let statusCode: number | undefined;
  let message = t('unknownError');
  let userMessage = t('unknownError');
  
  // Handle ApiError instances
  if (error instanceof ApiError) {
    statusCode = error.status;
    message = error.message;
    userMessage = getStatusCodeMessage(statusCode);
    
    if (statusCode >= 500) {
      errorType = ErrorType.SERVER;
    } else if (statusCode >= 400) {
      errorType = ErrorType.VALIDATION;
    }
  }
  // Handle network errors
  else if (isNetworkError(error)) {
    errorType = ErrorType.NETWORK;
    message = error.message || t('connectionError');
    userMessage = t('connectionError');
  }
  // Handle timeout errors
  else if (isTimeoutError(error)) {
    errorType = ErrorType.TIMEOUT;
    message = error.message || t('timeoutError');
    userMessage = t('timeoutError');
  }
  // Handle generic errors
  else if (error instanceof Error) {
    message = error.message;
    userMessage = t('unknownError');
  }
  // Handle string errors
  else if (typeof error === 'string') {
    message = error;
    userMessage = t('unknownError');
  }
  
  const canRetry = canRetryError(error, statusCode);
  const severity = getErrorSeverity(error, statusCode);
  
  return {
    type: errorType,
    severity,
    message,
    userMessage,
    canRetry,
    statusCode,
    originalError: error,
  };
}

/**
 * Get appropriate action message for error
 */
export function getErrorActionMessage(errorInfo: ErrorInfo): string {
  if (errorInfo.type === ErrorType.NETWORK) {
    return t('checkConnection');
  }
  
  if (errorInfo.canRetry) {
    return t('retry');
  }
  
  return t('tryAgainLater');
}

/**
 * Log error for debugging (in development)
 */
export function logError(errorInfo: ErrorInfo, context?: string): void {
  if (__DEV__) {
    console.group(`🚨 Error ${context ? `in ${context}` : ''}`);
    console.error('Type:', errorInfo.type);
    console.error('Severity:', errorInfo.severity);
    console.error('Message:', errorInfo.message);
    console.error('User Message:', errorInfo.userMessage);
    console.error('Can Retry:', errorInfo.canRetry);
    console.error('Status Code:', errorInfo.statusCode);
    console.error('Original Error:', errorInfo.originalError);
    console.groupEnd();
  }
}
