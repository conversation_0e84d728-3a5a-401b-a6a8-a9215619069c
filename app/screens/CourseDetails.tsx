import React, { useEffect, useState } from 'react';
import { ActivityIndicator, StyleSheet } from 'react-native';
import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { Course, courseService } from '@/services/courseService';
import { useLocalSearchParams } from 'expo-router';

export default function CourseDetailsScreen() {
  const { id } = useLocalSearchParams();
  const [course, setCourse] = useState<Course | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (id) {
      const fetchCourse = async () => {
        try {
          const data = await courseService.getCourse(id as string);
          setCourse(data);
        } catch (err) {
          setError('Failed to fetch course details.');
          console.error(err);
        } finally {
          setLoading(false);
        }
      };
      fetchCourse();
    } else {
      setError('Course ID not provided.');
      setLoading(false);
    }
  }, [id]);

  if (loading) {
    return (
      <ThemedView style={styles.centered}>
        <ActivityIndicator size="large" />
        <ThemedText>Loading course details...</ThemedText>
      </ThemedView>
    );
  }

  if (error) {
    return (
      <ThemedView style={styles.centered}>
        <ThemedText style={styles.errorText}>{error}</ThemedText>
      </ThemedView>
    );
  }

  if (!course) {
    return (
      <ThemedView style={styles.centered}>
        <ThemedText>Course not found.</ThemedText>
      </ThemedView>
    );
  }

  return (
    <ThemedView style={styles.container}>
      <ThemedText style={styles.title}>Course Details</ThemedText>
      <ThemedView style={styles.detailItem}>
        <ThemedText style={styles.label}>ID:</ThemedText>
        <ThemedText>{course.id}</ThemedText>
      </ThemedView>
      <ThemedView style={styles.detailItem}>
        <ThemedText style={styles.label}>Name:</ThemedText>
        <ThemedText>{course.name}</ThemedText>
      </ThemedView>
      {course.createdAt && (
        <ThemedView style={styles.detailItem}>
          <ThemedText style={styles.label}>Created At:</ThemedText>
          <ThemedText>{new Date(course.createdAt).toLocaleString()}</ThemedText>
        </ThemedView>
      )}
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  centered: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 20,
  },
  detailItem: {
    flexDirection: 'row',
    marginBottom: 10,
  },
  label: {
    fontWeight: 'bold',
    marginRight: 5,
  },
  errorText: {
    color: 'red',
    fontSize: 16,
  },
});
