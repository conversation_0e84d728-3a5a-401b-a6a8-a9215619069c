import { api } from './api';

// Define types
export interface Option {
  id: string;
  option_text: string;
  question_id: number;
  is_correct: boolean;
  createdAt?: string;
}

export interface CreateOptionDto {
  option_text: string;
  question_id: number;
  is_correct: boolean;
}

export interface UpdateOptionDto {
  option_text?: string;
  question_id?: number;
  is_correct?: boolean;
}

// Option service for handling option-related API calls
export const optionService = {
  // Get all options
  async getOptions(): Promise<Option[]> {
    const response = await api.get<Option[]>('/v1/api/options');
    return response.data;
  },

  // Get a specific option
  async getOption(id: string): Promise<Option> {
    const response = await api.get<Option>(`/v1/api/options/${id}`);
    return response.data;
  },

  // Create a new option
  async createOption(optionData: CreateOptionDto): Promise<Option> {
    const response = await api.post<Option>('/v1/api/options', optionData);
    return response.data;
  },

  // Update an option
  async updateOption(id: string, optionData: UpdateOptionDto): Promise<Option> {
    const response = await api.patch<Option>(`/v1/api/options/${id}`, optionData);
    return response.data;
  },

  // Delete an option
  async deleteOption(id: string): Promise<void> {
    await api.delete(`/v1/api/options/${id}`);
  },

  // Get the question for an option
  async getOptionQuestion(id: string): Promise<any> { // Assuming 'any' for now, will define Question interface later
    const response = await api.get<any>(`/v1/api/options/${id}/question`);
    return response.data;
  }
};
