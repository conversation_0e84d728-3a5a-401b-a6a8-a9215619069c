import React from 'react';
import { 
  ScrollView, 
  RefreshControl, 
  FlatList, 
  SectionList,
  type ViewStyle,
  type ListRenderItem,
  type SectionListData,
  type SectionListRenderItem,
} from 'react-native';
import { useTheme, ActivityIndicator } from 'react-native-paper';
import { PaperEmptyList } from './PaperList';
import { t } from '@/constants/Localization';

export interface PaperRefreshableScrollViewProps {
  children: React.ReactNode;
  refreshing?: boolean;
  onRefresh?: () => void;
  style?: ViewStyle;
  contentContainerStyle?: ViewStyle;
  showsVerticalScrollIndicator?: boolean;
}

export function PaperRefreshableScrollView({
  children,
  refreshing = false,
  onRefresh,
  style,
  contentContainerStyle,
  showsVerticalScrollIndicator = false,
}: PaperRefreshableScrollViewProps) {
  const theme = useTheme();

  return (
    <ScrollView
      style={style}
      contentContainerStyle={contentContainerStyle}
      showsVerticalScrollIndicator={showsVerticalScrollIndicator}
      refreshControl={
        onRefresh ? (
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={[theme.colors.primary]} // Android
            tintColor={theme.colors.primary} // iOS
            progressBackgroundColor={theme.colors.surface}
            title={refreshing ? t('refreshing') : t('pullToRefresh')}
            titleColor={theme.colors.onSurface}
          />
        ) : undefined
      }
    >
      {children}
    </ScrollView>
  );
}

export interface PaperRefreshableFlatListProps<T> {
  data: T[];
  renderItem: ListRenderItem<T>;
  keyExtractor: (item: T, index: number) => string;
  refreshing?: boolean;
  onRefresh?: () => void;
  onEndReached?: () => void;
  onEndReachedThreshold?: number;
  style?: ViewStyle;
  contentContainerStyle?: ViewStyle;
  showsVerticalScrollIndicator?: boolean;
  emptyTitle?: string;
  emptyDescription?: string;
  emptyIcon?: string;
  emptyAction?: React.ReactNode;
  loading?: boolean;
  loadingMore?: boolean;
  numColumns?: number;
  horizontal?: boolean;
}

export function PaperRefreshableFlatList<T>({
  data,
  renderItem,
  keyExtractor,
  refreshing = false,
  onRefresh,
  onEndReached,
  onEndReachedThreshold = 0.1,
  style,
  contentContainerStyle,
  showsVerticalScrollIndicator = false,
  emptyTitle,
  emptyDescription,
  emptyIcon,
  emptyAction,
  loading = false,
  loadingMore = false,
  numColumns = 1,
  horizontal = false,
}: PaperRefreshableFlatListProps<T>) {
  const theme = useTheme();

  const renderFooter = () => {
    if (loadingMore) {
      return (
        <ActivityIndicator 
          animating={true} 
          color={theme.colors.primary}
          style={{ paddingVertical: 16 }}
        />
      );
    }
    return null;
  };

  const renderEmpty = () => {
    if (loading) {
      return (
        <ActivityIndicator 
          animating={true} 
          color={theme.colors.primary}
          style={{ paddingVertical: 32 }}
        />
      );
    }

    return (
      <PaperEmptyList
        title={emptyTitle}
        description={emptyDescription}
        icon={emptyIcon}
        action={emptyAction}
      />
    );
  };

  return (
    <FlatList
      data={data}
      renderItem={renderItem}
      keyExtractor={keyExtractor}
      style={style}
      contentContainerStyle={contentContainerStyle}
      showsVerticalScrollIndicator={showsVerticalScrollIndicator}
      numColumns={numColumns}
      horizontal={horizontal}
      onEndReached={onEndReached}
      onEndReachedThreshold={onEndReachedThreshold}
      ListFooterComponent={renderFooter}
      ListEmptyComponent={renderEmpty}
      refreshControl={
        onRefresh ? (
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={[theme.colors.primary]} // Android
            tintColor={theme.colors.primary} // iOS
            progressBackgroundColor={theme.colors.surface}
            title={refreshing ? t('refreshing') : t('pullToRefresh')}
            titleColor={theme.colors.onSurface}
          />
        ) : undefined
      }
    />
  );
}

export interface PaperRefreshableSectionListProps<T, S> {
  sections: SectionListData<T, S>[];
  renderItem: SectionListRenderItem<T, S>;
  renderSectionHeader?: (info: { section: SectionListData<T, S> }) => React.ReactElement | null;
  keyExtractor: (item: T, index: number) => string;
  refreshing?: boolean;
  onRefresh?: () => void;
  onEndReached?: () => void;
  onEndReachedThreshold?: number;
  style?: ViewStyle;
  contentContainerStyle?: ViewStyle;
  showsVerticalScrollIndicator?: boolean;
  emptyTitle?: string;
  emptyDescription?: string;
  emptyIcon?: string;
  emptyAction?: React.ReactNode;
  loading?: boolean;
  loadingMore?: boolean;
}

export function PaperRefreshableSectionList<T, S>({
  sections,
  renderItem,
  renderSectionHeader,
  keyExtractor,
  refreshing = false,
  onRefresh,
  onEndReached,
  onEndReachedThreshold = 0.1,
  style,
  contentContainerStyle,
  showsVerticalScrollIndicator = false,
  emptyTitle,
  emptyDescription,
  emptyIcon,
  emptyAction,
  loading = false,
  loadingMore = false,
}: PaperRefreshableSectionListProps<T, S>) {
  const theme = useTheme();

  const renderFooter = () => {
    if (loadingMore) {
      return (
        <ActivityIndicator 
          animating={true} 
          color={theme.colors.primary}
          style={{ paddingVertical: 16 }}
        />
      );
    }
    return null;
  };

  const renderEmpty = () => {
    if (loading) {
      return (
        <ActivityIndicator 
          animating={true} 
          color={theme.colors.primary}
          style={{ paddingVertical: 32 }}
        />
      );
    }

    return (
      <PaperEmptyList
        title={emptyTitle}
        description={emptyDescription}
        icon={emptyIcon}
        action={emptyAction}
      />
    );
  };

  return (
    <SectionList
      sections={sections}
      renderItem={renderItem}
      renderSectionHeader={renderSectionHeader}
      keyExtractor={keyExtractor}
      style={style}
      contentContainerStyle={contentContainerStyle}
      showsVerticalScrollIndicator={showsVerticalScrollIndicator}
      onEndReached={onEndReached}
      onEndReachedThreshold={onEndReachedThreshold}
      ListFooterComponent={renderFooter}
      ListEmptyComponent={renderEmpty}
      refreshControl={
        onRefresh ? (
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={[theme.colors.primary]} // Android
            tintColor={theme.colors.primary} // iOS
            progressBackgroundColor={theme.colors.surface}
            title={refreshing ? t('refreshing') : t('pullToRefresh')}
            titleColor={theme.colors.onSurface}
          />
        ) : undefined
      }
    />
  );
}

// Hook for managing refresh state
export function useRefresh(refreshFunction: () => Promise<void>) {
  const [refreshing, setRefreshing] = React.useState(false);

  const onRefresh = React.useCallback(async () => {
    setRefreshing(true);
    try {
      await refreshFunction();
    } catch (error) {
      console.error('Refresh error:', error);
    } finally {
      setRefreshing(false);
    }
  }, [refreshFunction]);

  return { refreshing, onRefresh };
}

// Hook for managing pagination
export function usePagination<T>(
  fetchFunction: (page: number) => Promise<T[]>,
  initialPage: number = 1
) {
  const [data, setData] = React.useState<T[]>([]);
  const [loading, setLoading] = React.useState(false);
  const [loadingMore, setLoadingMore] = React.useState(false);
  const [page, setPage] = React.useState(initialPage);
  const [hasMore, setHasMore] = React.useState(true);

  const loadData = React.useCallback(async (pageNumber: number, append: boolean = false) => {
    if (append) {
      setLoadingMore(true);
    } else {
      setLoading(true);
    }

    try {
      const newData = await fetchFunction(pageNumber);
      
      if (append) {
        setData(prevData => [...prevData, ...newData]);
      } else {
        setData(newData);
      }

      setHasMore(newData.length > 0);
      setPage(pageNumber);
    } catch (error) {
      console.error('Load data error:', error);
    } finally {
      setLoading(false);
      setLoadingMore(false);
    }
  }, [fetchFunction]);

  const refresh = React.useCallback(() => {
    return loadData(initialPage, false);
  }, [loadData, initialPage]);

  const loadMore = React.useCallback(() => {
    if (!loadingMore && hasMore) {
      loadData(page + 1, true);
    }
  }, [loadData, page, loadingMore, hasMore]);

  React.useEffect(() => {
    loadData(initialPage, false);
  }, []);

  return {
    data,
    loading,
    loadingMore,
    hasMore,
    refresh,
    loadMore,
  };
}
