import React from 'react';
import { FAB, useTheme } from 'react-native-paper';
import { StyleSheet, type ViewStyle } from 'react-native';

export interface PaperFABProps {
  icon: string;
  onPress: () => void;
  style?: ViewStyle;
  size?: 'small' | 'medium' | 'large';
  mode?: 'surface' | 'primary' | 'secondary' | 'tertiary';
  disabled?: boolean;
  loading?: boolean;
  label?: string;
  uppercase?: boolean;
  visible?: boolean;
}

export function PaperFABComponent({
  icon,
  onPress,
  style,
  size = 'medium',
  mode = 'primary',
  disabled = false,
  loading = false,
  label,
  uppercase = false,
  visible = true,
}: PaperFABProps) {
  if (!visible) return null;

  return (
    <FAB
      icon={icon}
      onPress={onPress}
      style={[styles.fab, style]}
      size={size}
      mode={mode}
      disabled={disabled}
      loading={loading}
      label={label}
      uppercase={uppercase}
    />
  );
}

// FAB Group component
export interface PaperFABGroupAction {
  icon: string;
  label: string;
  onPress: () => void;
  style?: ViewStyle;
  color?: string;
  disabled?: boolean;
}

export function PaperFABGroup({
  open,
  onStateChange,
  actions,
  icon,
  style,
  fabStyle,
  visible = true,
}: {
  open: boolean;
  onStateChange: (state: { open: boolean }) => void;
  actions: PaperFABGroupAction[];
  icon?: string;
  style?: ViewStyle;
  fabStyle?: ViewStyle;
  visible?: boolean;
}) {
  if (!visible) return null;

  return (
    <FAB.Group
      open={open}
      visible={visible}
      icon={icon || (open ? 'close' : 'plus')}
      actions={actions.map(action => ({
        ...action,
        labelStyle: styles.fabGroupLabel,
      }))}
      onStateChange={onStateChange}
      style={[styles.fabGroup, style]}
      fabStyle={[styles.fabGroupFab, fabStyle]}
    />
  );
}

// Extended FAB component
export function PaperExtendedFAB({
  label,
  icon,
  onPress,
  style,
  mode = 'primary',
  disabled = false,
  loading = false,
  uppercase = false,
  visible = true,
}: {
  label: string;
  icon?: string;
  onPress: () => void;
  style?: ViewStyle;
  mode?: 'surface' | 'primary' | 'secondary' | 'tertiary';
  disabled?: boolean;
  loading?: boolean;
  uppercase?: boolean;
  visible?: boolean;
}) {
  if (!visible) return null;

  return (
    <FAB
      icon={icon}
      label={label}
      onPress={onPress}
      style={[styles.extendedFab, style]}
      mode={mode}
      disabled={disabled}
      loading={loading}
      uppercase={uppercase}
    />
  );
}

// Add FAB (commonly used)
export function PaperAddFAB({
  onPress,
  style,
  visible = true,
}: {
  onPress: () => void;
  style?: ViewStyle;
  visible?: boolean;
}) {
  return (
    <PaperFABComponent
      icon="plus"
      onPress={onPress}
      style={[styles.addFab, style]}
      visible={visible}
    />
  );
}

// Edit FAB (commonly used)
export function PaperEditFAB({
  onPress,
  style,
  visible = true,
}: {
  onPress: () => void;
  style?: ViewStyle;
  visible?: boolean;
}) {
  return (
    <PaperFABComponent
      icon="pencil"
      onPress={onPress}
      style={[styles.editFab, style]}
      size="small"
      visible={visible}
    />
  );
}

const styles = StyleSheet.create({
  fab: {
    position: 'absolute',
    margin: 16,
    right: 0,
    bottom: 0,
  },
  fabGroup: {
    position: 'absolute',
    margin: 16,
    right: 0,
    bottom: 0,
  },
  fabGroupFab: {
    // Custom styling for FAB group main button
  },
  fabGroupLabel: {
    fontFamily: 'Vazirmatn',
    fontSize: 14,
    textAlign: 'right',
  },
  extendedFab: {
    position: 'absolute',
    margin: 16,
    right: 0,
    bottom: 0,
  },
  addFab: {
    backgroundColor: '#4CAF50', // Green for add actions
  },
  editFab: {
    backgroundColor: '#FF9800', // Orange for edit actions
  },
});

// Export as default for easier importing
export default PaperFABComponent;
