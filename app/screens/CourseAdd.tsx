import React, { useState, useEffect } from "react";
import { StyleSheet, SafeAreaView } from "react-native";
import { Surface } from "react-native-paper";
import { courseService } from "@/services/courseService";
import { courseGroupService, CourseGroup } from "@/services/courseGroupService";
import KeyboardAvoidingWrapper from "@/components/KeyboardAvoidingWrapper";
import { router } from "expo-router";
import {
  PaperFormAppBar,
  PaperTextInput,
  PaperButton,
  PaperFormField,
  PaperFormSection,
  PaperAlertDialog,
} from "@/components/paper";
import { t } from "@/constants/Localization";
import { useApiError } from "@/hooks/useApiError";

export default function CourseAddScreen() {
  const [name, setName] = useState("");
  const [description, setDescription] = useState("");
  const [coefficient, setCoefficient] = useState("");
  const [negativeMarkingFactor, setNegativeMarkingFactor] = useState("");
  const [courseGroupId, setCourseGroupId] = useState<string | null>(null);
  const [courseGroups, setCourseGroups] = useState<CourseGroup[]>([]);
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState<{[key: string]: string}>({});
  const [showAlert, setShowAlert] = useState(false);
  const [alertTitle, setAlertTitle] = useState('');
  const [alertMessage, setAlertMessage] = useState('');
  const { handleApiError } = useApiError();

  useEffect(() => {
    const fetchCourseGroups = async () => {
      try {
        const response = await courseGroupService.getCourseGroups();
        setCourseGroups(response);
        if (response.length > 0) {
          setCourseGroupId(response[0].id);
        }
      } catch (error) {
        console.error("Failed to fetch course groups:", error);
        handleApiError(error, "خطا در بارگیری گروه‌های درسی");
      }
    };
    fetchCourseGroups();
  }, []);

  const validateForm = () => {
    const newErrors: {[key: string]: string} = {};

    if (!name.trim()) {
      newErrors.name = 'نام درس الزامی است';
    }
    if (!description.trim()) {
      newErrors.description = 'توضیحات درس الزامی است';
    }
    if (!coefficient.trim()) {
      newErrors.coefficient = 'ضریب الزامی است';
    } else if (isNaN(parseFloat(coefficient))) {
      newErrors.coefficient = 'ضریب باید عدد باشد';
    }
    if (!negativeMarkingFactor.trim()) {
      newErrors.negativeMarkingFactor = 'ضریب نمره منفی الزامی است';
    } else if (isNaN(parseFloat(negativeMarkingFactor))) {
      newErrors.negativeMarkingFactor = 'ضریب نمره منفی باید عدد باشد';
    }
    if (!courseGroupId) {
      newErrors.courseGroupId = 'گروه درسی الزامی است';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleAddCourse = async () => {
    if (!validateForm()) {
      return;
    }

    const parsedCoefficient = parseFloat(coefficient);
    const parsedNegativeMarkingFactor = parseFloat(negativeMarkingFactor);
    const parsedCourseGroupId = parseInt(courseGroupId!, 10);

    setLoading(true);
    try {
      await courseService.createCourse({
        name,
        description,
        coefficient: parsedCoefficient,
        negative_marking_factor: parsedNegativeMarkingFactor,
        course_group_id: parsedCourseGroupId,
      });
      setAlertTitle('موفقیت');
      setAlertMessage('درس با موفقیت اضافه شد!');
      setShowAlert(true);
      // Navigate back after showing success message
      setTimeout(() => {
        setShowAlert(false);
        router.back();
      }, 2000);
    } catch (error) {
      handleApiError(error, 'خطا در افزودن درس');
      console.error("Failed to add course:", error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      {/* App Bar */}
      <PaperFormAppBar
        title={'افزودن درس جدید'}
        onSavePress={handleAddCourse}
        saveDisabled={loading}
      />

      {/* Content */}
      <Surface style={styles.content}>
        <KeyboardAvoidingWrapper>
          <PaperFormSection>
            <PaperFormField
              label={'نام درس'}
              error={errors.name}
              required
            >
              <PaperTextInput
                value={name}
                onChangeText={setName}
                placeholder="نام درس را وارد کنید"
                disabled={loading}
                error={!!errors.name}
              />
            </PaperFormField>

            <PaperFormField
              label={'توضیحات درس'}
              error={errors.description}
              required
            >
              <PaperTextInput
                value={description}
                onChangeText={setDescription}
                placeholder="توضیحات درس را وارد کنید"
                disabled={loading}
                multiline
                numberOfLines={3}
                error={!!errors.description}
              />
            </PaperFormField>

            <PaperFormField
              label={'ضریب'}
              error={errors.coefficient}
              required
            >
              <PaperTextInput
                value={coefficient}
                onChangeText={setCoefficient}
                placeholder="ضریب درس"
                disabled={loading}
                keyboardType="numeric"
                error={!!errors.coefficient}
              />
            </PaperFormField>

            <PaperFormField
              label={'ضریب نمره منفی'}
              error={errors.negativeMarkingFactor}
              required
            >
              <PaperTextInput
                value={negativeMarkingFactor}
                onChangeText={setNegativeMarkingFactor}
                placeholder="ضریب نمره منفی"
                disabled={loading}
                keyboardType="numeric"
                error={!!errors.negativeMarkingFactor}
              />
            </PaperFormField>

            <PaperFormField
              label={'گروه درسی'}
              error={errors.courseGroupId}
              required
            >
              <PaperTextInput
                value={courseGroups.find(g => g.id === courseGroupId)?.name || ''}
                onChangeText={() => {}} // Dummy function since it's disabled
                placeholder="گروه درسی را انتخاب کنید"
                disabled={true}
                error={!!errors.courseGroupId}
              />
            </PaperFormField>

            <PaperButton
              title={loading ? 'در حال افزودن...' : 'افزودن درس'}
              onPress={handleAddCourse}
              disabled={loading}
              loading={loading}
              mode="contained"
              style={styles.submitButton}
              icon="plus"
            />
          </PaperFormSection>
        </KeyboardAvoidingWrapper>
      </Surface>

      {/* Alert Dialog */}
      <PaperAlertDialog
        visible={showAlert}
        onDismiss={() => setShowAlert(false)}
        title={alertTitle}
        message={alertMessage}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  submitButton: {
    marginTop: 24,
  },
});
