import React, { useEffect, useState } from 'react';
import { ActivityIndicator, StyleSheet, View, I18nManager } from 'react-native';
import { ThemedText } from '@/components/ThemedText';
import { Chapter, chapterService } from '@/services/chapterService';
import { Course, courseService } from '@/services/courseService';
import { Question } from '@/services/questionService';

// Enable RTL layout
I18nManager.allowRTL(true);
I18nManager.forceRTL(true);

interface QuestionDetailsProps {
  question: Question;
}

export default function QuestionDetails({ question }: QuestionDetailsProps) {
  const [chapter, setChapter] = useState<Chapter | null>(null);
  const [course, setCourse] = useState<Course | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchDetails = async () => {
      try {
        const chapterData = await chapterService.getChapter(question.chapter_id);
        setChapter(chapterData);
        if (chapterData) {
          const courseData = await courseService.getCourse(chapterData.course_id.toString());
          setCourse(courseData);
        }
      } catch (err) {
        setError('خطا در دریافت جزئیات سوال.');
        console.error(err);
      } finally {
        setLoading(false);
      }
    };
    fetchDetails();
  }, [question]);

  if (loading) {
    return (
      <View style={styles.container}>
        <ActivityIndicator size="small" />
      </View>
    );
  }

  if (error) {
    return (
      <View style={styles.container}>
        <ThemedText style={styles.errorText}>{error}</ThemedText>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <ThemedText style={styles.questionText}>{question.question_text}</ThemedText>
      {question.explanation && (
        <ThemedText style={styles.explanationText}>{question.explanation}</ThemedText>
      )}
      {course && (
        <ThemedText style={styles.courseChapterText}>درس: {course.name}</ThemedText>
      )}
      {chapter && (
        <ThemedText style={styles.courseChapterText}>فصل: {chapter.name}</ThemedText>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    marginBottom: 10,
    padding: 10,
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 5,
    direction: 'rtl',
    alignItems: 'flex-end',
  },
  questionText: {
    fontWeight: 'bold',
    textAlign: 'right',
    writingDirection: 'rtl',
    marginBottom: 8,
    fontSize: 16,
  },
  explanationText: {
    marginTop: 5,
    marginBottom: 8,
    textAlign: 'right',
    writingDirection: 'rtl',
    fontSize: 14,
    lineHeight: 20,
  },
  courseChapterText: {
    fontSize: 12,
    textAlign: 'right',
    writingDirection: 'rtl',
    marginTop: 4,
    fontStyle: 'italic',
    opacity: 0.8,
  },
  errorText: {
    color: 'red',
    fontSize: 16,
    textAlign: 'center',
  },
});
