# React Native Paper Integration Guide

This guide explains how React Native Paper has been integrated into the Azmoon Client project with RTL support and Persian localization.

## Installation

To install React Native Paper and its dependencies, run:

```bash
npm install react-native-paper react-native-vector-icons
```

## Setup

### 1. Theme Configuration

The app uses custom themes with RTL support and Persian fonts. See `constants/PaperTheme.ts`:

- **Light Theme**: Custom colors with Persian font (Vazirmatn)
- **Dark Theme**: Dark mode compatible colors
- **RTL Support**: Built-in right-to-left layout support
- **Persian Fonts**: Vazirmatn font family for all text

### 2. Provider Setup

The app is wrapped with `PaperProvider` in `app/_layout.tsx`:

```tsx
import { PaperProvider } from 'react-native-paper';
import { lightTheme, darkTheme } from '@/constants/PaperTheme';

// In your component
const paperTheme = appColorScheme === "dark" ? darkTheme : lightTheme;

return (
  <PaperProvider theme={paperTheme}>
    {/* Your app content */}
  </PaperProvider>
);
```

### 3. Localization

Persian/Farsi localization is available in `constants/Localization.ts`:

```tsx
import { t } from '@/constants/Localization';

// Usage
<Text>{t('save')}</Text> // Returns: 'ذخیره'
```

## Available Components

### Core Components

All components are available from `@/components/paper`:

```tsx
import {
  PaperButton,
  PaperCard,
  PaperText,
  PaperTextInput,
  // ... other components
} from '@/components/paper';
```

### 1. Button Components

```tsx
// Basic button
<PaperButton
  title={t('save')}
  onPress={handleSave}
  mode="contained"
  icon="content-save"
/>

// Loading button
<PaperButton
  title={t('loading')}
  loading={true}
  disabled={true}
/>
```

### 2. Card Components

```tsx
// Basic card
<PaperCard onPress={handlePress}>
  <PaperText>{content}</PaperText>
</PaperCard>

// Card with title and actions
<PaperCard>
  <PaperCardTitle
    title="Card Title"
    subtitle="Card Subtitle"
    left={(props) => <Avatar.Icon {...props} icon="folder" />}
  />
  <PaperCardActions>
    <PaperButton title={t('edit')} mode="outlined" />
    <PaperButton title={t('delete')} mode="outlined" />
  </PaperCardActions>
</PaperCard>
```

### 3. Text Components

```tsx
// Different text variants
<PaperTitle size="large">Main Title</PaperTitle>
<PaperHeadline size="medium">Section Header</PaperHeadline>
<PaperBody size="medium">Body text content</PaperBody>
<PaperLabel size="small">Label text</PaperLabel>
```

### 4. Input Components

```tsx
// Text input with validation
<PaperTextInput
  label={t('email')}
  value={email}
  onChangeText={setEmail}
  error={emailError}
  errorText={t('invalidEmail')}
  keyboardType="email-address"
/>

// Search input
<PaperSearchInput
  value={searchQuery}
  onChangeText={setSearchQuery}
  placeholder={t('search')}
/>
```

### 5. List Components

```tsx
// List items
<PaperListItem
  title="Item Title"
  description="Item Description"
  left={(props) => <List.Icon {...props} icon="star" />}
  right={(props) => <List.Icon {...props} icon="chevron-right" />}
  onPress={handlePress}
/>

// List sections
<PaperListSection title="Section Title">
  <PaperListItem title="Item 1" />
  <PaperListItem title="Item 2" />
</PaperListSection>
```

### 6. Dialog Components

```tsx
// Confirmation dialog
<PaperConfirmDialog
  visible={showDialog}
  onDismiss={() => setShowDialog(false)}
  onConfirm={handleConfirm}
  title={t('confirm')}
  message="Are you sure?"
/>

// Delete confirmation
<PaperDeleteDialog
  visible={showDeleteDialog}
  onDismiss={() => setShowDeleteDialog(false)}
  onConfirm={handleDelete}
  itemName="Item Name"
/>
```

### 7. FAB Components

```tsx
// Add FAB
<PaperAddFAB
  onPress={handleAdd}
  visible={!loading}
/>

// FAB Group
<PaperFABGroup
  open={fabOpen}
  onStateChange={({ open }) => setFabOpen(open)}
  actions={[
    { icon: 'plus', label: 'Add', onPress: handleAdd },
    { icon: 'pencil', label: 'Edit', onPress: handleEdit },
  ]}
/>
```

## Pull-to-Refresh Implementation

### Refreshable Lists

```tsx
import { PaperRefreshableFlatList, useRefresh } from '@/components/paper';

function MyListScreen() {
  const [data, setData] = useState([]);
  
  const loadData = async () => {
    const newData = await fetchData();
    setData(newData);
  };

  const { refreshing, onRefresh } = useRefresh(loadData);

  return (
    <PaperRefreshableFlatList
      data={data}
      renderItem={renderItem}
      keyExtractor={(item) => item.id}
      refreshing={refreshing}
      onRefresh={onRefresh}
      emptyTitle="No items found"
      emptyDescription="Pull to refresh or add new items"
      emptyIcon="inbox"
    />
  );
}
```

### Pagination Support

```tsx
import { usePagination } from '@/components/paper';

function MyPaginatedList() {
  const fetchPage = async (page: number) => {
    return await api.getData({ page, limit: 20 });
  };

  const {
    data,
    loading,
    loadingMore,
    hasMore,
    refresh,
    loadMore,
  } = usePagination(fetchPage);

  return (
    <PaperRefreshableFlatList
      data={data}
      renderItem={renderItem}
      keyExtractor={(item) => item.id}
      refreshing={loading}
      onRefresh={refresh}
      onEndReached={loadMore}
      loadingMore={loadingMore}
    />
  );
}
```

## Navigation Integration

### Drawer Navigation

The drawer has been updated to use Paper components:

```tsx
// CustomDrawerContent.tsx uses:
- Drawer.Item for navigation items
- Avatar.Icon for user profile
- Text components for labels
- PaperConfirmDialog for logout confirmation
```

### App Bar

```tsx
<Appbar.Header>
  <Appbar.BackAction onPress={handleBack} />
  <Appbar.Content title="Screen Title" />
  <Appbar.Action icon="plus" onPress={handleAdd} />
</Appbar.Header>
```

## Best Practices

### 1. RTL Support

All Paper components automatically support RTL layout. For custom components:

```tsx
import { rtlStyle } from '@/utils/rtl';

const styles = StyleSheet.create({
  container: {
    flexDirection: rtlStyle.flexDirection.row,
    textAlign: rtlStyle.textAlign.start,
  },
});
```

### 2. Theming

Use theme colors consistently:

```tsx
import { useTheme } from 'react-native-paper';

function MyComponent() {
  const theme = useTheme();
  
  return (
    <View style={{ backgroundColor: theme.colors.surface }}>
      <Text style={{ color: theme.colors.onSurface }}>
        Themed text
      </Text>
    </View>
  );
}
```

### 3. Accessibility

Paper components include built-in accessibility features:

```tsx
<PaperButton
  title="Save"
  accessibilityLabel="Save document"
  accessibilityHint="Saves the current document"
/>
```

### 4. Performance

Use React.memo for list items:

```tsx
const MemoizedListItem = React.memo(({ item }) => (
  <PaperListItem
    title={item.title}
    onPress={() => handlePress(item)}
  />
));
```

## Migration from Existing Components

### Before (Custom Components)
```tsx
<TouchableOpacity style={styles.button} onPress={onPress}>
  <Text style={styles.buttonText}>{title}</Text>
</TouchableOpacity>
```

### After (Paper Components)
```tsx
<PaperButton
  title={title}
  onPress={onPress}
  mode="contained"
/>
```

## Example Screen Implementation

See `app/screens/MajorsListPaper.tsx` for a complete example of:
- App bar with navigation
- Refreshable list with pull-to-refresh
- Card-based list items
- FAB for adding items
- Delete confirmation dialogs
- Empty state handling
- Loading states

## Troubleshooting

### Common Issues

1. **Icons not showing**: Ensure react-native-vector-icons is properly installed
2. **RTL not working**: Check I18nManager.isRTL is set correctly
3. **Theme not applied**: Verify PaperProvider wraps your app
4. **Fonts not loading**: Ensure Vazirmatn font is loaded in app/_layout.tsx

### Performance Tips

1. Use FlatList for large datasets
2. Implement proper key extractors
3. Use React.memo for complex list items
4. Optimize image loading in cards
5. Use pagination for large datasets

## Resources

- [React Native Paper Documentation](https://reactnativepaper.com/)
- [Material Design 3](https://m3.material.io/)
- [RTL Support Guide](https://reactnative.dev/docs/rtl-support)
