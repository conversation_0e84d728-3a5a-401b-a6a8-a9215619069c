import React, { useEffect, useState } from 'react';
import { StyleSheet, SafeAreaView } from 'react-native';
import { Surface, ActivityIndicator } from 'react-native-paper';
import { Chapter, chapterService } from '@/services/chapterService';
import { useLocalSearchParams, router } from 'expo-router';
import {
  PaperDetailsAppBar,
  PaperCard,
  PaperCardTitle,
  PaperCardActions,
  PaperButton,
  PaperTitle,
  PaperBody,
  PaperLabel,
} from '@/components/paper';
import { t } from '@/constants/Localization';
import { useApiError } from '@/hooks/useApiError';

export default function ChapterDetailsScreen() {
  const { id } = useLocalSearchParams();
  const [chapter, setChapter] = useState<Chapter | null>(null);
  const [loading, setLoading] = useState(true);
  const { handleApiError } = useApiError();

  useEffect(() => {
    if (id) {
      const fetchChapter = async () => {
        try {
          const data = await chapterService.getChapter(id as string);
          setChapter(data);
        } catch (err) {
          handleApiError(err, 'خطا در دریافت جزئیات فصل');
          console.error(err);
        } finally {
          setLoading(false);
        }
      };
      fetchChapter();
    } else {
      handleApiError(new Error('Chapter ID not provided'), 'شناسه فصل ارائه نشده');
      setLoading(false);
    }
  }, [id]);

  // Navigation handlers
  const handleEditChapter = () => {
    router.push(`/screens/ChapterEdit?id=${id}`);
  };

  const handleViewQuestions = () => {
    router.push(`/screens/QuestionsList?chapterId=${id}`);
  };

  const handleBack = () => {
    router.back();
  };

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <PaperDetailsAppBar
          title={t('chapterDetails') || 'جزئیات فصل'}
          onBackPress={handleBack}
        />
        <Surface style={styles.centered}>
          <ActivityIndicator size="large" />
          <PaperBody style={styles.loadingText}>در حال بارگذاری جزئیات فصل...</PaperBody>
        </Surface>
      </SafeAreaView>
    );
  }

  if (!chapter) {
    return (
      <SafeAreaView style={styles.container}>
        <PaperDetailsAppBar
          title={t('chapterDetails') || 'جزئیات فصل'}
          onBackPress={handleBack}
        />
        <Surface style={styles.centered}>
          <PaperBody>فصل یافت نشد</PaperBody>
        </Surface>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      {/* App Bar */}
      <PaperDetailsAppBar
        title={t('chapterDetails') || 'جزئیات فصل'}
        onBackPress={handleBack}
        onEditPress={handleEditChapter}
      />

      {/* Content */}
      <Surface style={styles.content}>
        <PaperCard style={styles.detailCard}>
          <PaperCardTitle
            title={chapter.name}
            subtitle={chapter.description || 'بدون توضیحات'}
            left={(props) => (
              <Surface {...props} style={styles.chapterIcon}>
                <PaperTitle size="small">{chapter.name.charAt(0)}</PaperTitle>
              </Surface>
            )}
          />

          <Surface style={styles.cardContent}>
            <PaperLabel style={styles.sectionTitle}>اطلاعات فصل</PaperLabel>

            <Surface style={styles.detailRow}>
              <PaperLabel style={styles.label}>شناسه:</PaperLabel>
              <PaperBody>{chapter.id}</PaperBody>
            </Surface>

            <Surface style={styles.detailRow}>
              <PaperLabel style={styles.label}>نام فصل:</PaperLabel>
              <PaperBody>{chapter.name}</PaperBody>
            </Surface>

            {chapter.course_id && (
              <Surface style={styles.detailRow}>
                <PaperLabel style={styles.label}>شناسه درس:</PaperLabel>
                <PaperBody>{chapter.course_id}</PaperBody>
              </Surface>
            )}

            {chapter.order_in_course && (
              <Surface style={styles.detailRow}>
                <PaperLabel style={styles.label}>ترتیب در درس:</PaperLabel>
                <PaperBody>{chapter.order_in_course}</PaperBody>
              </Surface>
            )}
          </Surface>

          <PaperCardActions>
            <PaperButton
              title={t('edit') || 'ویرایش'}
              mode="outlined"
              onPress={handleEditChapter}
              icon="pencil"
            />
            <PaperButton
              title={t('questions') || 'سوال‌ها'}
              mode="contained"
              onPress={handleViewQuestions}
              icon="help-circle"
            />
          </PaperCardActions>
        </PaperCard>
      </Surface>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  centered: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 16,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  detailCard: {
    marginBottom: 16,
  },
  cardContent: {
    padding: 16,
  },
  sectionTitle: {
    marginBottom: 16,
    fontWeight: 'bold',
  },
  detailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
    paddingVertical: 4,
  },
  label: {
    fontWeight: 'bold',
    flex: 1,
  },
  chapterIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#e8f5e8',
  },
  loadingText: {
    marginTop: 16,
  },
});
