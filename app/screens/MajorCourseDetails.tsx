import React, { useEffect, useState } from 'react';
import { ActivityIndicator, StyleSheet } from 'react-native';
import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { MajorCourse, majorCourseService } from '@/services/majorCourseService';
import { useLocalSearchParams } from 'expo-router';

export default function MajorCourseDetailsScreen() {
  const { majorId, courseId } = useLocalSearchParams();
  const [majorCourse, setMajorCourse] = useState<MajorCourse | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (majorId && courseId) {
      const fetchMajorCourse = async () => {
        try {
          const data = await majorCourseService.getMajorCourse(majorId as string, courseId as string);
          setMajorCourse(data);
        } catch (err) {
          setError('Failed to fetch major-course details.');
          console.error(err);
        } finally {
          setLoading(false);
        }
      };
      fetchMajorCourse();
    } else {
      setError('Major ID or Course ID not provided.');
      setLoading(false);
    }
  }, [majorId, courseId]);

  if (loading) {
    return (
      <ThemedView style={styles.centered}>
        <ActivityIndicator size="large" />
        <ThemedText>Loading major-course details...</ThemedText>
      </ThemedView>
    );
  }

  if (error) {
    return (
      <ThemedView style={styles.centered}>
        <ThemedText style={styles.errorText}>{error}</ThemedText>
      </ThemedView>
    );
  }

  if (!majorCourse) {
    return (
      <ThemedView style={styles.centered}>
        <ThemedText>Major-Course relation not found.</ThemedText>
      </ThemedView>
    );
  }

  return (
    <ThemedView style={styles.container}>
      <ThemedText style={styles.title}>Major-Course Details</ThemedText>
      <ThemedView style={styles.detailItem}>
        <ThemedText style={styles.label}>Major ID:</ThemedText>
        <ThemedText>{majorCourse.majorId}</ThemedText>
      </ThemedView>
      <ThemedView style={styles.detailItem}>
        <ThemedText style={styles.label}>Course ID:</ThemedText>
        <ThemedText>{majorCourse.courseId}</ThemedText>
      </ThemedView>
      {majorCourse.createdAt && (
        <ThemedView style={styles.detailItem}>
          <ThemedText style={styles.label}>Created At:</ThemedText>
          <ThemedText>{new Date(majorCourse.createdAt).toLocaleString()}</ThemedText>
        </ThemedView>
      )}
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  centered: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 20,
  },
  detailItem: {
    flexDirection: 'row',
    marginBottom: 10,
  },
  label: {
    fontWeight: 'bold',
    marginRight: 5,
  },
  errorText: {
    color: 'red',
    fontSize: 16,
  },
});
