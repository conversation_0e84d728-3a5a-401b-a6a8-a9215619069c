import React from 'react';
import { render, fireEvent, waitFor } from '@testing-library/react-native';
import MajorCoursesListScreen from '../../app/screens/MajorCoursesList';
import OptionsListScreen from '../../app/screens/OptionsList';
import SessionsListScreen from '../../app/screens/SessionsList';
import { majorCourseService } from '../../services/majorCourseService';
import { optionService } from '../../services/optionService';
import { sessionService } from '../../services/sessionService';

// Mock services
jest.mock('../../services/majorCourseService', () => ({
  majorCourseService: {
    getMajorCourses: jest.fn(),
  },
}));

jest.mock('../../services/optionService', () => ({
  optionService: {
    getOptions: jest.fn(),
  },
}));

jest.mock('../../services/sessionService', () => ({
  sessionService: {
    getSessions: jest.fn(),
  },
}));

describe('Pull-to-Refresh Comprehensive Tests', () => {
  describe('MajorCoursesList Screen', () => {
    const mockMajorCourses = [
      { majorId: '1', courseId: '1' },
      { majorId: '1', courseId: '2' },
    ];

    beforeEach(() => {
      jest.clearAllMocks();
      (majorCourseService.getMajorCourses as jest.Mock).mockResolvedValue(mockMajorCourses);
    });

    it('renders correctly with major courses', async () => {
      const { getByText } = render(<MajorCoursesListScreen />);
      
      await waitFor(() => {
        expect(getByText('Major Courses')).toBeTruthy();
      });
    });

    it('handles pull-to-refresh functionality', async () => {
      const { getByTestId } = render(<MajorCoursesListScreen />);
      
      await waitFor(() => {
        expect(majorCourseService.getMajorCourses).toHaveBeenCalledTimes(1);
      });

      const flatList = getByTestId('major-courses-flatlist');
      fireEvent(flatList, 'refresh');
      
      await waitFor(() => {
        expect(majorCourseService.getMajorCourses).toHaveBeenCalledTimes(2);
      });
    });

    it('shows loading state initially', () => {
      const { getByText } = render(<MajorCoursesListScreen />);
      expect(getByText('Loading major courses...')).toBeTruthy();
    });

    it('displays error message when fetch fails', async () => {
      (majorCourseService.getMajorCourses as jest.Mock).mockRejectedValue(new Error('Network error'));
      
      const { getByText } = render(<MajorCoursesListScreen />);
      
      await waitFor(() => {
        expect(getByText('Failed to fetch major courses.')).toBeTruthy();
      });
    });

    it('shows no data message when list is empty', async () => {
      (majorCourseService.getMajorCourses as jest.Mock).mockResolvedValue([]);
      
      const { getByText } = render(<MajorCoursesListScreen />);
      
      await waitFor(() => {
        expect(getByText('No major courses found.')).toBeTruthy();
      });
    });
  });

  describe('OptionsList Screen', () => {
    const mockOptions = [
      { id: '1', text: 'Option A', questionId: 'question-1' },
      { id: '2', text: 'Option B', questionId: 'question-1' },
    ];

    beforeEach(() => {
      jest.clearAllMocks();
      (optionService.getOptions as jest.Mock).mockResolvedValue(mockOptions);
    });

    it('renders correctly with options', async () => {
      const { getByText } = render(<OptionsListScreen />);
      
      await waitFor(() => {
        expect(getByText('Options')).toBeTruthy();
      });
    });

    it('handles pull-to-refresh functionality', async () => {
      const { getByTestId } = render(<OptionsListScreen />);
      
      await waitFor(() => {
        expect(optionService.getOptions).toHaveBeenCalledTimes(1);
      });

      const flatList = getByTestId('options-flatlist');
      fireEvent(flatList, 'refresh');
      
      await waitFor(() => {
        expect(optionService.getOptions).toHaveBeenCalledTimes(2);
      });
    });

    it('shows loading state initially', () => {
      const { getByText } = render(<OptionsListScreen />);
      expect(getByText('Loading options...')).toBeTruthy();
    });

    it('displays error message when fetch fails', async () => {
      (optionService.getOptions as jest.Mock).mockRejectedValue(new Error('Network error'));
      
      const { getByText } = render(<OptionsListScreen />);
      
      await waitFor(() => {
        expect(getByText('Failed to fetch options.')).toBeTruthy();
      });
    });

    it('shows no data message when list is empty', async () => {
      (optionService.getOptions as jest.Mock).mockResolvedValue([]);
      
      const { getByText } = render(<OptionsListScreen />);
      
      await waitFor(() => {
        expect(getByText('No options found.')).toBeTruthy();
      });
    });
  });

  describe('SessionsList Screen', () => {
    const mockSessions = [
      { id: '1', name: 'Session 1', userId: 'user-1' },
      { id: '2', name: 'Session 2', userId: 'user-1' },
    ];

    beforeEach(() => {
      jest.clearAllMocks();
      (sessionService.getSessions as jest.Mock).mockResolvedValue(mockSessions);
    });

    it('renders correctly with sessions', async () => {
      const { getByText } = render(<SessionsListScreen />);
      
      await waitFor(() => {
        expect(getByText('Sessions')).toBeTruthy();
      });
    });

    it('handles pull-to-refresh functionality', async () => {
      const { getByTestId } = render(<SessionsListScreen />);
      
      await waitFor(() => {
        expect(sessionService.getSessions).toHaveBeenCalledTimes(1);
      });

      const flatList = getByTestId('sessions-flatlist');
      fireEvent(flatList, 'refresh');
      
      await waitFor(() => {
        expect(sessionService.getSessions).toHaveBeenCalledTimes(2);
      });
    });

    it('shows loading state initially', () => {
      const { getByText } = render(<SessionsListScreen />);
      expect(getByText('Loading sessions...')).toBeTruthy();
    });

    it('displays error message when fetch fails', async () => {
      (sessionService.getSessions as jest.Mock).mockRejectedValue(new Error('Network error'));
      
      const { getByText } = render(<SessionsListScreen />);
      
      await waitFor(() => {
        expect(getByText('Failed to fetch sessions.')).toBeTruthy();
      });
    });

    it('shows no data message when list is empty', async () => {
      (sessionService.getSessions as jest.Mock).mockResolvedValue([]);
      
      const { getByText } = render(<SessionsListScreen />);
      
      await waitFor(() => {
        expect(getByText('No sessions found.')).toBeTruthy();
      });
    });
  });

  describe('RefreshControl Consistency Tests', () => {
    it('all screens use consistent refresh color', async () => {
      const screens = [
        { component: MajorCoursesListScreen, testId: 'major-courses-flatlist' },
        { component: OptionsListScreen, testId: 'options-flatlist' },
        { component: SessionsListScreen, testId: 'sessions-flatlist' },
      ];

      for (const screen of screens) {
        const { getByTestId } = render(<screen.component />);
        
        await waitFor(() => {
          const flatList = getByTestId(screen.testId);
          expect(flatList.props.refreshControl.props.tintColor).toBe('#007bff');
        });
      }
    });
  });
});
