import React from 'react';
import { render, fireEvent, waitFor } from '@testing-library/react-native';
import QuestionsListScreen from '../../app/screens/QuestionsList';
import { questionService } from '../../services/questionService';

// Mock the question service
jest.mock('../../services/questionService', () => ({
  questionService: {
    getQuestions: jest.fn(),
  },
}));

const mockQuestions = [
  {
    id: '1',
    text: 'What is React Native?',
    chapterId: 'chapter-1',
    createdAt: '2023-01-01T00:00:00Z',
  },
  {
    id: '2',
    text: 'How to implement pull-to-refresh?',
    chapterId: 'chapter-1',
    createdAt: '2023-01-02T00:00:00Z',
  },
];

describe('QuestionsListScreen', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    (questionService.getQuestions as jest.Mock).mockResolvedValue(mockQuestions);
  });

  it('renders correctly with questions', async () => {
    const { getByText } = render(<QuestionsListScreen />);
    
    await waitFor(() => {
      expect(getByText('Questions')).toBeTruthy();
      expect(getByText('Question ID: 1')).toBeTruthy();
      expect(getByText('Question ID: 2')).toBeTruthy();
    });
  });

  it('shows loading state initially', () => {
    const { getByText } = render(<QuestionsListScreen />);
    expect(getByText('Loading questions...')).toBeTruthy();
  });

  it('handles pull-to-refresh functionality', async () => {
    const { getByTestId } = render(<QuestionsListScreen />);
    
    // Wait for initial load
    await waitFor(() => {
      expect(questionService.getQuestions).toHaveBeenCalledTimes(1);
    });

    // Find the FlatList and trigger refresh
    const flatList = getByTestId('questions-flatlist');
    
    // Simulate pull-to-refresh
    fireEvent(flatList, 'refresh');
    
    await waitFor(() => {
      expect(questionService.getQuestions).toHaveBeenCalledTimes(2);
    });
  });

  it('displays error message when fetch fails', async () => {
    (questionService.getQuestions as jest.Mock).mockRejectedValue(new Error('Network error'));
    
    const { getByText } = render(<QuestionsListScreen />);
    
    await waitFor(() => {
      expect(getByText('Failed to fetch questions.')).toBeTruthy();
    });
  });

  it('shows no data message when questions list is empty', async () => {
    (questionService.getQuestions as jest.Mock).mockResolvedValue([]);
    
    const { getByText } = render(<QuestionsListScreen />);
    
    await waitFor(() => {
      expect(getByText('No questions found.')).toBeTruthy();
    });
  });

  it('handles refresh error gracefully', async () => {
    const { getByTestId } = render(<QuestionsListScreen />);
    
    // Wait for initial load
    await waitFor(() => {
      expect(questionService.getQuestions).toHaveBeenCalledTimes(1);
    });

    // Mock refresh to fail
    (questionService.getQuestions as jest.Mock).mockRejectedValueOnce(new Error('Refresh failed'));

    // Find the FlatList and trigger refresh
    const flatList = getByTestId('questions-flatlist');
    fireEvent(flatList, 'refresh');
    
    await waitFor(() => {
      expect(getByText('Failed to refresh questions.')).toBeTruthy();
    });
  });

  it('calls loadQuestions on component mount', async () => {
    render(<QuestionsListScreen />);
    
    await waitFor(() => {
      expect(questionService.getQuestions).toHaveBeenCalledTimes(1);
    });
  });

  it('renders add new question button', async () => {
    const { getByText } = render(<QuestionsListScreen />);
    
    await waitFor(() => {
      expect(getByText('Add New Question')).toBeTruthy();
    });
  });

  it('renders edit buttons for each question', async () => {
    const { getAllByText } = render(<QuestionsListScreen />);
    
    await waitFor(() => {
      const editButtons = getAllByText('ویرایش');
      expect(editButtons).toHaveLength(2); // One for each question
    });
  });
});
