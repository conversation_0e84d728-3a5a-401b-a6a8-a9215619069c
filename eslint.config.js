// https://docs.expo.dev/guides/using-eslint/
const { defineConfig } = require('eslint/config');
const expoConfig = require('eslint-config-expo/flat');

module.exports = defineConfig([
  expoConfig,
  {
    ignores: ["dist/*"],
  },
  {
    rules: {
      "no-unused-vars": "off", // Disables the rule entirely
      //"no-unused-vars": "warn" // Changes the rule to show warnings instead of errors
    },
  },
]);
