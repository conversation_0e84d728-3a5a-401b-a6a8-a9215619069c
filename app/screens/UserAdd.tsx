import React, { useState } from 'react';
import { StyleSheet, TextInput, Pressable, Alert } from 'react-native';
import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { userService } from '@/services/userService';
import KeyboardAvoidingWrapper from '@/components/KeyboardAvoidingWrapper';
import { router } from 'expo-router';

export default function UserAddScreen() {
  const [email, setEmail] = useState('');
  const [mobile, setMobile] = useState('');
  const [passwordHash, setPasswordHash] = useState('');
  const [firstName, setFirstName] = useState('');
  const [lastName, setLastName] = useState('');
  const [role, setRole] = useState('');
  const [loading, setLoading] = useState(false);

  const handleAddUser = async () => {
    if (!email.trim() || !mobile.trim() || !passwordHash.trim() || !firstName.trim() || !lastName.trim() || !role.trim()) {
      Alert.alert('خطای اعتبارسنجی', 'تمام فیلدها الزامی هستند.');
      return;
    }

    setLoading(true);
    try {
      await userService.createUser({
        email,
        mobile,
        password_hash: passwordHash,
        first_name: firstName,
        last_name: lastName,
        role,
      });
      Alert.alert('موفقیت', 'کاربر با موفقیت اضافه شد!');
      router.back();
    } catch (error) {
      Alert.alert('خطا', 'افزودن کاربر ناموفق بود. لطفاً دوباره تلاش کنید.');
      console.error('Failed to add user:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <KeyboardAvoidingWrapper style={styles.container}>
      <ThemedText style={styles.title}>افزودن کاربر جدید</ThemedText>
      <TextInput
        style={styles.input}
        placeholder="ایمیل"
        value={email}
        onChangeText={setEmail}
        editable={!loading}
        keyboardType="email-address"
        autoCapitalize="none"
      />
      <TextInput
        style={styles.input}
        placeholder="موبایل"
        value={mobile}
        onChangeText={setMobile}
        editable={!loading}
        keyboardType="phone-pad"
      />
      <TextInput
        style={styles.input}
        placeholder="رمز عبور"
        value={passwordHash}
        onChangeText={setPasswordHash}
        editable={!loading}
        secureTextEntry
      />
      <TextInput
        style={styles.input}
        placeholder="نام"
        value={firstName}
        onChangeText={setFirstName}
        editable={!loading}
      />
      <TextInput
        style={styles.input}
        placeholder="نام خانوادگی"
        value={lastName}
        onChangeText={setLastName}
        editable={!loading}
      />
      <TextInput
        style={styles.input}
        placeholder="نقش"
        value={role}
        onChangeText={setRole}
        editable={!loading}
      />
      <Pressable
        style={[styles.button, loading && styles.buttonDisabled]}
        onPress={handleAddUser}
        disabled={loading}
      >
        <ThemedText style={styles.buttonText}>
          {loading ? 'در حال افزودن...' : 'افزودن کاربر'}
        </ThemedText>
      </Pressable>
    </KeyboardAvoidingWrapper>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
    justifyContent: 'center',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 20,
    textAlign: 'center',
  },
  input: {
    height: 40,
    borderColor: '#ccc',
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 10,
    marginBottom: 15,
    backgroundColor: '#fff',
  },
  button: {
    backgroundColor: '#007bff',
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  buttonText: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 16,
  },
  buttonDisabled: {
    backgroundColor: '#a0c9ff',
  },
});
