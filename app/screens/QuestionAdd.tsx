import React, { useState, useEffect } from 'react';
import { StyleSheet, TextInput, Pressable, Alert } from 'react-native';
import { ThemedText } from '@/components/ThemedText';
import { questionService } from '@/services/questionService';
import { chapterService, Chapter } from '@/services/chapterService';
import KeyboardAvoidingWrapper from '@/components/KeyboardAvoidingWrapper';
import { router, useLocalSearchParams } from 'expo-router';

export default function QuestionAddScreen() {
  const { chapter_id, course_id, courseName } = useLocalSearchParams<{ chapter_id: string, course_id: string, courseName: string }>();
  const [questionText, setQuestionText] = useState('');
  const [explanation, setExplanation] = useState('');
  const [difficultyLevel, setDifficultyLevel] = useState('');
  const [chapter, setChapter] = useState<Chapter | null>(null);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (chapter_id) {
      const fetchChapter = async () => {
        try {
          const chapterData = await chapterService.getChapter(chapter_id);
          setChapter(chapterData);
        } catch (error) {
          console.error('Failed to fetch chapter:', error);
        }
      };
      fetchChapter();
    }
  }, [chapter_id]);

  const handleAddQuestion = async () => {
    if (!questionText.trim() || !explanation.trim() || !difficultyLevel.trim()) {
      Alert.alert('خطای اعتبارسنجی', 'تمام فیلدها الزامی هستند.');
      return;
    }

    if (!chapter_id) {
      Alert.alert('خطا', 'شناسه فصل یافت نشد.');
      return;
    }

    setLoading(true);
    try {
      await questionService.createQuestion({
        question_text: questionText,
        explanation,
        difficulty_level: difficultyLevel,
        chapter_id: chapter_id,
      });
      Alert.alert('موفقیت', 'سوال با موفقیت اضافه شد!');
      router.back();
    } catch (error) {
      Alert.alert('خطا', 'افزودن سوال ناموفق بود. لطفاً دوباره تلاش کنید.');
      console.error('Failed to add question:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <KeyboardAvoidingWrapper style={styles.container}>
      <ThemedText style={styles.title}>افزودن سوال جدید</ThemedText>
      {courseName && (
        <ThemedText style={styles.courseInfo}>
          دوره: {courseName} (شناسه: {course_id})
        </ThemedText>
      )}
      {chapter && (
        <ThemedText style={styles.chapterInfo}>
          فصل: {chapter.name} (شناسه: {chapter_id})
        </ThemedText>
      )}
      <TextInput
        style={styles.input}
        placeholder="متن سوال"
        value={questionText}
        onChangeText={setQuestionText}
        editable={!loading}
        multiline
      />
      <TextInput
        style={styles.input}
        placeholder="توضیحات"
        value={explanation}
        onChangeText={setExplanation}
        editable={!loading}
        multiline
      />
      <TextInput
        style={styles.input}
        placeholder="سطح دشواری"
        value={difficultyLevel}
        onChangeText={setDifficultyLevel}
        editable={!loading}
      />
      <Pressable
        style={[styles.button, loading && styles.buttonDisabled]}
        onPress={handleAddQuestion}
        disabled={loading}
      >
        <ThemedText style={styles.buttonText}>
          {loading ? 'در حال افزودن...' : 'افزودن سوال'}
        </ThemedText>
      </Pressable>
    </KeyboardAvoidingWrapper>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
    justifyContent: 'center',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 20,
    textAlign: 'center',
  },
  courseInfo: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 10,
    color: '#666',
  },
  chapterInfo: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 20,
    color: '#666',
  },
  input: {
    height: 40,
    borderColor: '#ccc',
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 10,
    marginBottom: 15,
    backgroundColor: '#fff',
  },
  button: {
    backgroundColor: '#007bff',
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  buttonText: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 16,
  },
  buttonDisabled: {
    backgroundColor: '#a0c9ff',
  },
});
