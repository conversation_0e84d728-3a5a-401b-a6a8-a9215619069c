import React, { useEffect, useState } from 'react';
import { StyleSheet, TextInput, Pressable, Alert, ActivityIndicator } from 'react-native';
import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { MajorCourse, majorCourseService } from '@/services/majorCourseService';
import { useLocalSearchParams, router } from 'expo-router';

export default function MajorCourseEditScreen() {
  const { majorId, courseId } = useLocalSearchParams();
  const [currentMajorId, setCurrentMajorId] = useState('');
  const [currentCourseId, setCurrentCourseId] = useState('');
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (majorId && courseId) {
      const fetchMajorCourse = async () => {
        try {
          const data = await majorCourseService.getMajorCourse(majorId as string, courseId as string);
          setCurrentMajorId(data.majorId);
          setCurrentCourseId(data.courseId);
        } catch (err) {
          setError('Failed to fetch major-course details for editing.');
          console.error(err);
        } finally {
          setLoading(false);
        }
      };
      fetchMajorCourse();
    } else {
      setError('Major ID or Course ID not provided for editing.');
      setLoading(false);
    }
  }, [majorId, courseId]);

  const handleUpdateMajorCourse = async () => {
    if (!currentMajorId.trim() || !currentCourseId.trim()) {
      Alert.alert('Validation Error', 'Major ID and Course ID cannot be empty.');
      return;
    }
    if (!majorId || !courseId) {
      Alert.alert('Error', 'Original Major ID or Course ID is missing.');
      return;
    }

    setSaving(true);
    try {
      // Note: The API for major-courses update might require sending the new majorId and courseId
      // along with the original ones if the IDs themselves are being changed.
      // Assuming for now that the update operation is on the existing relation,
      // and the fields being updated are not the IDs themselves, or the API handles ID changes.
      // If the API expects a full replacement or specific fields for update, this might need adjustment.
      await majorCourseService.updateMajorCourse(majorId as string, courseId as string, {
        majorId: currentMajorId,
        courseId: currentCourseId,
      });
      Alert.alert('Success', 'Major-Course relation updated successfully!');
      router.back();
    } catch (error) {
      Alert.alert('Error', 'Failed to update major-course relation. Please try again.');
      console.error('Failed to update major-course relation:', error);
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <ThemedView style={styles.centered}>
        <ActivityIndicator size="large" />
        <ThemedText>Loading major-course relation for editing...</ThemedText>
      </ThemedView>
    );
  }

  if (error) {
    return (
      <ThemedView style={styles.centered}>
        <ThemedText style={styles.errorText}>{error}</ThemedText>
      </ThemedView>
    );
  }

  return (
    <ThemedView style={styles.container}>
      <ThemedText style={styles.title}>Edit Major-Course Relation</ThemedText>
      <ThemedText style={styles.label}>Original Major ID: {majorId}</ThemedText>
      <ThemedText style={styles.label}>Original Course ID: {courseId}</ThemedText>
      <TextInput
        style={styles.input}
        placeholder="New Major ID"
        value={currentMajorId}
        onChangeText={setCurrentMajorId}
        editable={!saving}
      />
      <TextInput
        style={styles.input}
        placeholder="New Course ID"
        value={currentCourseId}
        onChangeText={setCurrentCourseId}
        editable={!saving}
      />
      <Pressable
        style={[styles.button, saving && styles.buttonDisabled]}
        onPress={handleUpdateMajorCourse}
        disabled={saving}
      >
        <ThemedText style={styles.buttonText}>
          {saving ? 'Saving...' : 'ذخیره تغییرات'}
        </ThemedText>
      </Pressable>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
    justifyContent: 'center',
  },
  centered: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 20,
    textAlign: 'center',
  },
  input: {
    height: 40,
    borderColor: '#ccc',
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 10,
    marginBottom: 15,
    backgroundColor: '#fff',
  },
  button: {
    backgroundColor: '#007bff',
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  buttonText: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 16,
  },
  buttonDisabled: {
    backgroundColor: '#a0c9ff',
  },
  errorText: {
    color: 'red',
    fontSize: 16,
  },
  label: {
    fontSize: 16,
    marginBottom: 5,
  },
});
