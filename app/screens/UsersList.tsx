import React, { useEffect, useState } from 'react';
import { StyleSheet, SafeAreaView } from 'react-native';
import { Surface } from 'react-native-paper';
import { User, userService } from '@/services/userService';
import { router } from 'expo-router';
import {
  PaperListAppBar,
  PaperRefreshableFlatList,
  PaperCard,
  PaperCardTitle,
  PaperCardActions,
  PaperButton,
  PaperTitle,
  PaperBody,
  PaperAddFAB,
  PaperDeleteDialog,
  useRefresh,
} from '@/components/paper';
import { t } from '@/constants/Localization';
import { useApiError } from '@/hooks/useApiError';

export default function UsersListScreen() {
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [deleteDialogVisible, setDeleteDialogVisible] = useState(false);
  const [userToDelete, setUserToDelete] = useState<User | null>(null);
  const { showError } = useApiError();

  const loadUsers = async () => {
    try {
      setLoading(true);
      const data = await userService.getUsers();
      setUsers(data);
    } catch (err) {
      showError(err, t('errorLoadingUsers') || 'خطا در دریافت کاربران');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  const { refreshing, onRefresh } = useRefresh(loadUsers);

  useEffect(() => {
    loadUsers();
  }, []);

  // Navigation handlers
  const handleAddUser = () => {
    router.push('/screens/UserAdd');
  };

  const handleUserPress = (user: User) => {
    router.push(`/UserDetails?id=${user.id}`);
  };

  const handleEditUser = (user: User) => {
    router.push(`/screens/UserEdit?id=${user.id}`);
  };

  const handleDeleteUser = (user: User) => {
    setUserToDelete(user);
    setDeleteDialogVisible(true);
  };

  const confirmDeleteUser = async () => {
    if (!userToDelete) return;

    try {
      await userService.deleteUser(userToDelete.id);
      setDeleteDialogVisible(false);
      setUserToDelete(null);
      loadUsers(); // Refresh the list
    } catch (error) {
      showError(error, t('errorDeletingUser') || 'خطا در حذف کاربر');
      console.error('Failed to delete user:', error);
    }
  };

  const renderUserItem = ({ item }: { item: User }) => (
    <PaperCard
      style={styles.userCard}
      onPress={() => handleUserPress(item)}
    >
      <PaperCardTitle
        title={`${item.first_name} ${item.last_name}`}
        subtitle={`${t('email')}: ${item.email} | ${t('mobile')}: ${item.mobile} | ${t('role')}: ${item.role}`}
        left={(props) => (
          <Surface {...props} style={styles.userIcon}>
            <PaperTitle size="small">{item.first_name.charAt(0)}</PaperTitle>
          </Surface>
        )}
      />
      <PaperCardActions>
        <PaperButton
          title={t('edit')}
          mode="outlined"
          onPress={() => handleEditUser(item)}
          icon="pencil"
          compact
        />
        <PaperButton
          title={t('delete')}
          mode="outlined"
          onPress={() => handleDeleteUser(item)}
          icon="delete"
          compact
          style={styles.deleteButton}
        />
      </PaperCardActions>
    </PaperCard>
  );

  return (
    <SafeAreaView style={styles.container}>
      {/* App Bar */}
      <PaperListAppBar
        title={t('users') || 'کاربران'}
        onAddPress={handleAddUser}
      />

      {/* Content */}
      <Surface style={styles.content}>
        <PaperRefreshableFlatList
          data={users}
          renderItem={renderUserItem}
          keyExtractor={(item) => item.id}
          refreshing={refreshing}
          onRefresh={onRefresh}
          loading={loading}
          style={styles.list}
          contentContainerStyle={styles.listContent}
          emptyTitle={t('noUsersFound') || 'کاربری یافت نشد'}
          emptyDescription={t('noUsersDescription') || 'برای افزودن کاربر جدید از دکمه + استفاده کنید'}
          emptyIcon="account-group"
          emptyAction={
            <PaperButton
              title={t('addUser') || 'افزودن کاربر'}
              mode="contained"
              onPress={handleAddUser}
              icon="plus"
            />
          }
        />
      </Surface>

      {/* Floating Action Button */}
      <PaperAddFAB
        onPress={handleAddUser}
        visible={!loading}
      />

      {/* Delete Confirmation Dialog */}
      <PaperDeleteDialog
        visible={deleteDialogVisible}
        onDismiss={() => setDeleteDialogVisible(false)}
        onConfirm={confirmDeleteUser}
        title={t('deleteUser') || 'حذف کاربر'}
        content={
          userToDelete
            ? `${t('confirmDeleteUser') || 'آیا از حذف کاربر'} "${userToDelete.first_name} ${userToDelete.last_name}" ${t('areYouSure') || 'مطمئن هستید؟'}`
            : ''
        }
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
  },
  list: {
    flex: 1,
  },
  listContent: {
    padding: 16,
  },
  userCard: {
    marginVertical: 8,
  },
  userIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#e3f2fd',
  },
  deleteButton: {
    borderColor: '#f44336',
  },
});
