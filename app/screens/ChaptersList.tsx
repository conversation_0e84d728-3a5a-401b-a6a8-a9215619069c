import React, { useEffect, useState } from "react";
import { StyleSheet, SafeAreaView } from "react-native";
import { Surface } from "react-native-paper";
import { Chapter, chapterService } from "@/services/chapterService";
import { Course, courseService } from "@/services/courseService";
import { useLocalSearchParams, router } from "expo-router";
import {
  PaperListAppBar,
  PaperRefreshableFlatList,
  PaperCard,
  PaperCardTitle,
  PaperCardActions,
  PaperButton,
  PaperTitle,
  PaperBody,
  PaperAddFAB,
  PaperDeleteDialog,
  useRefresh,
} from "@/components/paper";
import { t } from "@/constants/Localization";
import { useApiError } from "@/hooks/useApiError";

export default function ChaptersListScreen() {
  const { courseId } = useLocalSearchParams<{ courseId: string }>();
  const [chapters, setChapters] = useState<Chapter[]>([]);
  const [course, setCourse] = useState<Course | null>(null);
  const [loading, setLoading] = useState(true);
  const [deleteDialogVisible, setDeleteDialogVisible] = useState(false);
  const [chapterToDelete, setChapterToDelete] = useState<Chapter | null>(null);
  const { showError } = useApiError();

  const loadChapters = async () => {
    if (!courseId) {
      showError(new Error("Course ID is missing"), t('courseIdMissing') || "Course ID is missing");
      setLoading(false);
      return;
    }

    try {
      setLoading(true);

      // Fetch course details
      const courseData = await courseService.getCourse(courseId);
      setCourse(courseData);

      // Fetch chapters and filter them
      const chaptersData = await chapterService.getChapters();
      const numericCourseId = parseInt(courseId, 10);
      const filteredChapters = chaptersData.filter(
        (chapter) => chapter.course_id === numericCourseId
      );
      setChapters(filteredChapters);
    } catch (err) {
      showError(err, t('errorLoadingChapters') || "Failed to fetch chapters");
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  const { refreshing, onRefresh } = useRefresh(loadChapters);

  useEffect(() => {
    loadChapters();
  }, [courseId]); // Re-fetch when courseId changes

  // Navigation handlers
  const handleAddChapter = () => {
    router.push({
      pathname: "/screens/ChapterAdd",
      params: { course_id: courseId, courseName: course?.name },
    });
  };

  const handleChapterPress = (chapter: Chapter) => {
    router.push(`/screens/ChapterDetails?id=${chapter.id}`);
  };

  const handleEditChapter = (chapter: Chapter) => {
    router.push(`/screens/ChapterEdit?id=${chapter.id}`);
  };

  const handleViewQuestions = (chapter: Chapter) => {
    router.push({
      pathname: "/screens/QuestionsList",
      params: {
        chapterId: chapter.id,
        courseId: courseId,
        courseName: course?.name
      },
    });
  };

  const handleDeleteChapter = (chapter: Chapter) => {
    setChapterToDelete(chapter);
    setDeleteDialogVisible(true);
  };

  const confirmDeleteChapter = async () => {
    if (!chapterToDelete) return;

    try {
      await chapterService.deleteChapter(chapterToDelete.id);
      setDeleteDialogVisible(false);
      setChapterToDelete(null);
      loadChapters(); // Refresh the list
    } catch (error) {
      showError(error, t('errorDeletingChapter') || 'خطا در حذف فصل');
      console.error('Failed to delete chapter:', error);
    }
  };

  const renderChapterItem = ({ item }: { item: Chapter }) => (
    <PaperCard
      style={styles.chapterCard}
      onPress={() => handleChapterPress(item)}
    >
      <PaperCardTitle
        title={item.name}
        subtitle={item.course_id ? `${t('courseId')}: ${item.course_id}` : ''}
        left={(props) => (
          <Surface {...props} style={styles.chapterIcon}>
            <PaperTitle size="small">{item.name.charAt(0)}</PaperTitle>
          </Surface>
        )}
      />
      <PaperCardActions>
        <PaperButton
          title={t('edit')}
          mode="outlined"
          onPress={() => handleEditChapter(item)}
          icon="pencil"
          compact
        />
        <PaperButton
          title={t('questions') || 'سوال‌ها'}
          mode="outlined"
          onPress={() => handleViewQuestions(item)}
          icon="help-circle"
          compact
        />
        <PaperButton
          title={t('delete')}
          mode="outlined"
          onPress={() => handleDeleteChapter(item)}
          icon="delete"
          compact
          style={styles.deleteButton}
        />
      </PaperCardActions>
    </PaperCard>
  );

  return (
    <SafeAreaView style={styles.container}>
      {/* App Bar */}
      <PaperListAppBar
        title={course ? `${t('chaptersFor')} ${course.name}` : t('chapters') || 'Chapters'}
        onAddPress={handleAddChapter}
        showBackButton
      />

      {/* Content */}
      <Surface style={styles.content}>
        <PaperRefreshableFlatList
          data={chapters}
          renderItem={renderChapterItem}
          keyExtractor={(item) => item.id}
          refreshing={refreshing}
          onRefresh={onRefresh}
          loading={loading}
          style={styles.list}
          contentContainerStyle={styles.listContent}
          emptyTitle={t('noChaptersFound') || 'فصلی یافت نشد'}
          emptyDescription={t('noChaptersDescription') || 'برای افزودن فصل جدید از دکمه + استفاده کنید'}
          emptyIcon="book-open"
          emptyAction={
            <PaperButton
              title={t('addChapter') || 'افزودن فصل'}
              mode="contained"
              onPress={handleAddChapter}
              icon="plus"
            />
          }
        />
      </Surface>

      {/* Floating Action Button */}
      <PaperAddFAB
        onPress={handleAddChapter}
        visible={!loading}
      />

      {/* Delete Confirmation Dialog */}
      <PaperDeleteDialog
        visible={deleteDialogVisible}
        onDismiss={() => setDeleteDialogVisible(false)}
        onConfirm={confirmDeleteChapter}
        title={t('deleteChapter') || 'حذف فصل'}
        content={
          chapterToDelete
            ? `${t('confirmDeleteChapter') || 'آیا از حذف فصل'} "${chapterToDelete.name}" ${t('areYouSure') || 'مطمئن هستید؟'}`
            : ''
        }
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
  },
  list: {
    flex: 1,
  },
  listContent: {
    padding: 16,
  },
  chapterCard: {
    marginVertical: 8,
  },
  chapterIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#e8f5e8',
  },
  deleteButton: {
    borderColor: '#f44336',
  },
});
