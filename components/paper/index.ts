// Export all Paper components for easy importing

// Button components
export { 
  PaperButtonComponent as PaperButton,
  type PaperButtonProps 
} from './PaperButton';

// Card components
export { 
  PaperCardComponent as PaperCard,
  PaperCardTitle,
  PaperCardActions,
  PaperCardCover,
  type PaperCardProps 
} from './PaperCard';

// Text components
export { 
  PaperTextComponent as PaperText,
  PaperTitle,
  PaperHeadline,
  PaperBody,
  PaperLabel,
  type PaperTextProps 
} from './PaperText';

// TextInput components
export { 
  PaperTextInputComponent as PaperTextInput,
  PaperHelperText,
  PaperSearchInput,
  type PaperTextInputProps 
} from './PaperTextInput';

// List components
export { 
  PaperListItem,
  PaperListSection,
  PaperListAccordion,
  PaperScrollableList,
  PaperDividedList,
  PaperEmptyList,
  type PaperListItemProps 
} from './PaperList';

// Dialog components
export { 
  PaperDialogComponent as PaperDialog,
  PaperConfirmDialog,
  PaperDeleteDialog,
  PaperAlertDialog,
  PaperLoadingDialog,
  type PaperDialogProps 
} from './PaperDialog';

// FAB components
export {
  PaperFABComponent as PaperFAB,
  PaperFABGroup,
  PaperExtendedFAB,
  PaperAddFAB,
  PaperEditFAB,
  type PaperFABProps,
  type PaperFABGroupAction
} from './PaperFAB';

// Refreshable List components
export {
  PaperRefreshableScrollView,
  PaperRefreshableFlatList,
  PaperRefreshableSectionList,
  useRefresh,
  usePagination,
  type PaperRefreshableScrollViewProps,
  type PaperRefreshableFlatListProps,
  type PaperRefreshableSectionListProps,
} from './PaperRefreshableList';

// AppBar components
export {
  PaperAppBarComponent as PaperAppBar,
  PaperListAppBar,
  PaperFormAppBar,
  PaperDetailsAppBar,
  type PaperAppBarProps,
} from './PaperAppBar';

// Bottom Navigation components
export {
  PaperBottomNavigationComponent as PaperBottomNavigation,
  PaperRTLBottomNavigation,
  useBottomNavigation,
  commonBottomNavigationRoutes,
  type PaperBottomNavigationProps,
  type PaperBottomNavigationRoute,
} from './PaperBottomNavigation';

// Form Control components
export {
  PaperCheckbox,
  PaperSwitch,
  PaperRadioGroup,
  PaperSegmentedButtons,
  PaperFormField,
  PaperFormSection,
  type PaperCheckboxProps,
  type PaperSwitchProps,
  type PaperRadioGroupProps,
  type PaperSegmentedButtonsProps,
  type PaperFormFieldProps,
  type PaperFormSectionProps,
} from './PaperFormControls';

// Form Validation components
export {
  useFormValidation,
  PaperValidationHelperText,
  PaperFormValidationSummary,
  commonValidationRules,
  type ValidationRule,
  type PaperValidationHelperTextProps,
  type PaperFormValidationSummaryProps,
} from './PaperFormValidation';

// Form Builder components
export {
  PaperFormBuilder,
  type FormFieldType,
  type FormFieldConfig,
  type FormSectionConfig,
  type FormConfig,
  type PaperFormBuilderProps,
} from './PaperFormBuilder';

// Re-export commonly used React Native Paper components with custom styling
export {
  Appbar,
  Avatar,
  Badge,
  Banner,
  BottomNavigation,
  Checkbox,
  Chip,
  DataTable,
  Drawer,
  IconButton,
  Menu,
  Modal,
  ProgressBar,
  RadioButton,
  Searchbar,
  SegmentedButtons,
  Snackbar,
  Surface,
  Switch,
  ToggleButton,
  Tooltip,
  ActivityIndicator,
  Divider,
} from 'react-native-paper';
