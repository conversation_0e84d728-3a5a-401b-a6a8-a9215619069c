import React, { useState } from 'react';
import { StyleSheet, TextInput, Pressable, Alert, Switch } from 'react-native';
import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { optionService } from '@/services/optionService';
import KeyboardAvoidingWrapper from '@/components/KeyboardAvoidingWrapper';
import { router, useLocalSearchParams } from 'expo-router';

export default function OptionAddScreen() {
  const { questionId } = useLocalSearchParams();
  const [text, setText] = useState('');
  const [isCorrect, setIsCorrect] = useState(false);
  const [loading, setLoading] = useState(false);

  const handleAddOption = async () => {
    if (!text.trim()) {
      Alert.alert('خطای اعتبارسنجی', 'متن گزینه نمی‌تواند خالی باشد.');
      return;
    }

    if (!questionId) {
      Alert.alert('خطا', 'شناسه سوال موجود نیست. لطفاً از یک سوال مراجعه کنید.');
      return;
    }

    setLoading(true);
    try {
      // Assuming your CreateOptionDto has 'text', 'questionId', and 'isCorrect' fields
      await optionService.createOption({ text, questionId: questionId as string, isCorrect });
      Alert.alert('موفقیت', 'گزینه با موفقیت اضافه شد!');
      router.back();
    } catch (error) {
      Alert.alert('خطا', 'افزودن گزینه ناموفق بود. لطفاً دوباره تلاش کنید.');
      console.error('Failed to add option:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <KeyboardAvoidingWrapper style={styles.container}>
      <ThemedText style={styles.title}>افزودن گزینه جدید</ThemedText>
      {questionId && (
        <ThemedView style={styles.infoContainer}>
          <ThemedText style={styles.infoText}>
            افزودن گزینه برای سوال شناسه: {questionId}
          </ThemedText>
        </ThemedView>
      )}
      <TextInput
        style={styles.input}
        placeholder="متن گزینه"
        value={text}
        onChangeText={setText}
        editable={!loading}
        multiline
      />
      <ThemedView style={styles.switchContainer}>
        <ThemedText style={styles.label}>پاسخ صحیح:</ThemedText>
        <Switch
          value={isCorrect}
          onValueChange={setIsCorrect}
          disabled={loading}
        />
      </ThemedView>
      <Pressable
        style={[styles.button, loading && styles.buttonDisabled]}
        onPress={handleAddOption}
        disabled={loading}
      >
        <ThemedText style={styles.buttonText}>
          {loading ? 'در حال افزودن...' : 'افزودن گزینه'}
        </ThemedText>
      </Pressable>
    </KeyboardAvoidingWrapper>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
    justifyContent: 'center',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 20,
    textAlign: 'center',
  },
  infoContainer: {
    backgroundColor: '#e3f2fd',
    padding: 12,
    borderRadius: 8,
    marginBottom: 15,
  },
  infoText: {
    fontSize: 14,
    color: '#1976d2',
    textAlign: 'center',
  },
  input: {
    height: 40,
    borderColor: '#ccc',
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 10,
    marginBottom: 15,
    backgroundColor: '#fff',
  },
  switchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 15,
    justifyContent: 'space-between',
  },
  label: {
    fontSize: 16,
  },
  button: {
    backgroundColor: '#007bff',
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  buttonText: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 16,
  },
  buttonDisabled: {
    backgroundColor: '#a0c9ff',
  },
});
