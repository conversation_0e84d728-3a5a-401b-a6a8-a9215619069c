import React, { useEffect, useState } from 'react';
import { StyleSheet, TextInput, Pressable, Alert, ActivityIndicator } from 'react-native';
import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { Major, majorService } from '@/services/majorService';
import KeyboardAvoidingWrapper from '@/components/KeyboardAvoidingWrapper';
import { useLocalSearchParams, router } from 'expo-router';

export default function MajorEditScreen() {
  const { id } = useLocalSearchParams();
  const [name, setName] = useState('');
  const [description, setDescription] = useState('');
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (id) {
      const fetchMajor = async () => {
        try {
          const data = await majorService.getMajor(id as string);
          setName(data.name);
          setDescription(data.description);
        } catch (err) {
          setError('Failed to fetch major details for editing.');
          console.error(err);
        } finally {
          setLoading(false);
        }
      };
      fetchMajor();
    } else {
      setError('Major ID not provided for editing.');
      setLoading(false);
    }
  }, [id]);

  const handleUpdateMajor = async () => {
    if (!name.trim()) {
      Alert.alert('Validation Error', 'Major name cannot be empty.');
      return;
    }
    if (!description.trim()) {
      Alert.alert('Validation Error', 'Major description cannot be empty.');
      return;
    }
    if (!id) {
      Alert.alert('Error', 'Major ID is missing.');
      return;
    }

    setSaving(true);
    try {
      await majorService.updateMajor(id as string, { name, description });
      Alert.alert('Success', 'Major updated successfully!');
      router.back();
    } catch (error) {
      Alert.alert('Error', 'Failed to update major. Please try again.');
      console.error('Failed to update major:', error);
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <ThemedView style={styles.centered}>
        <ActivityIndicator size="large" />
        <ThemedText>Loading major for editing...</ThemedText>
      </ThemedView>
    );
  }

  if (error) {
    return (
      <ThemedView style={styles.centered}>
        <ThemedText style={styles.errorText}>{error}</ThemedText>
      </ThemedView>
    );
  }

  return (
    <KeyboardAvoidingWrapper style={styles.container}>
      <ThemedText style={styles.title}>Edit Major</ThemedText>
      <TextInput
        style={styles.input}
        placeholder="Major Name"
        value={name}
        onChangeText={setName}
        editable={!saving}
      />
      <TextInput
        style={styles.input}
        placeholder="Major Description"
        value={description}
        onChangeText={setDescription}
        editable={!saving}
        multiline
      />
      <Pressable
        style={[styles.button, saving && styles.buttonDisabled]}
        onPress={handleUpdateMajor}
        disabled={saving}
      >
        <ThemedText style={styles.buttonText}>
          {saving ? 'Saving...' : 'ذخیره تغییرات'}
        </ThemedText>
      </Pressable>
    </KeyboardAvoidingWrapper>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
    justifyContent: 'center',
  },
  centered: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 20,
    textAlign: 'center',
  },
  input: {
    height: 40,
    borderColor: '#ccc',
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 10,
    marginBottom: 15,
    backgroundColor: '#fff',
  },
  button: {
    backgroundColor: '#007bff',
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  buttonText: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 16,
  },
  buttonDisabled: {
    backgroundColor: '#a0c9ff',
  },
  errorText: {
    color: 'red',
    fontSize: 16,
  },
});
