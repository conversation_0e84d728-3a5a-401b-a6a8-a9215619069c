import React, { useEffect, useState } from 'react';
import { FlatList, ActivityIndicator, StyleSheet, Pressable, RefreshControl } from 'react-native';
import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { MajorCourse, majorCourseService } from '@/services/majorCourseService';
import { Link } from 'expo-router';

export default function MajorCoursesListScreen() {
  const [majorCourses, setMajorCourses] = useState<MajorCourse[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const loadMajorCourses = async () => {
    try {
      setLoading(true);
      setError(null);
      const data = await majorCourseService.getMajorCourses();
      setMajorCourses(data);
    } catch (err) {
      setError('Failed to fetch major-course relations.');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = async () => {
    try {
      setRefreshing(true);
      setError(null);
      const data = await majorCourseService.getMajorCourses();
      setMajorCourses(data);
    } catch (err) {
      setError('Failed to refresh major-course relations.');
      console.error(err);
    } finally {
      setRefreshing(false);
    }
  };

  useEffect(() => {
    loadMajorCourses();
  }, []);

  if (loading) {
    return (
      <ThemedView style={styles.centered}>
        <ActivityIndicator size="large" />
        <ThemedText>Loading major-course relations...</ThemedText>
      </ThemedView>
    );
  }

  if (error) {
    return (
      <ThemedView style={styles.centered}>
        <ThemedText style={styles.errorText}>{error}</ThemedText>
      </ThemedView>
    );
  }

  return (
    <ThemedView style={styles.container}>
      <ThemedText style={styles.title}>Major-Course Relations</ThemedText>
      <Link href="/screens/MajorCourseAdd" asChild>
        <Pressable style={styles.addButton}>
          <ThemedText style={styles.addButtonText}>Add New Relation</ThemedText>
        </Pressable>
      </Link>
      {majorCourses.length === 0 ? (
        <ThemedText style={styles.noDataText}>No major-course relations found.</ThemedText>
      ) : (
        <FlatList
          testID="major-courses-flatlist"
          data={majorCourses}
          keyExtractor={(item) => `${item.majorId}-${item.courseId}`}
          renderItem={({ item }) => (
            <ThemedView style={styles.majorCourseItem}>
              <Link href={`/screens/MajorCourseDetails?majorId=${item.majorId}&courseId=${item.courseId}`} asChild>
                <Pressable>
                  <ThemedText style={styles.majorCourseName}>Major ID: {item.majorId}, Course ID: {item.courseId}</ThemedText>
                </Pressable>
              </Link>
              <ThemedView style={styles.actions}>
                <Link href={`/screens/MajorCourseEdit?majorId=${item.majorId}&courseId=${item.courseId}`} asChild>
                  <Pressable style={styles.actionButton}>
                    <ThemedText style={styles.actionButtonText}>ویرایش</ThemedText>
                  </Pressable>
                </Link>
                {/* Delete functionality would be implemented here */}
              </ThemedView>
            </ThemedView>
          )}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={handleRefresh}
              colors={['#007bff']}
              tintColor="#007bff"
            />
          }
        />
      )}
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  centered: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  addButton: {
    backgroundColor: '#007bff',
    padding: 10,
    borderRadius: 5,
    alignItems: 'center',
    marginBottom: 16,
  },
  addButtonText: {
    color: '#fff',
    fontWeight: 'bold',
  },
  majorCourseItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 12,
    marginVertical: 8,
    borderWidth: 1,
    borderColor: '#ccc',
    borderRadius: 8,
  },
  majorCourseName: {
    fontSize: 16,
  },
  actions: {
    flexDirection: 'row',
  },
  actionButton: {
    backgroundColor: '#28a745',
    padding: 8,
    borderRadius: 5,
    marginLeft: 8,
  },
  actionButtonText: {
    color: '#fff',
  },
  errorText: {
    color: 'red',
    fontSize: 16,
  },
  noDataText: {
    fontSize: 18,
    textAlign: 'center',
    marginTop: 20,
  },
});
