import React, { useEffect, useState } from "react";
import {
  FlatList,
  ActivityIndicator,
  StyleSheet,
  Pressable,
  Alert,
  RefreshControl,
} from "react-native";
import { ThemedText } from "@/components/ThemedText";
import { ThemedView } from "@/components/ThemedView";
import { Chapter, chapterService } from "@/services/chapterService";
import { Course, courseService } from "@/services/courseService";
import { Link, useLocalSearchParams, router } from "expo-router";

export default function ChaptersListScreen() {
  const { courseId } = useLocalSearchParams<{ courseId: string }>();
  const [chapters, setChapters] = useState<Chapter[]>([]);
  const [course, setCourse] = useState<Course | null>(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const loadChapters = async () => {
    if (!courseId) {
      setError("Course ID is missing.");
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      setError(null);

      // Fetch course details
      const courseData = await courseService.getCourse(courseId);
      setCourse(courseData);

      // Fetch chapters and filter them
      const chaptersData = await chapterService.getChapters();
      const numericCourseId = parseInt(courseId, 10);
      const filteredChapters = chaptersData.filter(
        (chapter) => chapter.course_id === numericCourseId
      );
      setChapters(filteredChapters);
    } catch (err) {
      setError("Failed to fetch data.");
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = async () => {
    if (!courseId) {
      setError("Course ID is missing.");
      setRefreshing(false);
      return;
    }

    try {
      setRefreshing(true);
      setError(null);

      // Re-fetch course details
      const courseData = await courseService.getCourse(courseId);
      setCourse(courseData);

      // Re-fetch chapters and filter them
      const chaptersData = await chapterService.getChapters();
      const numericCourseId = parseInt(courseId, 10);
      const filteredChapters = chaptersData.filter(
        (chapter) => chapter.course_id === numericCourseId
      );
      setChapters(filteredChapters);
    } catch (err) {
      setError("Failed to refresh data.");
      console.error(err);
    } finally {
      setRefreshing(false);
    }
  };

  useEffect(() => {
    loadChapters();
  }, [courseId]); // Re-fetch when courseId changes

  const handleDeleteChapter = async (id: string) => {
    Alert.alert(
      "Delete Chapter",
      "Are you sure you want to delete this chapter?",
      [
        {
          text: "Cancel",
          style: "cancel",
        },
        {
          text: "Delete",
          onPress: async () => {
            try {
              await chapterService.deleteChapter(id);
              Alert.alert("Success", "Chapter deleted successfully!");
              loadChapters(); // Refresh the list
            } catch (error) {
              Alert.alert(
                "Error",
                "Failed to delete chapter. Please try again."
              );
              console.error("Failed to delete chapter:", error);
            }
          },
          style: "destructive",
        },
      ],
      { cancelable: true }
    );
  };

  if (loading) {
    return (
      <ThemedView style={styles.centered}>
        <ActivityIndicator size="large" />
        <ThemedText>Loading chapters...</ThemedText>
      </ThemedView>
    );
  }

  if (error) {
    return (
      <ThemedView style={styles.centered}>
        <ThemedText style={styles.errorText}>{error}</ThemedText>
      </ThemedView>
    );
  }

  return (
    <ThemedView style={styles.container}>
      <ThemedText style={styles.title}>
        {course ? `Chapters for ${course.name}` : "Chapters"}
      </ThemedText>
      <Link
        href={{
          pathname: "/screens/ChapterAdd",
          params: { course_id: courseId, courseName: course?.name },
        }}
        asChild
      >
        <Pressable style={styles.addButton}>
          <ThemedText style={styles.addButtonText}>Add New Chapter</ThemedText>
        </Pressable>
      </Link>
      {chapters.length === 0 ? (
        <ThemedText style={styles.noDataText}>No chapters found.</ThemedText>
      ) : (
        <FlatList
          testID="chapters-flatlist"
          data={chapters}
          keyExtractor={(item) => item.id}
          renderItem={({ item }) => (
            <ThemedView style={styles.chapterItem}>
              <Pressable
                onPress={() =>
                  router.push(`/screens/ChapterDetails?id=${item.id}`)
                }
              >
                <ThemedText style={styles.chapterName}>{item.name}</ThemedText>
                {item.course_id && (
                  <ThemedText>Course ID: {item.course_id}</ThemedText>
                )}
              </Pressable>
              <ThemedView style={styles.actions}>
                <Link href={`/screens/ChapterEdit?id=${item.id}`} asChild>
                  <Pressable style={styles.actionButton}>
                    <ThemedText style={styles.actionButtonText}>
                      ویرایش
                    </ThemedText>
                  </Pressable>
                </Link>
                <Pressable
                  style={[styles.actionButton]}
                  onPress={() =>
                    router.push({
                      pathname: "/screens/QuestionsList",
                      params: { chapterId: item.id, courseId: courseId, courseName: course?.name },
                    })
                  }
                >
                  <ThemedText style={styles.actionButtonText}>
                    سوال‌ها
                  </ThemedText>
                </Pressable>
                <Pressable
                  style={[styles.actionButton, styles.deleteButton]}
                  onPress={() => handleDeleteChapter(item.id)}
                >
                  <ThemedText style={styles.actionButtonText}>
                    Delete
                  </ThemedText>
                </Pressable>
              </ThemedView>
            </ThemedView>
          )}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={handleRefresh}
              colors={["#007bff"]}
              tintColor="#007bff"
            />
          }
        />
      )}
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  centered: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  title: {
    fontSize: 24,
    fontWeight: "bold",
    marginBottom: 16,
  },
  addButton: {
    backgroundColor: "#007bff",
    padding: 10,
    borderRadius: 5,
    alignItems: "center",
    marginBottom: 16,
  },
  addButtonText: {
    color: "#fff",
    fontWeight: "bold",
  },
  chapterItem: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    padding: 12,
    marginVertical: 8,
    borderWidth: 1,
    borderColor: "#ccc",
    borderRadius: 8,
  },
  chapterName: {
    fontSize: 18,
  },
  actions: {
    flexDirection: "row",
  },
  actionButton: {
    backgroundColor: "#28a745",
    padding: 8,
    borderRadius: 5,
    marginLeft: 8,
  },
  actionButtonText: {
    color: "#fff",
  },
  errorText: {
    color: "red",
    fontSize: 16,
  },
  noDataText: {
    fontSize: 18,
    textAlign: "center",
    marginTop: 20,
  },
  deleteButton: {
    backgroundColor: "#dc3545",
  },
});
