import React from 'react';
import { View, StyleSheet } from 'react-native';
import { HelperText, useTheme } from 'react-native-paper';
import { PaperTextComponent } from './PaperText';
import { t } from '@/constants/Localization';

// Validation rules interface
export interface ValidationRule {
  required?: boolean;
  minLength?: number;
  maxLength?: number;
  pattern?: RegExp;
  email?: boolean;
  phone?: boolean;
  numeric?: boolean;
  custom?: (value: string) => string | null;
}

// Form field validation hook
export function useFormValidation<T extends Record<string, any>>(
  initialValues: T,
  validationRules: Partial<Record<keyof T, ValidationRule>>
) {
  const [values, setValues] = React.useState<T>(initialValues);
  const [errors, setErrors] = React.useState<Partial<Record<keyof T, string>>>({});
  const [touched, setTouched] = React.useState<Partial<Record<keyof T, boolean>>>({});

  const validateField = React.useCallback((field: keyof T, value: any): string => {
    const rules = validationRules[field];
    if (!rules) return '';

    // Required validation
    if (rules.required && (!value || value.toString().trim() === '')) {
      return t('required') || 'این فیلد الزامی است';
    }

    // Skip other validations if field is empty and not required
    if (!value || value.toString().trim() === '') {
      return '';
    }

    const stringValue = value.toString();

    // Min length validation
    if (rules.minLength && stringValue.length < rules.minLength) {
      return t('minLength')?.replace('{0}', rules.minLength.toString()) || 
             `حداقل ${rules.minLength} کاراکتر وارد کنید`;
    }

    // Max length validation
    if (rules.maxLength && stringValue.length > rules.maxLength) {
      return t('maxLength')?.replace('{0}', rules.maxLength.toString()) || 
             `حداکثر ${rules.maxLength} کاراکتر مجاز است`;
    }

    // Email validation
    if (rules.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(stringValue)) {
      return t('invalidEmail') || 'ایمیل نامعتبر است';
    }

    // Phone validation (Iranian format)
    if (rules.phone && !/^09\d{9}$/.test(stringValue)) {
      return 'شماره موبایل نامعتبر است (مثال: 09123456789)';
    }

    // Numeric validation
    if (rules.numeric && isNaN(Number(stringValue))) {
      return 'لطفا یک عدد معتبر وارد کنید';
    }

    // Pattern validation
    if (rules.pattern && !rules.pattern.test(stringValue)) {
      return 'فرمت وارد شده نامعتبر است';
    }

    // Custom validation
    if (rules.custom) {
      const customError = rules.custom(stringValue);
      if (customError) return customError;
    }

    return '';
  }, [validationRules]);

  const setValue = React.useCallback((field: keyof T, value: any) => {
    setValues(prev => ({ ...prev, [field]: value }));
    
    // Validate field if it has been touched
    if (touched[field]) {
      const error = validateField(field, value);
      setErrors(prev => ({ ...prev, [field]: error }));
    }
  }, [validateField, touched]);

  const setFieldTouched = React.useCallback((field: keyof T) => {
    setTouched(prev => ({ ...prev, [field]: true }));
    
    // Validate field when touched
    const error = validateField(field, values[field]);
    setErrors(prev => ({ ...prev, [field]: error }));
  }, [validateField, values]);

  const validateForm = React.useCallback((): boolean => {
    const newErrors: Partial<Record<keyof T, string>> = {};
    let isValid = true;

    Object.keys(validationRules).forEach(field => {
      const error = validateField(field as keyof T, values[field]);
      if (error) {
        newErrors[field as keyof T] = error;
        isValid = false;
      }
    });

    setErrors(newErrors);
    setTouched(Object.keys(validationRules).reduce((acc, field) => ({
      ...acc,
      [field]: true
    }), {}));

    return isValid;
  }, [validateField, values, validationRules]);

  const resetForm = React.useCallback(() => {
    setValues(initialValues);
    setErrors({});
    setTouched({});
  }, [initialValues]);

  const isFieldValid = React.useCallback((field: keyof T): boolean => {
    return !errors[field] && touched[field];
  }, [errors, touched]);

  return {
    values,
    errors,
    touched,
    setValue,
    setFieldTouched,
    validateForm,
    resetForm,
    isFieldValid,
    isFormValid: Object.keys(validationRules).every(field => 
      !errors[field as keyof T] && 
      (touched[field as keyof T] || !validationRules[field as keyof T]?.required)
    ),
  };
}

// Validation helper text component
export interface PaperValidationHelperTextProps {
  error?: string;
  visible?: boolean;
  type?: 'error' | 'info';
}

export function PaperValidationHelperText({
  error,
  visible = true,
  type = 'error',
}: PaperValidationHelperTextProps) {
  const theme = useTheme();

  if (!visible || !error) return null;

  return (
    <HelperText 
      type={type}
      visible={visible}
      style={[
        styles.helperText,
        { color: type === 'error' ? theme.colors.error : theme.colors.onSurfaceVariant }
      ]}
    >
      {error}
    </HelperText>
  );
}

// Form validation summary component
export interface PaperFormValidationSummaryProps {
  errors: Record<string, string>;
  visible?: boolean;
}

export function PaperFormValidationSummary({
  errors,
  visible = true,
}: PaperFormValidationSummaryProps) {
  const theme = useTheme();
  const errorList = Object.values(errors).filter(error => error);

  if (!visible || errorList.length === 0) return null;

  return (
    <View style={[styles.validationSummary, { backgroundColor: theme.colors.errorContainer }]}>
      <PaperTextComponent 
        variant="titleSmall" 
        style={[styles.summaryTitle, { color: theme.colors.onErrorContainer }]}
      >
        {t('validationError') || 'خطاهای اعتبارسنجی:'}
      </PaperTextComponent>
      {errorList.map((error, index) => (
        <PaperTextComponent 
          key={index}
          variant="bodySmall" 
          style={[styles.summaryError, { color: theme.colors.onErrorContainer }]}
        >
          • {error}
        </PaperTextComponent>
      ))}
    </View>
  );
}

// Common validation rules
export const commonValidationRules = {
  email: {
    required: true,
    email: true,
  } as ValidationRule,
  
  password: {
    required: true,
    minLength: 8,
  } as ValidationRule,
  
  confirmPassword: (password: string) => ({
    required: true,
    custom: (value: string) => {
      if (value !== password) {
        return t('passwordMismatch') || 'رمزهای عبور مطابقت ندارند';
      }
      return null;
    },
  } as ValidationRule),
  
  phone: {
    required: true,
    phone: true,
  } as ValidationRule,
  
  name: {
    required: true,
    minLength: 2,
    maxLength: 50,
  } as ValidationRule,
  
  description: {
    maxLength: 500,
  } as ValidationRule,
  
  numeric: {
    required: true,
    numeric: true,
  } as ValidationRule,
};

const styles = StyleSheet.create({
  helperText: {
    fontFamily: 'Vazirmatn',
    textAlign: 'right',
    marginTop: 4,
  },
  validationSummary: {
    padding: 16,
    borderRadius: 8,
    marginVertical: 8,
  },
  summaryTitle: {
    fontWeight: '600',
    marginBottom: 8,
  },
  summaryError: {
    marginVertical: 2,
  },
});
