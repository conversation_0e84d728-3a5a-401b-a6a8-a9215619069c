import React, { useEffect, useState, useCallback } from 'react';
import {
  ActivityIndicator,
  StyleSheet,
  RefreshControl,
  Alert,
} from 'react-native';
import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { Session, sessionService } from '@/services/sessionService';
import { SessionResults } from '@/components/SessionResults';
import { useLocalSearchParams } from 'expo-router';
import { rtlStyle } from '@/utils/rtl';

export default function SessionResultsScreen() {
  const { id } = useLocalSearchParams();
  const [sessionResult, setSessionResult] = useState<Session | null>(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchSessionResults = useCallback(async () => {
    if (!id) {
      setError('شناسه جلسه موجود نیست.');
      setLoading(false);
      return;
    }

    try {
      setError(null);
      const session = await sessionService.getSession(id as string);
      // Assuming the Session object returned by getSession will contain the result data
      // if the session is completed and results are available.
      setSessionResult(session);
    } catch (err) {
      setError('خطا در دریافت نتایج جلسه.');
      console.error('Error fetching session results:', err);
      Alert.alert('خطا', 'خطا در دریافت نتایج جلسه. لطفاً دوباره تلاش کنید.');
    } finally {
      setLoading(false);
    }
  }, [id]);

  const onRefresh = useCallback(async () => {
    setRefreshing(true);
    await fetchSessionResults();
    setRefreshing(false);
  }, [fetchSessionResults]);

  useEffect(() => {
    fetchSessionResults();
  }, [fetchSessionResults]);

  if (loading) {
    return (
      <ThemedView style={styles.centerContainer}>
        <ActivityIndicator size="large" color="#4F46E5" />
        <ThemedText style={styles.loadingText}>در حال بارگذاری نتایج...</ThemedText>
      </ThemedView>
    );
  }

  if (error || !sessionResult) {
    return (
      <ThemedView style={styles.centerContainer}>
        <ThemedText style={styles.errorText}>
          {error || 'خطا در دریافت نتایج جلسه.'}
        </ThemedText>
      </ThemedView>
    );
  }

  return (
    <ThemedView style={styles.container}>
      <ThemedView style={styles.header}>
        <ThemedText style={styles.title}>نتایج جلسه</ThemedText>
        <ThemedText style={styles.subtitle}>شناسه جلسه: {id}</ThemedText>
      </ThemedView>
      
      <SessionResults 
        sessionResult={sessionResult}
      />
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  centerContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  header: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
    backgroundColor: '#F9FAFB',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: rtlStyle.textAlign.right,
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 14,
    opacity: 0.7,
    textAlign: rtlStyle.textAlign.right,
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
    textAlign: 'center',
  },
  errorText: {
    fontSize: 16,
    color: '#DC2626',
    textAlign: 'center',
    lineHeight: 24,
  },
});
