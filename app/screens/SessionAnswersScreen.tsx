import React, { useEffect, useState, useCallback } from "react";
import {
  ActivityIndicator,
  StyleSheet,
  ScrollView,
  RefreshControl,
  Text,
} from "react-native";
import { ThemedText } from "@/components/ThemedText";
import { ThemedView } from "@/components/ThemedView";
import { sessionService } from "@/services/sessionService";
import { Answer } from "@/services/answerService";
import { Question, questionService } from "@/services/questionService";
import { Option, optionService } from "@/services/optionService";
import { useLocalSearchParams } from "expo-router";
import { rtlStyle } from "@/utils/rtl";

// Enhanced answer interface with question and option details
interface EnhancedAnswer extends Answer {
  question?: Question;
  selectedOption?: Option;
}

export default function SessionAnswersScreen() {
  const { id } = useLocalSearchParams();
  const [answers, setAnswers] = useState<EnhancedAnswer[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Enhanced data fetching function
  const fetchSessionAnswers = useCallback(async () => {
    if (!id) {
      setError("شناسه جلسه ارائه نشده است.");
      setLoading(false);
      return;
    }

    try {
      setError(null);

      // Fetch session answers
      const answersData = await sessionService.getSessionAnswers(id as string);

      // Enhance answers with question and option details
      const enhancedAnswers: EnhancedAnswer[] = await Promise.all(
        answersData.map(async (answer: Answer) => {
          try {
            const [question, selectedOption] = await Promise.all([
              questionService.getQuestion(answer.question?.id.toString()),
              optionService.getOption(answer.selected_option_id.toString()),
            ]);

            return {
              ...answer,
              question,
              selectedOption,
            };
          } catch (err) {
            console.error(
              `Error fetching details for answer ${answer.id}:`,
              err
            );
            return answer; // Return basic answer if details fetch fails
          }
        })
      );

      setAnswers(enhancedAnswers);
    } catch (err) {
      setError("خطا در دریافت پاسخ‌های جلسه.");
      console.error(err);
    } finally {
      setLoading(false);
    }
  }, [id]);

  const onRefresh = useCallback(async () => {
    setRefreshing(true);
    await fetchSessionAnswers();
    setRefreshing(false);
  }, [fetchSessionAnswers]);

  useEffect(() => {
    fetchSessionAnswers();
  }, [fetchSessionAnswers]);

  if (loading) {
    return (
      <ThemedView style={styles.centered}>
        <ActivityIndicator size="large" color="#4F46E5" />
        <ThemedText style={styles.loadingText}>در حال بارگذاری پاسخ‌ها...</ThemedText>
      </ThemedView>
    );
  }

  if (error) {
    return (
      <ThemedView style={styles.centered}>
        <ThemedText style={styles.errorText}>{error}</ThemedText>
      </ThemedView>
    );
  }

  return (
    <ThemedView style={styles.container}>
      <ThemedView style={styles.header}>
        <ThemedText style={styles.title}>پاسخ‌های ارائه شده</ThemedText>
        <ThemedText style={styles.subtitle}>
          شناسه جلسه: {id} • {answers.length} پاسخ
        </ThemedText>
      </ThemedView>

      <ScrollView
        style={styles.scrollView}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        showsVerticalScrollIndicator={false}
      >
        {answers.length === 0 ? (
          <ThemedView style={styles.centered}>
            <ThemedText style={styles.noDataText}>
              هیچ پاسخی برای این جلسه یافت نشد.
            </ThemedText>
          </ThemedView>
        ) : (
          <ThemedView style={styles.answersContainer}>
            {answers.map((answer: EnhancedAnswer, index: number) => (
              <ThemedView key={answer.id} style={styles.answerItem}>
                <ThemedView style={styles.answerHeader}>
                  <ThemedText style={styles.answerNumber}>
                    سوال {index + 1}
                  </ThemedText>
                  <ThemedText
                    style={[
                      styles.answerStatus,
                      answer.is_correct
                        ? styles.correctAnswer
                        : styles.incorrectAnswer,
                    ]}
                  >
                    {answer.is_correct ? "✓ صحیح" : "✗ نادرست"}
                  </ThemedText>
                </ThemedView>

                {answer.question && (
                  <ThemedView style={styles.questionContainer}>
                    <ThemedText style={styles.questionLabel}>
                      متن سوال:
                    </ThemedText>
                    <Text style={styles.questionText}>
                      {answer.question.question_text}
                    </Text>
                  </ThemedView>
                )}

                {answer.selectedOption && (
                  <ThemedView style={styles.optionContainer}>
                    <ThemedText style={styles.optionLabel}>
                      پاسخ انتخابی:
                    </ThemedText>
                    <Text
                      style={[
                        styles.optionText,
                        answer.is_correct
                          ? styles.correctOptionText
                          : styles.incorrectOptionText,
                      ]}
                    >
                      {answer.selectedOption.option_text}
                    </Text>
                  </ThemedView>
                )}

                <ThemedView style={styles.answerMeta}>
                  <ThemedText style={styles.metaText}>
                    شناسه سوال: {answer.question?.id || 'نامشخص'}
                  </ThemedText>
                  <ThemedText style={styles.metaText}>
                    شناسه گزینه: {answer.selected_option_id}
                  </ThemedText>
                </ThemedView>
              </ThemedView>
            ))}
          </ThemedView>
        )}
      </ScrollView>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#f5f5f5",
  },
  header: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: "#E5E7EB",
    backgroundColor: "#F9FAFB",
  },
  title: {
    fontSize: 24,
    fontWeight: "bold",
    textAlign: rtlStyle.textAlign,
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 14,
    opacity: 0.7,
    textAlign: rtlStyle.textAlign,
  },
  scrollView: {
    flex: 1,
  },
  centered: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: 20,
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
    textAlign: "center",
  },
  errorText: {
    fontSize: 16,
    color: "#DC2626",
    textAlign: "center",
    lineHeight: 24,
  },
  noDataText: {
    fontSize: 16,
    textAlign: "center",
    color: "#6B7280",
    lineHeight: 24,
  },
  answersContainer: {
    padding: 16,
  },
  answerItem: {
    backgroundColor: "#ffffff",
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  answerHeader: {
    flexDirection: rtlStyle.flexDirection.row,
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 12,
    paddingBottom: 8,
    borderBottomWidth: 1,
    borderBottomColor: "#e9ecef",
  },
  answerNumber: {
    fontSize: 16,
    fontWeight: "bold",
    color: "#495057",
  },
  answerStatus: {
    fontSize: 14,
    fontWeight: "bold",
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  correctAnswer: {
    color: "#155724",
    backgroundColor: "#d4edda",
  },
  incorrectAnswer: {
    color: "#721c24",
    backgroundColor: "#f8d7da",
  },
  questionContainer: {
    marginBottom: 12,
  },
  questionLabel: {
    fontSize: 14,
    fontWeight: "bold",
    color: "#6c757d",
    marginBottom: 4,
  },
  questionText: {
    fontSize: 16,
    color: "#2c3e50",
    lineHeight: 24,
    textAlign: rtlStyle.textAlign,
  },
  optionContainer: {
    marginBottom: 12,
  },
  optionLabel: {
    fontSize: 14,
    fontWeight: "bold",
    color: "#6c757d",
    marginBottom: 4,
  },
  optionText: {
    fontSize: 16,
    lineHeight: 22,
    textAlign: rtlStyle.textAlign,
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 6,
    backgroundColor: "#ffffff",
  },
  correctOptionText: {
    borderLeftWidth: 3,
    borderLeftColor: "#28a745",
    backgroundColor: "#d4edda",
  },
  incorrectOptionText: {
    borderLeftWidth: 3,
    borderLeftColor: "#dc3545",
    backgroundColor: "#f8d7da",
  },
  answerMeta: {
    flexDirection: rtlStyle.flexDirection.row,
    justifyContent: "space-between",
    marginTop: 8,
    paddingTop: 8,
    borderTopWidth: 1,
    borderTopColor: "#e9ecef",
  },
  metaText: {
    fontSize: 12,
    color: "#6c757d",
  },
});
