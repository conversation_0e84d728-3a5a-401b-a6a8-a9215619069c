import React, { useEffect, useState } from "react";
import {
  ActivityIndicator,
  FlatList,
  RefreshControl,
  SafeAreaView,
  StatusBar,
  StyleSheet,
  Text,
  TouchableOpacity,
  View
} from "react-native";
import { Path, Svg } from "react-native-svg";
import { examService, Exam } from "../../services/examService";
import { rtlStyle } from "../../utils/rtl";

// --- Icon Components ---

const BackArrowIcon = ({ color = "#1F2937", size = 24 }) => (
  <Svg
    width={size}
    height={size}
    viewBox="0 0 24 24"
    fill="none"
    stroke={color}
    strokeWidth="2"
    strokeLinecap="round"
    strokeLinejoin="round"
  >
    <Path d="M15 18l-6-6 6-6" />
  </Svg>
);

const GraduationCapIcon = ({ color = "#4B5563", size = 32 }) => (
  <Svg
    width={size}
    height={size}
    viewBox="0 0 24 24"
    fill="none"
    stroke={color}
    strokeWidth="1.5"
    strokeLinecap="round"
    strokeLinejoin="round"
  >
    <Path d="M4.26 10.147a60.436 60.436 0 00-.491 6.347A48.627 48.627 0 0112 20.904a48.627 48.627 0 018.232-4.41 60.46 60.46 0 00-.491-6.347m-15.482 0a50.57 50.57 0 00-2.658-.813A59.905 59.905 0 0112 3.493a59.902 59.902 0 0110.399 5.84c-.896.248-1.783.52-2.658.814m-15.482 0l15.482 0" />
  </Svg>
);

const PlusIcon = ({ color = "#2563EB", size = 32 }) => (
  <Svg
    width={size}
    height={size}
    viewBox="0 0 24 24"
    fill="none"
    stroke={color}
    strokeWidth="2.5"
    strokeLinecap="round"
    strokeLinejoin="round"
  >
    <Path d="M12 5v14M5 12h14" />
  </Svg>
);

// --- Main Screen Component ---

const ExamsListScreen = () => {
  const [exams, setExams] = useState<Exam[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadExams();
  }, []);

  const loadExams = async () => {
    try {
      setLoading(true);
      setError(null);
      const data = await examService.getExams();
      setExams(data);
    } catch (err) {
      console.error("Failed to load exams:", err);
      setError((err as Error).message || "Failed to load exams");
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = async () => {
    try {
      setRefreshing(true);
      setError(null);
      const data = await examService.getExams();
      setExams(data);
    } catch (err) {
      console.error("Failed to refresh exams:", err);
      setError((err as Error).message || "Failed to refresh exams");
    } finally {
      setRefreshing(false);
    }
  };

  const handleExamPress = async (examId: string) => {
    const { router } = await import("expo-router");
    router.push(`/screens/ExamDetails?id=${examId}`);
  };

  const renderExamItem = ({ item }: { item: Exam }) => (
    <TouchableOpacity
      style={styles.itemContainer}
      onPress={() => handleExamPress(item.id)}
    >
      <View style={styles.itemIconBackground}>
        <GraduationCapIcon />
      </View>
      <View style={styles.itemTextContainer}>
        <Text style={styles.itemTitle}>{item.name}</Text>
        <Text style={styles.itemSubtitle}>{item.duration_minutes} دقیقه</Text>
        <Text style={styles.itemSubtitle}>{item.instructions}</Text>
      </View>
    </TouchableOpacity>
  );

  const renderContent = () => {
    if (loading) {
      return (
        <View style={styles.centerContainer}>
          <ActivityIndicator size="large" color="#2563EB" />
        </View>
      );
    }

    if (error) {
      return (
        <View style={styles.centerContainer}>
          <Text style={styles.errorText}>{error}</Text>
          <TouchableOpacity style={styles.retryButton} onPress={loadExams}>
            <Text style={styles.retryButtonText}>تلاش مجدد</Text>
          </TouchableOpacity>
        </View>
      );
    }

    return (
      <FlatList
        data={exams}
        renderItem={renderExamItem}
        keyExtractor={(item: Exam) => item.id}
        ListHeaderComponent={() => (
          <Text style={styles.contentTitle}>آزمون‌ها</Text>
        )}
        contentContainerStyle={styles.listContentContainer}
        ListEmptyComponent={() => (
          <View style={styles.emptyContainer}>
            <Text style={styles.emptyText}>هیچ آزمونی یافت نشد</Text>
          </View>
        )}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={["#2563EB"]}
            tintColor="#2563EB"
          />
        }
      />
    );
  };

  return (
    <SafeAreaView style={styles.safeArea}>
      <StatusBar barStyle="dark-content" />
      <View style={styles.container}>
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity style={styles.headerButton}>
            <BackArrowIcon />
          </TouchableOpacity>
          {/* <Text style={styles.headerTitle}>آزمون‌ها</Text> */}
        </View>

        {/* Content */}
        {renderContent()}

        {/* Floating Action Button */}
        <TouchableOpacity
          style={styles.fab}
          onPress={async () => {
            const { router } = await import("expo-router");
            router.push("/screens/ExamAdd");
          }}
        >
          <PlusIcon />
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
};

// --- Stylesheet ---

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: "#FFFFFF",
  },
  container: {
    flex: 1,
  },
  header: {
    flexDirection: rtlStyle.flexDirection.row,
    alignItems: "center",
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: "#F3F4F6", // gray-100
  },
  headerButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: "bold",
    color: "#1F2937", // gray-800
    ...rtlStyle.marginLeft(8),
    textAlign: rtlStyle.textAlign.start,
  },
  contentTitle: {
    fontSize: 28,
    fontWeight: "600",
    color: "#1F2937",
    marginBottom: 24,
    paddingHorizontal: 24, // Consistent padding with list items
  },
  listContentContainer: {
    paddingTop: 24,
    paddingBottom: 100, // To avoid FAB overlap
  },
  itemContainer: {
    flexDirection: rtlStyle.flexDirection.row,
    alignItems: "center",
    backgroundColor: "#F9FAFB", // gray-50
    borderRadius: 16,
    padding: 16,
    marginHorizontal: 24,
    marginBottom: 16,
  },
  itemIconBackground: {
    backgroundColor: "#E5E7EB", // gray-200
    padding: 12,
    borderRadius: 12,
    justifyContent: "center",
    alignItems: "center",
  },
  itemTextContainer: {
    ...rtlStyle.marginLeft(16),
    flex: 1, // Allow text to take remaining space
  },
  itemTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: "#111827", // gray-900
  },
  itemSubtitle: {
    fontSize: 14,
    color: "#6B7280", // gray-500
    marginTop: 2,
  },
  fab: {
    position: "absolute",
    bottom: 32,
    ...rtlStyle.right(32),
    backgroundColor: "#DBEAFE", // blue-200
    width: 64,
    height: 64,
    borderRadius: 32,
    justifyContent: "center",
    alignItems: "center",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 5,
  },
  centerContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: 24,
  },
  errorText: {
    fontSize: 16,
    color: "#EF4444", // red-500
    textAlign: "center",
    marginBottom: 16,
  },
  retryButton: {
    backgroundColor: "#DBEAFE", // blue-100
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
  },
  retryButtonText: {
    color: "#2563EB", // blue-600
    fontWeight: "600",
  },
  emptyContainer: {
    padding: 24,
    alignItems: "center",
  },
  emptyText: {
    fontSize: 16,
    color: "#6B7280", // gray-500
    textAlign: "center",
  },
});

export default ExamsListScreen;
