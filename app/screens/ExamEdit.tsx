import { majorService } from "@/services/majorService";
import React, { useState, useEffect, useCallback, useMemo } from "react";
import { rtlStyle } from "@/utils/rtl";
import { useLocalSearchParams, router } from "expo-router";
import {
  validateMajorForm,
  sanitizeInput,
  getCharacterCount,
} from "@/utils/validation";
import {
  ActivityIndicator,
  Alert,
  SafeAreaView,
  ScrollView,
  StatusBar,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from "react-native";
import { Path, Svg } from "react-native-svg";

// --- Icon Components ---

const BackArrowIcon = ({ color = "#1F2937", size = 24 }) => (
  <Svg
    width={size}
    height={size}
    viewBox="0 0 24 24"
    fill="none"
    stroke={color}
    strokeWidth="2"
    strokeLinecap="round"
    strokeLinejoin="round"
  >
    <Path d="M15 18l-6-6 6-6" />
  </Svg>
);

const TrashIcon = ({ color = "#EF4444", size = 24 }) => (
  <Svg
    width={size}
    height={size}
    viewBox="0 0 24 24"
    fill="none"
    stroke={color}
    strokeWidth="2"
    strokeLinecap="round"
    strokeLinejoin="round"
  >
    <Path d="M3 6h18M8 6V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2m3 0v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6h14ZM10 11v6M14 11v6" />
  </Svg>
);

// --- Main Screen Component ---

const ExamEditScreen = () => {
  const { id } = useLocalSearchParams();
  const [name, setName] = useState("");
  const [description, setDescription] = useState("");
  const [loading, setLoading] = useState(false);
  const [deleting, setDeleting] = useState(false);
  const [initialLoading, setInitialLoading] = useState(true);
  const [nameError, setNameError] = useState("");
  const [descriptionError, setDescriptionError] = useState("");

  const loadMajor = useCallback(async () => {
    if (!id || typeof id !== "string") {
      Alert.alert("خطا", "شناسه رشته نامعتبر است");
      return;
    }

    try {
      setInitialLoading(true);
      const major = await majorService.getMajor(id);
      setName(major.name);
      setDescription(major.description || "");
    } catch (err) {
      console.error("Failed to load major:", err);
      Alert.alert("خطا", (err as Error).message || "خطا در بارگذاری رشته");
    } finally {
      setInitialLoading(false);
    }
  }, [id]);

  useEffect(() => {
    loadMajor();
  }, [loadMajor]);

  // Compute validation state
  const validation = useMemo(() => {
    return validateMajorForm(name, description);
  }, [name, description]);

  // Update error states when validation changes
  React.useEffect(() => {
    setNameError(validation.name.error || "");
    setDescriptionError(validation.description.error || "");
  }, [validation]);

  // Handle name change with validation
  const handleNameChange = (text: string) => {
    setName(text);
    // Clear error when user starts typing
    if (nameError) {
      setNameError("");
    }
  };

  // Handle description change with validation
  const handleDescriptionChange = (text: string) => {
    setDescription(text);
    // Clear error when user starts typing
    if (descriptionError) {
      setDescriptionError("");
    }
  };

  const handleSubmit = async () => {
    // Validate form before submission
    if (!validation.isFormValid) {
      Alert.alert("خطا", "لطفاً خطاهای فرم را برطرف کنید");
      return;
    }

    if (!id || typeof id !== "string") {
      Alert.alert("خطا", "شناسه رشته نامعتبر است");
      return;
    }

    try {
      setLoading(true);
      await majorService.updateMajor(id, {
        name: sanitizeInput(name),
        description: sanitizeInput(description),
      });

      Alert.alert("موفقیت", "رشته تحصیلی به‌روزرسانی شد!", [
        {
          text: "تأیید",
          onPress: async () => {
            router.back();
          },
        },
      ]);
    } catch (err) {
      console.error("Failed to update major:", err);
      Alert.alert("خطا", (err as Error).message || "خطا در به‌روزرسانی رشته");
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async () => {
    if (!id || typeof id !== "string") {
      Alert.alert("خطا", "شناسه رشته نامعتبر است");
      return;
    }

    Alert.alert(
      "تأیید حذف",
      `آیا مطمئن هستید که می‌خواهید رشته "${name}" را حذف کنید؟\n\nاین عمل قابل بازگشت نیست.`,
      [
        {
          text: "لغو",
          style: "cancel",
        },
        {
          text: "حذف",
          style: "destructive",
          onPress: async () => {
            try {
              setDeleting(true);
              await majorService.deleteMajor(id);

              Alert.alert("موفقیت", "رشته تحصیلی حذف شد!", [
                {
                  text: "تأیید",
                  onPress: async () => {
                    router.back();
                  },
                },
              ]);
            } catch (err) {
              console.error("Failed to delete major:", err);
              Alert.alert("خطا", (err as Error).message || "خطا در حذف رشته");
            } finally {
              setDeleting(false);
            }
          },
        },
      ]
    );
  };

  const handleBack = async () => {
    router.back();
  };

  if (initialLoading) {
    return (
      <SafeAreaView style={styles.safeArea}>
        <StatusBar barStyle="dark-content" />
        <View style={styles.centerContainer}>
          <ActivityIndicator size="large" color="#2563EB" />
          <Text style={styles.loadingText}>در حال بارگذاری...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.safeArea}>
      <StatusBar barStyle="dark-content" />
      <View style={styles.container}>
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity style={styles.headerButton} onPress={handleBack}>
            <BackArrowIcon />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>ویرایش رشته تحصیلی</Text>
          <TouchableOpacity
            style={styles.deleteButton}
            onPress={handleDelete}
            disabled={deleting}
          >
            {deleting ? (
              <ActivityIndicator size="small" color="#EF4444" />
            ) : (
              <TrashIcon />
            )}
          </TouchableOpacity>
        </View>

        {/* Form Content */}
        <ScrollView
          style={styles.scrollView}
          contentContainerStyle={styles.formContainer}
        >
          <View style={styles.inputContainer}>
            <View style={styles.labelContainer}>
              <Text style={styles.label}>نام رشته *</Text>
              <Text style={styles.characterCount}>
                {getCharacterCount(name)}/100
              </Text>
            </View>
            <TextInput
              style={[styles.input, nameError ? styles.inputError : null]}
              value={name}
              onChangeText={handleNameChange}
              placeholder="نام رشته را وارد کنید"
              placeholderTextColor="#9CA3AF"
              editable={!loading && !deleting}
              maxLength={100}
            />
            {nameError ? (
              <Text style={styles.errorText}>{nameError}</Text>
            ) : null}
          </View>

          <View style={styles.inputContainer}>
            <View style={styles.labelContainer}>
              <Text style={styles.label}>توضیحات</Text>
              <Text style={styles.characterCount}>
                {getCharacterCount(description)}/500
              </Text>
            </View>
            <TextInput
              style={[
                styles.input,
                styles.textArea,
                descriptionError ? styles.inputError : null,
              ]}
              value={description}
              onChangeText={handleDescriptionChange}
              placeholder="توضیحات را وارد کنید"
              placeholderTextColor="#9CA3AF"
              multiline
              numberOfLines={4}
              textAlignVertical="top"
              editable={!loading && !deleting}
              maxLength={500}
            />
            {descriptionError ? (
              <Text style={styles.errorText}>{descriptionError}</Text>
            ) : null}
          </View>

          <TouchableOpacity
            style={[
              styles.submitButton,
              (loading || deleting || !validation.isFormValid) &&
                styles.submitButtonDisabled,
            ]}
            onPress={handleSubmit}
            disabled={loading || deleting || !validation.isFormValid}
          >
            {loading ? (
              <ActivityIndicator size="small" color="#FFFFFF" />
            ) : (
              <Text style={styles.submitButtonText}>به‌روزرسانی</Text>
            )}
          </TouchableOpacity>
        </ScrollView>
      </View>
    </SafeAreaView>
  );
};

// --- Stylesheet ---

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: "#FFFFFF",
  },
  container: {
    flex: 1,
  },
  centerContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: 24,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: "#6B7280",
  },
  header: {
    flexDirection: rtlStyle.flexDirection.row,
    alignItems: "center",
    justifyContent: "space-between",
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: "#F3F4F6", // gray-100
  },
  headerButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: "bold",
    color: "#1F2937", // gray-800
    flex: 1,
    ...rtlStyle.marginLeft(8),
    textAlign: rtlStyle.textAlign.start,
  },
  deleteButton: {
    padding: 8,
    borderRadius: 8,
    backgroundColor: "#FEF2F2", // red-50
  },
  scrollView: {
    flex: 1,
  },
  formContainer: {
    padding: 24,
  },
  inputContainer: {
    marginBottom: 24,
  },
  labelContainer: {
    flexDirection: rtlStyle.flexDirection.row,
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 8,
  },
  label: {
    fontSize: 16,
    fontWeight: "600",
    color: "#1F2937",
  },
  characterCount: {
    fontSize: 12,
    color: "#6B7280", // gray-500
  },
  input: {
    borderWidth: 1,
    borderColor: "#D1D5DB",
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
    color: "#1F2937",
    backgroundColor: "#FFFFFF",
    textAlign: rtlStyle.textAlign.start,
  },
  inputError: {
    borderColor: "#EF4444", // red-500
    backgroundColor: "#FEF2F2", // red-50
  },
  errorText: {
    fontSize: 14,
    color: "#EF4444", // red-500
    marginTop: 4,
    textAlign: rtlStyle.textAlign.start,
  },
  textArea: {
    height: 100,
    textAlignVertical: "top",
  },
  submitButton: {
    backgroundColor: "#2563EB",
    borderRadius: 12,
    paddingVertical: 16,
    alignItems: "center",
    marginTop: 16,
  },
  submitButtonDisabled: {
    backgroundColor: "#9CA3AF",
  },
  submitButtonText: {
    color: "#FFFFFF",
    fontSize: 16,
    fontWeight: "600",
  },
});

export default ExamEditScreen;
