import React from 'react';
import { View, StyleSheet } from 'react-native';
import { Surface } from 'react-native-paper';
import {
  PaperTextInput,
  PaperCheckbox,
  PaperSwitch,
  PaperRadioGroup,
  PaperSegmentedButtons,
  PaperFormField,
  PaperFormSection,
  PaperButton,
} from './index';
import { 
  useFormValidation, 
  ValidationRule, 
  PaperFormValidationSummary,
  type PaperRadioOption 
} from './PaperFormValidation';

// Form field types
export type FormFieldType = 
  | 'text' 
  | 'email' 
  | 'password' 
  | 'phone' 
  | 'number' 
  | 'textarea' 
  | 'checkbox' 
  | 'switch' 
  | 'radio' 
  | 'segmented';

// Form field configuration
export interface FormFieldConfig {
  name: string;
  type: FormFieldType;
  label: string;
  placeholder?: string;
  required?: boolean;
  validation?: ValidationRule;
  options?: PaperRadioOption[] | Array<{ value: string; label: string; icon?: string }>;
  multiline?: boolean;
  numberOfLines?: number;
  disabled?: boolean;
  helperText?: string;
}

// Form section configuration
export interface FormSectionConfig {
  title?: string;
  fields: FormFieldConfig[];
}

// Form configuration
export interface FormConfig {
  sections: FormSectionConfig[];
  submitButtonText?: string;
  resetButtonText?: string;
  showValidationSummary?: boolean;
}

// Form builder props
export interface PaperFormBuilderProps<T extends Record<string, any>> {
  config: FormConfig;
  initialValues: T;
  onSubmit: (values: T) => void | Promise<void>;
  onReset?: () => void;
  loading?: boolean;
  style?: any;
}

export function PaperFormBuilder<T extends Record<string, any>>({
  config,
  initialValues,
  onSubmit,
  onReset,
  loading = false,
  style,
}: PaperFormBuilderProps<T>) {
  // Build validation rules from config
  const validationRules = React.useMemo(() => {
    const rules: Partial<Record<keyof T, ValidationRule>> = {};
    
    config.sections.forEach(section => {
      section.fields.forEach(field => {
        if (field.validation || field.required) {
          rules[field.name as keyof T] = {
            required: field.required,
            ...field.validation,
          };
        }
      });
    });
    
    return rules;
  }, [config]);

  const {
    values,
    errors,
    touched,
    setValue,
    setFieldTouched,
    validateForm,
    resetForm,
    isFieldValid,
  } = useFormValidation(initialValues, validationRules);

  const handleSubmit = async () => {
    if (validateForm()) {
      await onSubmit(values);
    }
  };

  const handleReset = () => {
    resetForm();
    onReset?.();
  };

  const renderField = (field: FormFieldConfig) => {
    const fieldError = touched[field.name as keyof T] ? errors[field.name as keyof T] : '';
    const fieldValue = values[field.name as keyof T];

    switch (field.type) {
      case 'text':
      case 'email':
      case 'password':
      case 'phone':
      case 'number':
        return (
          <PaperFormField
            key={field.name}
            label={field.label}
            error={fieldError}
            required={field.required}
          >
            <PaperTextInput
              value={fieldValue || ''}
              onChangeText={(value) => setValue(field.name as keyof T, value)}
              onBlur={() => setFieldTouched(field.name as keyof T)}
              placeholder={field.placeholder}
              disabled={field.disabled || loading}
              error={!!fieldError}
              keyboardType={
                field.type === 'email' ? 'email-address' :
                field.type === 'phone' ? 'phone-pad' :
                field.type === 'number' ? 'numeric' : 'default'
              }
              secureTextEntry={field.type === 'password'}
              autoCapitalize={field.type === 'email' ? 'none' : 'sentences'}
            />
          </PaperFormField>
        );

      case 'textarea':
        return (
          <PaperFormField
            key={field.name}
            label={field.label}
            error={fieldError}
            required={field.required}
          >
            <PaperTextInput
              value={fieldValue || ''}
              onChangeText={(value) => setValue(field.name as keyof T, value)}
              onBlur={() => setFieldTouched(field.name as keyof T)}
              placeholder={field.placeholder}
              disabled={field.disabled || loading}
              error={!!fieldError}
              multiline={true}
              numberOfLines={field.numberOfLines || 3}
            />
          </PaperFormField>
        );

      case 'checkbox':
        return (
          <PaperFormField
            key={field.name}
            error={fieldError}
            required={field.required}
          >
            <PaperCheckbox
              status={fieldValue ? 'checked' : 'unchecked'}
              onPress={() => {
                setValue(field.name as keyof T, !fieldValue);
                setFieldTouched(field.name as keyof T);
              }}
              label={field.label}
              disabled={field.disabled || loading}
            />
          </PaperFormField>
        );

      case 'switch':
        return (
          <PaperFormField
            key={field.name}
            error={fieldError}
            required={field.required}
          >
            <PaperSwitch
              value={fieldValue || false}
              onValueChange={(value) => {
                setValue(field.name as keyof T, value);
                setFieldTouched(field.name as keyof T);
              }}
              label={field.label}
              disabled={field.disabled || loading}
            />
          </PaperFormField>
        );

      case 'radio':
        return (
          <PaperFormField
            key={field.name}
            label={field.label}
            error={fieldError}
            required={field.required}
          >
            <PaperRadioGroup
              options={field.options as PaperRadioOption[] || []}
              value={fieldValue || ''}
              onValueChange={(value) => {
                setValue(field.name as keyof T, value);
                setFieldTouched(field.name as keyof T);
              }}
            />
          </PaperFormField>
        );

      case 'segmented':
        return (
          <PaperFormField
            key={field.name}
            label={field.label}
            error={fieldError}
            required={field.required}
          >
            <PaperSegmentedButtons
              value={fieldValue || ''}
              onValueChange={(value) => {
                setValue(field.name as keyof T, value);
                setFieldTouched(field.name as keyof T);
              }}
              buttons={field.options?.map(option => ({
                value: option.value,
                label: option.label,
                icon: option.icon,
              })) || []}
            />
          </PaperFormField>
        );

      default:
        return null;
    }
  };

  return (
    <Surface style={[styles.container, style]}>
      {/* Validation Summary */}
      {config.showValidationSummary && (
        <PaperFormValidationSummary
          errors={errors as Record<string, string>}
          visible={Object.keys(errors).length > 0}
        />
      )}

      {/* Form Sections */}
      {config.sections.map((section, sectionIndex) => (
        <PaperFormSection
          key={sectionIndex}
          title={section.title}
          style={styles.section}
        >
          {section.fields.map(renderField)}
        </PaperFormSection>
      ))}

      {/* Action Buttons */}
      <View style={styles.actions}>
        {config.resetButtonText && (
          <PaperButton
            title={config.resetButtonText}
            mode="outlined"
            onPress={handleReset}
            disabled={loading}
            style={styles.resetButton}
          />
        )}
        <PaperButton
          title={config.submitButtonText || 'ذخیره'}
          mode="contained"
          onPress={handleSubmit}
          loading={loading}
          disabled={loading}
          style={styles.submitButton}
        />
      </View>
    </Surface>
  );
}

const styles = StyleSheet.create({
  container: {
    padding: 16,
  },
  section: {
    marginBottom: 16,
  },
  actions: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    marginTop: 24,
    gap: 12,
  },
  resetButton: {
    flex: 1,
  },
  submitButton: {
    flex: 2,
  },
});
