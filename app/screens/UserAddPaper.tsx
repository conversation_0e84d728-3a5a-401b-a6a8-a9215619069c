import React, { useState } from 'react';
import { StyleSheet, SafeAreaView } from 'react-native';
import { Surface } from 'react-native-paper';
import { router } from 'expo-router';
import { userService } from '@/services/userService';
import {
  PaperFormAppBar,
  PaperFormBuilder,
  PaperAlertDialog,
  type FormConfig,
  commonValidationRules,
} from '@/components/paper';
import { t } from '@/constants/Localization';

interface UserFormData {
  email: string;
  mobile: string;
  password: string;
  confirmPassword: string;
  firstName: string;
  lastName: string;
  role: string;
}

export default function UserAddPaperScreen() {
  const [loading, setLoading] = useState(false);
  const [showAlert, setShowAlert] = useState(false);
  const [alertMessage, setAlertMessage] = useState('');
  const [alertTitle, setAlertTitle] = useState('');

  const initialValues: UserFormData = {
    email: '',
    mobile: '',
    password: '',
    confirmPassword: '',
    firstName: '',
    lastName: '',
    role: '',
  };

  const formConfig: FormConfig = {
    sections: [
      {
        title: t('accountInfo') || 'اطلاعات حساب کاربری',
        fields: [
          {
            name: 'email',
            type: 'email',
            label: t('email'),
            placeholder: '<EMAIL>',
            required: true,
            validation: commonValidationRules.email,
          },
          {
            name: 'mobile',
            type: 'phone',
            label: t('mobile') || 'شماره موبایل',
            placeholder: '***********',
            required: true,
            validation: commonValidationRules.phone,
          },
          {
            name: 'password',
            type: 'password',
            label: t('password'),
            placeholder: 'حداقل 8 کاراکتر',
            required: true,
            validation: commonValidationRules.password,
          },
          {
            name: 'confirmPassword',
            type: 'password',
            label: t('confirmPassword'),
            placeholder: 'تکرار رمز عبور',
            required: true,
            validation: {
              required: true,
              custom: (value: string, formValues?: any) => {
                if (formValues?.password && value !== formValues.password) {
                  return t('passwordMismatch') || 'رمزهای عبور مطابقت ندارند';
                }
                return null;
              },
            },
          },
        ],
      },
      {
        title: t('personalInfo') || 'اطلاعات شخصی',
        fields: [
          {
            name: 'firstName',
            type: 'text',
            label: t('firstName'),
            placeholder: 'نام',
            required: true,
            validation: commonValidationRules.name,
          },
          {
            name: 'lastName',
            type: 'text',
            label: t('lastName'),
            placeholder: 'نام خانوادگی',
            required: true,
            validation: commonValidationRules.name,
          },
          {
            name: 'role',
            type: 'radio',
            label: t('role'),
            required: true,
            options: [
              { label: t('admin') || 'مدیر', value: 'admin' },
              { label: t('teacher') || 'مدرس', value: 'teacher' },
              { label: t('student') || 'دانشجو', value: 'student' },
            ],
            validation: { required: true },
          },
        ],
      },
    ],
    submitButtonText: t('addUser') || 'افزودن کاربر',
    resetButtonText: t('reset') || 'پاک کردن',
    showValidationSummary: true,
  };

  const handleSubmit = async (values: UserFormData) => {
    setLoading(true);
    try {
      await userService.createUser({
        email: values.email,
        mobile: values.mobile,
        password_hash: values.password,
        first_name: values.firstName,
        last_name: values.lastName,
        role: values.role,
      });
      
      setAlertTitle(t('success'));
      setAlertMessage(t('userAddSuccess') || 'کاربر با موفقیت اضافه شد!');
      setShowAlert(true);
      
      // Navigate back after showing success message
      setTimeout(() => {
        router.back();
      }, 1500);
    } catch (error) {
      setAlertTitle(t('error'));
      setAlertMessage(t('userAddError') || 'افزودن کاربر انجام نشد. لطفا دوباره امتحان کنید.');
      setShowAlert(true);
      console.error('Failed to add user:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleReset = () => {
    // Additional reset logic if needed
    console.log('Form reset');
  };

  return (
    <SafeAreaView style={styles.container}>
      {/* App Bar */}
      <PaperFormAppBar
        title={t('addUser') || 'افزودن کاربر'}
        onSavePress={() => {}} // Handled by form builder
        saveDisabled={loading}
        showSave={false} // Form builder handles submit
      />

      {/* Content */}
      <Surface style={styles.content}>
        <PaperFormBuilder
          config={formConfig}
          initialValues={initialValues}
          onSubmit={handleSubmit}
          onReset={handleReset}
          loading={loading}
          style={styles.form}
        />
      </Surface>

      {/* Alert Dialog */}
      <PaperAlertDialog
        visible={showAlert}
        onDismiss={() => setShowAlert(false)}
        title={alertTitle}
        message={alertMessage}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
  },
  form: {
    flex: 1,
  },
});
