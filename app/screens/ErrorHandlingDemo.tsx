import React, { useState } from 'react';
import { View, StyleSheet, SafeAreaView } from 'react-native';
import { Appbar, Surface } from 'react-native-paper';
import { router } from 'expo-router';
import { 
  PaperButton, 
  PaperCard, 
  PaperText, 
  PaperTitle 
} from '@/components/paper';
import { useApiError } from '@/hooks/useApiError';
import { api, ApiError } from '@/services/api';
import { t } from '@/constants/Localization';

/**
 * Demo screen to showcase the error handling system
 */
export default function ErrorHandlingDemoScreen() {
  const [loading, setLoading] = useState(false);
  const { handleApiError, showSuccess, showLoadError, toast } = useApiError();

  const handleBack = () => {
    router.back();
  };

  // Simulate network error
  const simulateNetworkError = async () => {
    setLoading(true);
    try {
      // This will fail with a network error
      await fetch('http://invalid-url-that-will-fail.com/api/test');
    } catch (error) {
      handleApiError(error, 'Network Error Demo', simulateNetworkError);
    } finally {
      setLoading(false);
    }
  };

  // Simulate server error (500)
  const simulateServerError = async () => {
    setLoading(true);
    try {
      // Create a mock server error
      throw new ApiError('Internal server error', 500);
    } catch (error) {
      handleApiError(error, 'Server Error Demo', simulateServerError);
    } finally {
      setLoading(false);
    }
  };

  // Simulate validation error (400)
  const simulateValidationError = async () => {
    setLoading(true);
    try {
      // Create a mock validation error
      throw new ApiError('Invalid input data', 400, { field: 'email', message: 'Invalid email format' });
    } catch (error) {
      handleApiError(error, 'Validation Error Demo');
    } finally {
      setLoading(false);
    }
  };

  // Simulate unauthorized error (401)
  const simulateUnauthorizedError = async () => {
    setLoading(true);
    try {
      // Create a mock unauthorized error
      throw new ApiError('Unauthorized access', 401);
    } catch (error) {
      handleApiError(error, 'Unauthorized Error Demo');
    } finally {
      setLoading(false);
    }
  };

  // Simulate timeout error
  const simulateTimeoutError = async () => {
    setLoading(true);
    try {
      // Create a mock timeout error
      throw new Error('Connection timeout');
    } catch (error) {
      handleApiError(error, 'Timeout Error Demo', simulateTimeoutError);
    } finally {
      setLoading(false);
    }
  };

  // Simulate successful operation
  const simulateSuccess = async () => {
    setLoading(true);
    try {
      // Simulate some async work
      await new Promise(resolve => setTimeout(resolve, 1000));
      showSuccess(t('operationSuccessful'));
    } catch (error) {
      handleApiError(error, 'Success Demo');
    } finally {
      setLoading(false);
    }
  };

  // Test different toast types
  const showInfoToast = () => {
    toast.showInfo('This is an info message');
  };

  const showWarningToast = () => {
    toast.showWarning('This is a warning message');
  };

  const showErrorToast = () => {
    toast.showError('This is an error message');
  };

  const showSuccessToast = () => {
    toast.showSuccess('This is a success message');
  };

  return (
    <SafeAreaView style={styles.container}>
      <Appbar.Header>
        <Appbar.BackAction onPress={handleBack} />
        <Appbar.Content title="Error Handling Demo" />
      </Appbar.Header>

      <Surface style={styles.surface}>
        <View style={styles.content}>
          <PaperTitle style={styles.title}>
            Error Handling System Demo
          </PaperTitle>

          <PaperText style={styles.description}>
            Test different error scenarios and see how the error handling system responds with appropriate toast notifications and retry options.
          </PaperText>

          <PaperCard style={styles.section}>
            <PaperCard.Content>
              <PaperText variant="titleMedium" style={styles.sectionTitle}>
                API Error Scenarios
              </PaperText>
              
              <View style={styles.buttonGroup}>
                <PaperButton
                  title="Network Error (with retry)"
                  onPress={simulateNetworkError}
                  loading={loading}
                  mode="outlined"
                  style={styles.button}
                />
                
                <PaperButton
                  title="Server Error 500 (with retry)"
                  onPress={simulateServerError}
                  loading={loading}
                  mode="outlined"
                  style={styles.button}
                />
                
                <PaperButton
                  title="Validation Error 400"
                  onPress={simulateValidationError}
                  loading={loading}
                  mode="outlined"
                  style={styles.button}
                />
                
                <PaperButton
                  title="Unauthorized Error 401"
                  onPress={simulateUnauthorizedError}
                  loading={loading}
                  mode="outlined"
                  style={styles.button}
                />
                
                <PaperButton
                  title="Timeout Error (with retry)"
                  onPress={simulateTimeoutError}
                  loading={loading}
                  mode="outlined"
                  style={styles.button}
                />
              </View>
            </PaperCard.Content>
          </PaperCard>

          <PaperCard style={styles.section}>
            <PaperCard.Content>
              <PaperText variant="titleMedium" style={styles.sectionTitle}>
                Toast Notifications
              </PaperText>
              
              <View style={styles.buttonGroup}>
                <PaperButton
                  title="Success Toast"
                  onPress={showSuccessToast}
                  mode="contained"
                  style={[styles.button, styles.successButton]}
                />
                
                <PaperButton
                  title="Info Toast"
                  onPress={showInfoToast}
                  mode="contained"
                  style={[styles.button, styles.infoButton]}
                />
                
                <PaperButton
                  title="Warning Toast"
                  onPress={showWarningToast}
                  mode="contained"
                  style={[styles.button, styles.warningButton]}
                />
                
                <PaperButton
                  title="Error Toast"
                  onPress={showErrorToast}
                  mode="contained"
                  style={[styles.button, styles.errorButton]}
                />
              </View>
            </PaperCard.Content>
          </PaperCard>

          <PaperCard style={styles.section}>
            <PaperCard.Content>
              <PaperText variant="titleMedium" style={styles.sectionTitle}>
                Success Scenario
              </PaperText>
              
              <PaperButton
                title="Simulate Success"
                onPress={simulateSuccess}
                loading={loading}
                mode="contained"
                style={styles.button}
              />
            </PaperCard.Content>
          </PaperCard>
        </View>
      </Surface>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  surface: {
    flex: 1,
    margin: 16,
    borderRadius: 8,
  },
  content: {
    padding: 16,
  },
  title: {
    textAlign: 'center',
    marginBottom: 16,
    fontFamily: 'Vazirmatn',
  },
  description: {
    textAlign: 'center',
    marginBottom: 24,
    fontFamily: 'Vazirmatn',
    opacity: 0.7,
  },
  section: {
    marginBottom: 16,
  },
  sectionTitle: {
    marginBottom: 16,
    fontFamily: 'Vazirmatn',
  },
  buttonGroup: {
    gap: 8,
  },
  button: {
    marginVertical: 4,
  },
  successButton: {
    backgroundColor: '#4CAF50',
  },
  infoButton: {
    backgroundColor: '#2196F3',
  },
  warningButton: {
    backgroundColor: '#FF9800',
  },
  errorButton: {
    backgroundColor: '#F44336',
  },
});
