import React from 'react';
import { render, fireEvent, waitFor } from '@testing-library/react-native';
import { Alert } from 'react-native';
import UsersListScreen from '../../app/screens/UsersList';
import { userService } from '../../services/userService';

// Mock the user service
jest.mock('../../services/userService', () => ({
  userService: {
    getUsers: jest.fn(),
    deleteUser: jest.fn(),
  },
}));

const mockUsers = [
  {
    id: '1',
    name: '<PERSON>',
    email: '<EMAIL>',
    createdAt: '2023-01-01T00:00:00Z',
  },
  {
    id: '2',
    name: '<PERSON>',
    email: '<EMAIL>',
    createdAt: '2023-01-02T00:00:00Z',
  },
];

describe('UsersListScreen', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    (userService.getUsers as jest.Mock).mockResolvedValue(mockUsers);
  });

  it('renders correctly with users', async () => {
    const { getByText } = render(<UsersListScreen />);
    
    await waitFor(() => {
      expect(getByText('Users')).toBeTruthy();
      expect(getByText('John Doe')).toBeTruthy();
      expect(getByText('Jane Smith')).toBeTruthy();
    });
  });

  it('shows loading state initially', () => {
    const { getByText } = render(<UsersListScreen />);
    expect(getByText('Loading users...')).toBeTruthy();
  });

  it('handles pull-to-refresh functionality', async () => {
    const { getByTestId } = render(<UsersListScreen />);
    
    // Wait for initial load
    await waitFor(() => {
      expect(userService.getUsers).toHaveBeenCalledTimes(1);
    });

    // Find the FlatList and trigger refresh
    const flatList = getByTestId('users-flatlist');
    
    // Simulate pull-to-refresh
    fireEvent(flatList, 'refresh');
    
    await waitFor(() => {
      expect(userService.getUsers).toHaveBeenCalledTimes(2);
    });
  });

  it('displays error message when fetch fails', async () => {
    (userService.getUsers as jest.Mock).mockRejectedValue(new Error('Network error'));
    
    const { getByText } = render(<UsersListScreen />);
    
    await waitFor(() => {
      expect(getByText('Failed to fetch users.')).toBeTruthy();
    });
  });

  it('shows no data message when users list is empty', async () => {
    (userService.getUsers as jest.Mock).mockResolvedValue([]);
    
    const { getByText } = render(<UsersListScreen />);
    
    await waitFor(() => {
      expect(getByText('No users found.')).toBeTruthy();
    });
  });

  it('handles delete user functionality', async () => {
    (userService.deleteUser as jest.Mock).mockResolvedValue(undefined);
    const alertSpy = jest.spyOn(Alert, 'alert');
    
    const { getByText } = render(<UsersListScreen />);
    
    await waitFor(() => {
      expect(getByText('John Doe')).toBeTruthy();
    });

    // Find and press delete button
    const deleteButton = getByText('Delete');
    fireEvent.press(deleteButton);

    // Verify Alert.alert was called
    expect(alertSpy).toHaveBeenCalledWith(
      'Delete User',
      'Are you sure you want to delete this user?',
      expect.any(Array),
      expect.any(Object)
    );
  });

  it('refreshes data after successful delete', async () => {
    (userService.deleteUser as jest.Mock).mockResolvedValue(undefined);
    const alertSpy = jest.spyOn(Alert, 'alert').mockImplementation((title, message, buttons) => {
      // Simulate user pressing "Delete" button
      if (buttons && buttons[1] && buttons[1].onPress) {
        buttons[1].onPress();
      }
    });
    
    const { getByText } = render(<UsersListScreen />);
    
    await waitFor(() => {
      expect(getByText('John Doe')).toBeTruthy();
    });

    // Trigger delete
    const deleteButton = getByText('Delete');
    fireEvent.press(deleteButton);

    await waitFor(() => {
      // Should call getUsers again after delete (initial + refresh)
      expect(userService.getUsers).toHaveBeenCalledTimes(2);
    });

    alertSpy.mockRestore();
  });

  it('handles refresh error gracefully', async () => {
    const { getByTestId } = render(<UsersListScreen />);
    
    // Wait for initial load
    await waitFor(() => {
      expect(userService.getUsers).toHaveBeenCalledTimes(1);
    });

    // Mock refresh to fail
    (userService.getUsers as jest.Mock).mockRejectedValueOnce(new Error('Refresh failed'));

    // Find the FlatList and trigger refresh
    const flatList = getByTestId('users-flatlist');
    fireEvent(flatList, 'refresh');
    
    await waitFor(() => {
      expect(getByText('Failed to refresh users.')).toBeTruthy();
    });
  });

  it('renders add new user button', async () => {
    const { getByText } = render(<UsersListScreen />);
    
    await waitFor(() => {
      expect(getByText('Add New User')).toBeTruthy();
    });
  });

  it('renders edit buttons for each user', async () => {
    const { getAllByText } = render(<UsersListScreen />);
    
    await waitFor(() => {
      const editButtons = getAllByText('Edit');
      expect(editButtons).toHaveLength(2); // One for each user
    });
  });
});
