import React, { useState, useEffect } from "react";
import { StyleSheet, TextInput, Pressable, Alert } from "react-native";
import { ThemedText } from "@/components/ThemedText";
import { ThemedView } from "@/components/ThemedView";
import { courseService } from "@/services/courseService";
import { courseGroupService, CourseGroup } from "@/services/courseGroupService";
import KeyboardAvoidingWrapper from "@/components/KeyboardAvoidingWrapper";
import { router } from "expo-router";
// @ts-ignore
import { Picker } from "@react-native-picker/picker";

export default function CourseAddScreen() {
  const [name, setName] = useState("");
  const [description, setDescription] = useState("");
  const [coefficient, setCoefficient] = useState("");
  const [negativeMarkingFactor, setNegativeMarkingFactor] = useState("");
  const [courseGroupId, setCourseGroupId] = useState<string | null>(null);
  const [courseGroups, setCourseGroups] = useState<CourseGroup[]>([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    const fetchCourseGroups = async () => {
      try {
        const response = await courseGroupService.getCourseGroups();
        setCourseGroups(response);
        if (response.length > 0) {
          setCourseGroupId(response[0].id);
        }
      } catch (error) {
        console.error("Failed to fetch course groups:", error);
        Alert.alert("خطا", "خطا در بارگیری گروه‌های درسی.");
      }
    };
    fetchCourseGroups();
  }, []);

  const handleAddCourse = async () => {
    if (
      !name.trim() ||
      !description.trim() ||
      !coefficient.trim() ||
      !negativeMarkingFactor.trim() ||
      courseGroupId === null
    ) {
      Alert.alert("خطای اعتبارسنجی", "لطفاً تمام فیلدهای الزامی را پر کنید.");
      return;
    }

    const parsedCoefficient = parseFloat(coefficient);
    const parsedNegativeMarkingFactor = parseFloat(negativeMarkingFactor);
    const parsedCourseGroupId = parseInt(courseGroupId, 10);

    if (
      isNaN(parsedCoefficient) ||
      isNaN(parsedNegativeMarkingFactor) ||
      isNaN(parsedCourseGroupId)
    ) {
      Alert.alert(
        "خطای اعتبارسنجی",
        "لطفاً مقادیر عددی را به درستی وارد کنید."
      );
      return;
    }

    setLoading(true);
    try {
      await courseService.createCourse({
        name,
        description,
        coefficient: parsedCoefficient,
        negative_marking_factor: parsedNegativeMarkingFactor,
        course_group_id: parsedCourseGroupId,
      });
      Alert.alert("موفقیت", "درس با موفقیت اضافه شد!");
      router.back();
    } catch (error) {
      Alert.alert("خطا", "افزودن درس ناموفق بود. لطفاً دوباره تلاش کنید.");
      console.error("Failed to add course:", error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <KeyboardAvoidingWrapper style={styles.container}>
      <ThemedText style={styles.title}>افزودن درس جدید</ThemedText>
      <TextInput
        style={styles.input}
        placeholder="نام درس"
        value={name}
        onChangeText={setName}
        editable={!loading}
      />
      <TextInput
        style={styles.input}
        placeholder="توضیحات درس"
        value={description}
        onChangeText={setDescription}
        editable={!loading}
      />
      <TextInput
        style={styles.input}
        placeholder="ضریب"
        value={coefficient}
        onChangeText={setCoefficient}
        keyboardType="numeric"
        editable={!loading}
      />
      <TextInput
        style={styles.input}
        placeholder="ضریب نمره منفی"
        value={negativeMarkingFactor}
        onChangeText={setNegativeMarkingFactor}
        keyboardType="numeric"
        editable={!loading}
      />
      <ThemedText style={styles.pickerLabel}>گروه درسی:</ThemedText>
      <Picker
        selectedValue={courseGroupId}
        onValueChange={(itemValue: string) => setCourseGroupId(itemValue)}
        style={styles.picker}
        enabled={!loading}
      >
        {courseGroups.map((group: CourseGroup) => (
          <Picker.Item key={group.id} label={group.name} value={group.id} />
        ))}
      </Picker>
      <Pressable
        style={[styles.button, loading && styles.buttonDisabled]}
        onPress={handleAddCourse}
        disabled={loading}
      >
        <ThemedText style={styles.buttonText}>
          {loading ? "در حال افزودن..." : "افزودن درس"}
        </ThemedText>
      </Pressable>
    </KeyboardAvoidingWrapper>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
    justifyContent: "center",
  },
  title: {
    fontSize: 24,
    fontWeight: "bold",
    marginBottom: 20,
    textAlign: "center",
  },
  input: {
    height: 40,
    borderColor: "#ccc",
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 10,
    marginBottom: 15,
    backgroundColor: "#fff",
  },
  pickerLabel: {
    fontSize: 16,
    marginBottom: 5,
    color: "#333",
  },
  picker: {
    height: 50,
    width: "100%",
    marginBottom: 15,
    borderColor: "#ccc",
    borderWidth: 1,
    borderRadius: 8,
    backgroundColor: "#fff",
  },
  button: {
    backgroundColor: "#007bff",
    padding: 12,
    borderRadius: 8,
    alignItems: "center",
  },
  buttonText: {
    color: "#fff",
    fontWeight: "bold",
    fontSize: 16,
  },
  buttonDisabled: {
    backgroundColor: "#a0c9ff",
  },
});
