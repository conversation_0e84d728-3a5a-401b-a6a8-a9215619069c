import React, { useEffect, useState } from 'react';
import { ActivityIndicator, StyleSheet } from 'react-native';
import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { Option, optionService } from '@/services/optionService';
import { useLocalSearchParams } from 'expo-router';

export default function OptionDetailsScreen() {
  const { id } = useLocalSearchParams();
  const [option, setOption] = useState<Option | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (id) {
      const fetchOption = async () => {
        try {
          const data = await optionService.getOption(id as string);
          setOption(data);
        } catch (err) {
          setError('Failed to fetch option details.');
          console.error(err);
        } finally {
          setLoading(false);
        }
      };
      fetchOption();
    } else {
      setError('Option ID not provided.');
      setLoading(false);
    }
  }, [id]);

  if (loading) {
    return (
      <ThemedView style={styles.centered}>
        <ActivityIndicator size="large" />
        <ThemedText>Loading option details...</ThemedText>
      </ThemedView>
    );
  }

  if (error) {
    return (
      <ThemedView style={styles.centered}>
        <ThemedText style={styles.errorText}>{error}</ThemedText>
      </ThemedView>
    );
  }

  if (!option) {
    return (
      <ThemedView style={styles.centered}>
        <ThemedText>Option not found.</ThemedText>
      </ThemedView>
    );
  }

  return (
    <ThemedView style={styles.container}>
      <ThemedText style={styles.title}>Option Details</ThemedText>
      <ThemedView style={styles.detailItem}>
        <ThemedText style={styles.label}>ID:</ThemedText>
        <ThemedText>{option.id}</ThemedText>
      </ThemedView>
      {/* Add other option properties here based on your schema */}
      {option.createdAt && (
        <ThemedView style={styles.detailItem}>
          <ThemedText style={styles.label}>Created At:</ThemedText>
          <ThemedText>{new Date(option.createdAt).toLocaleString()}</ThemedText>
        </ThemedView>
      )}
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  centered: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 20,
  },
  detailItem: {
    flexDirection: 'row',
    marginBottom: 10,
  },
  label: {
    fontWeight: 'bold',
    marginRight: 5,
  },
  errorText: {
    color: 'red',
    fontSize: 16,
  },
});
