import React from 'react';
import { BottomNavigation, useTheme } from 'react-native-paper';
import { StyleSheet } from 'react-native';

export interface PaperBottomNavigationRoute {
  key: string;
  title: string;
  focusedIcon: string;
  unfocusedIcon?: string;
  badge?: string | number | boolean;
  accessibilityLabel?: string;
  testID?: string;
}

export interface PaperBottomNavigationProps {
  navigationState: {
    index: number;
    routes: PaperBottomNavigationRoute[];
  };
  onIndexChange: (index: number) => void;
  renderScene: (props: { route: PaperBottomNavigationRoute; jumpTo: (key: string) => void }) => React.ReactNode;
  shifting?: boolean;
  labeled?: boolean;
  barStyle?: any;
  activeColor?: string;
  inactiveColor?: string;
  keyboardHidesNavigationBar?: boolean;
  safeAreaInsets?: {
    bottom?: number;
    top?: number;
    left?: number;
    right?: number;
  };
}

export function PaperBottomNavigationComponent({
  navigationState,
  onIndexChange,
  renderScene,
  shifting = false,
  labeled = true,
  barStyle,
  activeColor,
  inactiveColor,
  keyboardHidesNavigationBar = true,
  safeAreaInsets,
}: PaperBottomNavigationProps) {
  const theme = useTheme();

  return (
    <BottomNavigation
      navigationState={navigationState}
      onIndexChange={onIndexChange}
      renderScene={renderScene}
      shifting={shifting}
      labeled={labeled}
      barStyle={[styles.bottomNavigation, barStyle]}
      activeColor={activeColor || theme.colors.primary}
      inactiveColor={inactiveColor || theme.colors.onSurfaceVariant}
      keyboardHidesNavigationBar={keyboardHidesNavigationBar}
      safeAreaInsets={safeAreaInsets}
      getLabelText={({ route }) => route.title}
      getAccessibilityLabel={({ route }) => route.accessibilityLabel || route.title}
      getBadge={({ route }) => route.badge}
      getTestID={({ route }) => route.testID}
    />
  );
}

// Hook for managing bottom navigation state
export function useBottomNavigation(initialIndex: number = 0, routes: PaperBottomNavigationRoute[]) {
  const [index, setIndex] = React.useState(initialIndex);

  const navigationState = React.useMemo(() => ({
    index,
    routes,
  }), [index, routes]);

  const jumpTo = React.useCallback((key: string) => {
    const routeIndex = routes.findIndex(route => route.key === key);
    if (routeIndex !== -1) {
      setIndex(routeIndex);
    }
  }, [routes]);

  return {
    navigationState,
    onIndexChange: setIndex,
    jumpTo,
    currentRoute: routes[index],
  };
}

// Common bottom navigation configurations
export const commonBottomNavigationRoutes = {
  main: [
    {
      key: 'home',
      title: 'خانه',
      focusedIcon: 'home',
      unfocusedIcon: 'home-outline',
      accessibilityLabel: 'صفحه اصلی',
    },
    {
      key: 'explore',
      title: 'کاوش',
      focusedIcon: 'compass',
      unfocusedIcon: 'compass-outline',
      accessibilityLabel: 'کاوش محتوا',
    },
    {
      key: 'exams',
      title: 'آزمون‌ها',
      focusedIcon: 'clipboard-text',
      unfocusedIcon: 'clipboard-text-outline',
      accessibilityLabel: 'آزمون‌ها',
    },
    {
      key: 'profile',
      title: 'پروفایل',
      focusedIcon: 'account',
      unfocusedIcon: 'account-outline',
      accessibilityLabel: 'پروفایل کاربری',
    },
  ] as PaperBottomNavigationRoute[],
  
  admin: [
    {
      key: 'dashboard',
      title: 'داشبورد',
      focusedIcon: 'view-dashboard',
      unfocusedIcon: 'view-dashboard-outline',
      accessibilityLabel: 'داشبورد مدیریت',
    },
    {
      key: 'users',
      title: 'کاربران',
      focusedIcon: 'account-group',
      unfocusedIcon: 'account-group-outline',
      accessibilityLabel: 'مدیریت کاربران',
    },
    {
      key: 'content',
      title: 'محتوا',
      focusedIcon: 'book',
      unfocusedIcon: 'book-outline',
      accessibilityLabel: 'مدیریت محتوا',
    },
    {
      key: 'settings',
      title: 'تنظیمات',
      focusedIcon: 'cog',
      unfocusedIcon: 'cog-outline',
      accessibilityLabel: 'تنظیمات سیستم',
    },
  ] as PaperBottomNavigationRoute[],
};

// RTL-aware bottom navigation
export function PaperRTLBottomNavigation(props: PaperBottomNavigationProps) {
  return (
    <PaperBottomNavigationComponent
      {...props}
      barStyle={[styles.rtlBottomNavigation, props.barStyle]}
    />
  );
}

const styles = StyleSheet.create({
  bottomNavigation: {
    // Custom styling for bottom navigation
  },
  rtlBottomNavigation: {
    // RTL-specific styling
    flexDirection: 'row-reverse',
  },
});

// Export as default for easier importing
export default PaperBottomNavigationComponent;
