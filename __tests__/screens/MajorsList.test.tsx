import React from 'react';
import { render, fireEvent, waitFor } from '@testing-library/react-native';
import MajorsListScreen from '../../app/screens/MajorsList';
import { majorService } from '../../services/majorService';

// Mock the major service
jest.mock('../../services/majorService', () => ({
  majorService: {
    getMajors: jest.fn(),
  },
}));

const mockMajors = [
  {
    id: '1',
    name: 'Computer Science',
    createdAt: '2023-01-01T00:00:00Z',
  },
  {
    id: '2',
    name: 'Mathematics',
    createdAt: '2023-01-02T00:00:00Z',
  },
];

describe('MajorsListScreen', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    (majorService.getMajors as jest.Mock).mockResolvedValue(mockMajors);
  });

  it('renders correctly with majors', async () => {
    const { getByText } = render(<MajorsListScreen />);
    
    await waitFor(() => {
      expect(getByText('Majors')).toBeTruthy();
      expect(getByText('Computer Science')).toBeTruthy();
      expect(getByText('Mathematics')).toBeTruthy();
    });
  });

  it('shows loading state initially', () => {
    const { getByText } = render(<MajorsListScreen />);
    expect(getByText('Loading majors...')).toBeTruthy();
  });

  it('handles pull-to-refresh functionality with ScrollView', async () => {
    const { getByTestId } = render(<MajorsListScreen />);
    
    // Wait for initial load
    await waitFor(() => {
      expect(majorService.getMajors).toHaveBeenCalledTimes(1);
    });

    // Find the ScrollView and trigger refresh
    const scrollView = getByTestId('majors-scrollview');
    
    // Simulate pull-to-refresh on ScrollView
    fireEvent(scrollView, 'refresh');
    
    await waitFor(() => {
      expect(majorService.getMajors).toHaveBeenCalledTimes(2);
    });
  });

  it('displays error message when fetch fails', async () => {
    (majorService.getMajors as jest.Mock).mockRejectedValue(new Error('Network error'));
    
    const { getByText } = render(<MajorsListScreen />);
    
    await waitFor(() => {
      expect(getByText('Failed to fetch majors.')).toBeTruthy();
    });
  });

  it('shows no data message when majors list is empty', async () => {
    (majorService.getMajors as jest.Mock).mockResolvedValue([]);
    
    const { getByText } = render(<MajorsListScreen />);
    
    await waitFor(() => {
      expect(getByText('No majors found.')).toBeTruthy();
    });
  });

  it('handles refresh error gracefully', async () => {
    const { getByTestId } = render(<MajorsListScreen />);
    
    // Wait for initial load
    await waitFor(() => {
      expect(majorService.getMajors).toHaveBeenCalledTimes(1);
    });

    // Mock refresh to fail
    (majorService.getMajors as jest.Mock).mockRejectedValueOnce(new Error('Refresh failed'));

    // Find the ScrollView and trigger refresh
    const scrollView = getByTestId('majors-scrollview');
    fireEvent(scrollView, 'refresh');
    
    await waitFor(() => {
      expect(getByText('Failed to refresh majors.')).toBeTruthy();
    });
  });

  it('calls loadMajors on component mount', async () => {
    render(<MajorsListScreen />);
    
    await waitFor(() => {
      expect(majorService.getMajors).toHaveBeenCalledTimes(1);
    });
  });

  it('renders add new major button', async () => {
    const { getByText } = render(<MajorsListScreen />);
    
    await waitFor(() => {
      expect(getByText('Add New Major')).toBeTruthy();
    });
  });

  it('renders view details buttons for each major', async () => {
    const { getAllByText } = render(<MajorsListScreen />);
    
    await waitFor(() => {
      const viewButtons = getAllByText('View Details');
      expect(viewButtons).toHaveLength(2); // One for each major
    });
  });

  it('refreshControl has correct color', async () => {
    const { getByTestId } = render(<MajorsListScreen />);
    
    await waitFor(() => {
      const scrollView = getByTestId('majors-scrollview');
      expect(scrollView.props.refreshControl.props.tintColor).toBe('#007bff');
    });
  });
});
