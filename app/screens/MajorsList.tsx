import React, { useEffect, useState } from "react";
import { StyleSheet, SafeAreaView } from "react-native";
import { Surface } from "react-native-paper";
import { Major, majorService } from "@/services/majorService";
import { router } from "expo-router";
import {
  PaperListAppBar,
  PaperRefreshableFlatList,
  PaperCard,
  PaperCardTitle,
  PaperCardActions,
  PaperButton,
  PaperTitle,
  PaperBody,
  PaperAddFAB,
  PaperDeleteDialog,
  useRefresh,
} from "@/components/paper";
import { t } from "@/constants/Localization";
import { useApiError } from "@/hooks/useApiError";

export default function MajorsListScreen() {
  const [majors, setMajors] = useState<Major[]>([]);
  const [loading, setLoading] = useState(true);
  const [deleteDialogVisible, setDeleteDialogVisible] = useState(false);
  const [majorToDelete, setMajorToDelete] = useState<Major | null>(null);
  const { showError } = useApiError();

  const loadMajors = async () => {
    try {
      setLoading(true);
      const data = await majorService.getMajors();
      setMajors(data);
    } catch (err) {
      showError(err, t('errorLoadingMajors') || "خطا در دریافت رشته‌ها");
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  const { refreshing, onRefresh } = useRefresh(loadMajors);

  useEffect(() => {
    loadMajors();
  }, []);

  // Navigation handlers
  const handleAddMajor = () => {
    router.push('/screens/MajorAdd');
  };

  const handleMajorPress = (major: Major) => {
    router.push(`/screens/MajorDetails?id=${major.id}`);
  };

  const handleEditMajor = (major: Major) => {
    router.push(`/screens/MajorEdit?id=${major.id}`);
  };

  const handleDeleteMajor = (major: Major) => {
    setMajorToDelete(major);
    setDeleteDialogVisible(true);
  };

  const confirmDeleteMajor = async () => {
    if (!majorToDelete) return;

    try {
      await majorService.deleteMajor(majorToDelete.id);
      setDeleteDialogVisible(false);
      setMajorToDelete(null);
      loadMajors(); // Refresh the list
    } catch (error) {
      showError(error, t('errorDeletingMajor') || 'خطا در حذف رشته');
      console.error('Failed to delete major:', error);
    }
  };

  const renderMajorItem = ({ item }: { item: Major }) => (
    <PaperCard
      style={styles.majorCard}
      onPress={() => handleMajorPress(item)}
    >
      <PaperCardTitle
        title={item.name}
        subtitle={item.description || t('majorDescription')}
        left={(props) => (
          <Surface {...props} style={styles.majorIcon}>
            <PaperTitle size="small">{item.name.charAt(0)}</PaperTitle>
          </Surface>
        )}
      />
      <PaperCardActions>
        <PaperButton
          title={t('edit')}
          mode="outlined"
          onPress={() => handleEditMajor(item)}
          icon="pencil"
          compact
        />
        <PaperButton
          title={t('delete')}
          mode="outlined"
          onPress={() => handleDeleteMajor(item)}
          icon="delete"
          compact
          style={styles.deleteButton}
        />
      </PaperCardActions>
    </PaperCard>
  );

  return (
    <SafeAreaView style={styles.container}>
      {/* App Bar */}
      <PaperListAppBar
        title={t('majorsList') || 'لیست رشته‌ها'}
        onAddPress={handleAddMajor}
      />

      {/* Content */}
      <Surface style={styles.content}>
        <PaperRefreshableFlatList
          data={majors}
          renderItem={renderMajorItem}
          keyExtractor={(item) => item.id}
          refreshing={refreshing}
          onRefresh={onRefresh}
          loading={loading}
          style={styles.list}
          contentContainerStyle={styles.listContent}
          emptyTitle={t('noMajorsFound') || 'رشته‌ای یافت نشد'}
          emptyDescription={t('noMajorsDescription') || 'برای افزودن رشته جدید از دکمه + استفاده کنید'}
          emptyIcon="school"
          emptyAction={
            <PaperButton
              title={t('addMajor') || 'افزودن رشته'}
              mode="contained"
              onPress={handleAddMajor}
              icon="plus"
            />
          }
        />
      </Surface>

      {/* Floating Action Button */}
      <PaperAddFAB
        onPress={handleAddMajor}
        visible={!loading}
      />

      {/* Delete Confirmation Dialog */}
      <PaperDeleteDialog
        visible={deleteDialogVisible}
        onDismiss={() => setDeleteDialogVisible(false)}
        onConfirm={confirmDeleteMajor}
        title={t('deleteMajor') || 'حذف رشته'}
        content={
          majorToDelete
            ? `${t('confirmDeleteMajor') || 'آیا از حذف رشته'} "${majorToDelete.name}" ${t('areYouSure') || 'مطمئن هستید؟'}`
            : ''
        }
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
  },
  list: {
    flex: 1,
  },
  listContent: {
    padding: 16,
  },
  majorCard: {
    marginVertical: 8,
  },
  majorIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#e3f2fd',
  },
  deleteButton: {
    borderColor: '#f44336',
  },
});
