import { api } from './api';
import { Option } from './optionService';
import { Question } from './questionService';

// Define types
export interface Answer {
  id: string;
  session_id: number;
  question: Question;
  selected_option: Option;
  selected_option_id: number;
  is_correct: boolean;
  created_at?: string;
}

export interface CreateAnswerDto {
  session_id: number;
  question_id: number;
  selected_option_id: number;
  is_correct: boolean;
}

// Answer service for handling answer-related API calls
export const answerService = {
  // Submit an answer to a question in a session
  async submitAnswer(answerData: CreateAnswerDto): Promise<Answer> {
    const response = await api.post<Answer>('/v1/api/answers', answerData);
    return response.data;
  },

  // Get all answers
  async getAllAnswers(): Promise<Answer[]> {
    const response = await api.get<Answer[]>('/v1/api/answers');
    return response.data;
  },

  // Get an answer by ID
  async getAnswerById(id: string): Promise<Answer> {
    const response = await api.get<Answer>(`/v1/api/answers/${id}`);
    return response.data;
  }
};
