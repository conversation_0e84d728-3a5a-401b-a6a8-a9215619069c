import { Image } from 'expo-image';
import { Platform, StyleSheet } from 'react-native';

import { Collapsible } from '@/components/Collapsible';
import { ExternalLink } from '@/components/ExternalLink';
import ParallaxScrollView from '@/components/ParallaxScrollView';
import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { IconSymbol } from '@/components/ui/IconSymbol';

export default function ExploreScreen() {
  return (
    <ParallaxScrollView
      headerBackgroundColor={{ light: '#D0D0D0', dark: '#353636' }}
      headerImage={
        <IconSymbol
          size={310}
          color="#808080"
          name="chevron.left.forwardslash.chevron.right"
          style={styles.headerImage}
        />
      }>
      <ThemedView style={styles.titleContainer}>
        <ThemedText type="title"></ThemedText>
      </ThemedView>
      {/* <ThemedText>این برنامه شامل کدهای نمونه برای شروع کار شماست.</ThemedText> */}
      {/* <Collapsible title="کامپوننت‌های حالت روشن و تاریک"> */}
        {/* <ThemedText>
          این قالب از حالت روشن و تاریک پشتیبانی می‌کند. هوک{' '}
          <ThemedText type="defaultSemiBold">useColorScheme()</ThemedText> به شما امکان بررسی
          طرح رنگی فعلی کاربر را می‌دهد و بنابراین می‌توانید رنگ‌های رابط کاربری را متناسب تنظیم کنید.
        </ThemedText> */}
        {/* <ExternalLink href="https://docs.expo.dev/develop/user-interface/color-themes/">
          <ThemedText type="link">بیشتر بدانید</ThemedText>
        </ExternalLink> */}
      {/* </Collapsible> */}
    </ParallaxScrollView>
  );
}

const styles = StyleSheet.create({
  headerImage: {
    color: '#808080',
    bottom: -90,
    left: -35,
    position: 'absolute',
  },
  titleContainer: {
    flexDirection: 'row',
    gap: 8,
  },
});
