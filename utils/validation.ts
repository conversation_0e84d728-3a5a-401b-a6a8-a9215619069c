/**
 * Validation utilities for form inputs
 */

export interface ValidationResult {
  isValid: boolean;
  error?: string;
}

export interface MajorValidation {
  name: ValidationResult;
  description: ValidationResult;
  isFormValid: boolean;
}

/**
 * Validate major name
 */
export const validateMajorName = (name: string): ValidationResult => {
  const trimmedName = name.trim();
  
  if (!trimmedName) {
    return {
      isValid: false,
      error: "نام رشته الزامی است"
    };
  }
  
  if (trimmedName.length < 2) {
    return {
      isValid: false,
      error: "نام رشته باید حداقل ۲ کاراکتر باشد"
    };
  }
  
  if (trimmedName.length > 100) {
    return {
      isValid: false,
      error: "نام رشته نباید بیش از ۱۰۰ کاراکتر باشد"
    };
  }
  
  // Check for valid characters (Persian, Arabic, English letters, numbers, spaces, and common punctuation)
  const validNamePattern = /^[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFFa-zA-Z0-9\s\-_().]+$/;
  if (!validNamePattern.test(trimmedName)) {
    return {
      isValid: false,
      error: "نام رشته شامل کاراکترهای غیرمجاز است"
    };
  }
  
  return {
    isValid: true
  };
};

/**
 * Validate major description
 */
export const validateMajorDescription = (description: string): ValidationResult => {
  const trimmedDescription = description.trim();
  
  // Description is optional, so empty is valid
  if (!trimmedDescription) {
    return {
      isValid: true
    };
  }
  
  if (trimmedDescription.length > 500) {
    return {
      isValid: false,
      error: "توضیحات نباید بیش از ۵۰۰ کاراکتر باشد"
    };
  }
  
  return {
    isValid: true
  };
};

/**
 * Validate entire major form
 */
export const validateMajorForm = (name: string, description: string): MajorValidation => {
  const nameValidation = validateMajorName(name);
  const descriptionValidation = validateMajorDescription(description);
  
  return {
    name: nameValidation,
    description: descriptionValidation,
    isFormValid: nameValidation.isValid && descriptionValidation.isValid
  };
};

/**
 * Sanitize input text
 */
export const sanitizeInput = (input: string): string => {
  return input.trim().replace(/\s+/g, ' '); // Remove extra whitespace
};

/**
 * Check if text contains only whitespace
 */
export const isOnlyWhitespace = (text: string): boolean => {
  return !text.trim();
};

/**
 * Get character count for display
 */
export const getCharacterCount = (text: string): number => {
  return text.length;
};

/**
 * Format validation error message for display
 */
export const formatValidationError = (error?: string): string => {
  return error || "";
};
