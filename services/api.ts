import Constants from 'expo-constants';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { handleError, logError, getErrorActionMessage, ErrorInfo } from '@/utils/errorHandler';
import { withRetry, RetryPresets, RetryConfig } from '@/utils/retryMechanism';

// Define base types for API responses
interface ApiResponse<T> {
  data: T;
  status: number;
  message?: string;
}

// Error class for API errors
export class ApiError extends Error {
  status: number;
  data?: any;

  constructor(message: string, status: number, data?: any) {
    super(message);
    this.name = 'ApiError';
    this.status = status;
    this.data = data;
  }
}

class ApiService {
  private baseUrl: string;
  private defaultHeaders: Record<string, string>;
  private readonly API_URL_STORAGE_KEY = 'app_api_url';
  private readonly DEFAULT_API_URL = 'http://192.168.166.95:4000';
  private defaultRetryConfig: Partial<RetryConfig> = RetryPresets.STANDARD;

  constructor() {
    // Initialize with default, will be updated in init()
    this.baseUrl = this.DEFAULT_API_URL;
    this.defaultHeaders = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    };
    
    // Initialize the API URL
    this.init();
  }

  // Initialize API URL from storage or environment
  private async init() {
    try {
      // Try to get from AsyncStorage first
      const storedUrl = await AsyncStorage.getItem(this.API_URL_STORAGE_KEY);
      
      if (storedUrl) {
        this.baseUrl = storedUrl;
      } else {
        // Fall back to environment variable or default
        this.baseUrl = Constants.expoConfig?.extra?.apiUrl || this.DEFAULT_API_URL;
        // Save the default to storage for future use
        await this.setApiUrl(this.baseUrl);
      }
    } catch (error) {
      console.error('Failed to initialize API URL:', error);
      // Fall back to default if there's an error
      this.baseUrl = this.DEFAULT_API_URL;
    }
  }

  // Method to update the API URL
  async setApiUrl(url: string): Promise<void> {
    try {
      await AsyncStorage.setItem(this.API_URL_STORAGE_KEY, url);
      this.baseUrl = url;
    } catch (error) {
      console.error('Failed to save API URL:', error);
      throw new Error('Failed to save API URL');
    }
  }

  // Get the current API URL
  getApiUrl(): string {
    return this.baseUrl;
  }

  // Add auth token to requests
  setAuthToken(token: string | null) {
    if (token) {
      this.defaultHeaders['Authorization'] = `Bearer ${token}`;
    } else {
      delete this.defaultHeaders['Authorization'];
    }
  }

  // Set default retry configuration
  setRetryConfig(config: Partial<RetryConfig>) {
    this.defaultRetryConfig = { ...this.defaultRetryConfig, ...config };
  }

  // Get current retry configuration
  getRetryConfig(): Partial<RetryConfig> {
    return this.defaultRetryConfig;
  }

  // Generic request method
  private async request<T>(
    endpoint: string,
    method: string,
    data?: any,
    customHeaders?: Record<string, string>,
    retryConfig?: Partial<RetryConfig>
  ): Promise<ApiResponse<T>> {
    const finalRetryConfig = { ...this.defaultRetryConfig, ...retryConfig };

    // Create the actual request function
    const makeRequest = async (): Promise<ApiResponse<T>> => {
      const url = `${this.baseUrl}${endpoint}`;

      const headers = {
        ...this.defaultHeaders,
        ...customHeaders,
      };

      const config: RequestInit = {
        method,
        headers,
        body: data ? JSON.stringify(data) : undefined,
      };

      try {
        const response = await fetch(url, config);
        let responseData;

        // Try to parse JSON response
        try {
          responseData = await response.json();
        } catch (parseError) {
          // If JSON parsing fails, use response text
          responseData = await response.text();
        }

        if (!response.ok) {
          throw new ApiError(
            responseData?.message || responseData || 'An error occurred',
            response.status,
            responseData
          );
        }

        return {
          data: responseData,
          status: response.status,
          message: responseData?.message,
        };
      } catch (error) {
        // Handle and log the error
        const errorInfo = handleError(error);
        logError(errorInfo, `${method} ${endpoint}`);

        if (error instanceof ApiError) {
          throw error;
        }

        throw new ApiError(
          error instanceof Error ? error.message : 'Network error',
          500
        );
      }
    };

    // Execute with retry if retry config is provided
    if (finalRetryConfig && Object.keys(finalRetryConfig).length > 0) {
      const result = await withRetry(makeRequest, finalRetryConfig);

      if (result.success) {
        return result.data!;
      } else {
        throw result.error;
      }
    } else {
      return await makeRequest();
    }
  }

  // HTTP method wrappers
  async get<T>(endpoint: string, customHeaders?: Record<string, string>, retryConfig?: Partial<RetryConfig>): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, 'GET', undefined, customHeaders, retryConfig);
  }

  async post<T>(endpoint: string, data?: any, customHeaders?: Record<string, string>, retryConfig?: Partial<RetryConfig>): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, 'POST', data, customHeaders, retryConfig);
  }

  async put<T>(endpoint: string, data?: any, customHeaders?: Record<string, string>, retryConfig?: Partial<RetryConfig>): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, 'PUT', data, customHeaders, retryConfig);
  }

  async patch<T>(endpoint: string, data?: any, customHeaders?: Record<string, string>, retryConfig?: Partial<RetryConfig>): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, 'PATCH', data, customHeaders, retryConfig);
  }

  async delete<T>(endpoint: string, customHeaders?: Record<string, string>, retryConfig?: Partial<RetryConfig>): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, 'DELETE', undefined, customHeaders, retryConfig);
  }

  // Convenience methods with specific retry configurations
  async getWithRetry<T>(endpoint: string, customHeaders?: Record<string, string>): Promise<ApiResponse<T>> {
    return this.get<T>(endpoint, customHeaders, RetryPresets.STANDARD);
  }

  async postWithRetry<T>(endpoint: string, data?: any, customHeaders?: Record<string, string>): Promise<ApiResponse<T>> {
    return this.post<T>(endpoint, data, customHeaders, RetryPresets.STANDARD);
  }

  async putWithRetry<T>(endpoint: string, data?: any, customHeaders?: Record<string, string>): Promise<ApiResponse<T>> {
    return this.put<T>(endpoint, data, customHeaders, RetryPresets.STANDARD);
  }

  async patchWithRetry<T>(endpoint: string, data?: any, customHeaders?: Record<string, string>): Promise<ApiResponse<T>> {
    return this.patch<T>(endpoint, data, customHeaders, RetryPresets.STANDARD);
  }

  async deleteWithRetry<T>(endpoint: string, customHeaders?: Record<string, string>): Promise<ApiResponse<T>> {
    return this.delete<T>(endpoint, customHeaders, RetryPresets.STANDARD);
  }
}

// Export a singleton instance
export const api = new ApiService();
