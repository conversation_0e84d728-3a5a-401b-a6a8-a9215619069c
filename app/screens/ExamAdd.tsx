import React, { useState, useEffect } from 'react';
import { StyleSheet, TextInput, Alert, Text, View } from 'react-native';
import { ThemedText } from '@/components/ThemedText';
import { Button } from '@/components/Button';
import { ThemedView } from '@/components/ThemedView';
import { examService } from '@/services/examService';
import { majorService, Major } from '@/services/majorService';
import { questionService, Question } from '@/services/questionService';
import KeyboardAvoidingWrapper from '@/components/KeyboardAvoidingWrapper';
import { router } from 'expo-router';
import { Picker } from '@react-native-picker/picker';
import CheckBox from 'expo-checkbox';

export default function ExamAddScreen() {
  const [name, setName] = useState('');
  const [durationMinutes, setDurationMinutes] = useState('');
  const [instructions, setInstructions] = useState('');
  const [majorId, setMajorId] = useState<number | undefined>(undefined);
  const [selectedQuestions, setSelectedQuestions] = useState<string[]>([]);
  const [majors, setMajors] = useState<Major[]>([]);
  const [questions, setQuestions] = useState<Question[]>([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    const fetchMajorsAndQuestions = async () => {
      try {
        const majorsData = await majorService.getMajors();
        setMajors(majorsData);
        const questionsData = await questionService.getQuestions();
        setQuestions(questionsData);
      } catch (error) {
        console.error('Failed to fetch majors or questions:', error);
      }
    };
    fetchMajorsAndQuestions();
  }, []);

  const handleAddExam = async () => {
    if (!name.trim()) {
      Alert.alert('Validation Error', 'Exam name cannot be empty.');
      return;
    }
    if (!durationMinutes.trim() || isNaN(Number(durationMinutes))) {
      Alert.alert('Validation Error', 'Duration must be a valid number.');
      return;
    }
    if (!instructions.trim()) {
      Alert.alert('Validation Error', 'Instructions cannot be empty.');
      return;
    }
    if (majorId === undefined) { // Changed from !majorId to majorId === undefined for clarity with number type
      Alert.alert('Validation Error', 'Please select a major.');
      return;
    }
    if (selectedQuestions.length === 0) {
      Alert.alert('Validation Error', 'Please select at least one question.');
      return;
    }

    setLoading(true);
    try {
      await examService.createExam({
        name,
        duration_minutes: Number(durationMinutes),
        instructions,
        majorId: Number(majorId), // Ensure majorId is a number
        questionIds: selectedQuestions,
      });
      Alert.alert('Success', 'Exam added successfully!');
      router.back();
    } catch (error) {
      Alert.alert('Error', 'Failed to add exam. Please try again.');
      console.error('Failed to add exam:', error);
    } finally {
      setLoading(false);
    }
  };

  const toggleQuestionSelection = (questionId: string) => {
    setSelectedQuestions((prevSelected) =>
      prevSelected.includes(questionId)
        ? prevSelected.filter((id) => id !== questionId)
        : [...prevSelected, questionId]
    );
  };

  return (
    <KeyboardAvoidingWrapper style={styles.container}>
      <ThemedText style={styles.title}>ایجاد آزمون جدید</ThemedText>
      <TextInput
        style={styles.input}
        placeholder="عنوان آزمون"
        value={name}
        onChangeText={setName}
        editable={!loading}
      />
      <TextInput
        style={styles.input}
        placeholder="مدت آزمون (دقیقه)"
        value={durationMinutes}
        onChangeText={setDurationMinutes}
        editable={!loading}
        keyboardType="numeric"
      />
      <TextInput
        style={styles.input}
        placeholder="توضیحات/ دستورالعمل"
        value={instructions}
        onChangeText={setInstructions}
        editable={!loading}
        multiline
      />
      <Picker
        selectedValue={majorId}
        onValueChange={(itemValue: string | number | undefined) => setMajorId(itemValue ? Number(itemValue) : undefined)}
        style={styles.picker}
      >
        <Picker.Item label="انتخاب رشته تحصیلی" value={undefined} />
        {majors.map((major) => (
          <Picker.Item key={major.id} label={major.name} value={Number(major.id)} />
        ))}
      </Picker>
      <ThemedText style={styles.subtitle}>انتخاب سوال‌ها</ThemedText>
      {questions.map((question) => (
        <View key={question.id} style={styles.checkboxContainer}>
          <CheckBox
            value={selectedQuestions.includes(question.id)}
            onValueChange={() => toggleQuestionSelection(question.id)}
          />
          <Text style={styles.label}>{question.question_text}</Text>
        </View>
      ))}
      <Button
        title={loading ? 'Adding...' : 'افزودن آزمون'}
        onPress={handleAddExam}
        disabled={loading}
        style={[styles.button, loading && styles.buttonDisabled]}
        textStyle={styles.buttonText}
        mode="contained"
      />
    </KeyboardAvoidingWrapper>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
    justifyContent: 'center',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 20,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginTop: 20,
    marginBottom: 10,
  },
  input: {
    height: 40,
    borderColor: '#ccc',
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 10,
    marginBottom: 15,
    backgroundColor: '#fff',
  },
  picker: {
    height: 50,
    width: '100%',
    marginBottom: 15,
  },
  checkboxContainer: {
    flexDirection: 'row',
    marginBottom: 12,
    alignItems: 'center',
  },
  label: {
    margin: 8,
  },
  button: {
    marginTop: 20,
  },
  buttonText: {
    // The custom Button component handles color, fontWeight, and fontSize based on its theme and mode.
    // We can keep this if we want to override, but for now, let's rely on the Button component's defaults.
  },
  buttonDisabled: {
    // The custom Button component handles disabled state styling.
  },
});
