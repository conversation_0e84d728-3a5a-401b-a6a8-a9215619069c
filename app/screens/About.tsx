import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  ScrollView,
  TouchableOpacity,
  Linking,
} from 'react-native';
import { router } from 'expo-router';
import { Svg, Path, Circle } from 'react-native-svg';

import { rtlStyle } from '@/utils/rtl';
import { useColorScheme } from '@/hooks/useColorScheme';
import { Colors } from '@/constants/Colors';
import { ThemedText } from '@/components/ThemedText';

// Icon Components
const BackArrowIcon = ({ color = "#1F2937", size = 24 }) => (
  <Svg width={size} height={size} viewBox="0 0 24 24" fill="none" stroke={color} strokeWidth="2">
    <Path d="M15 18l-6-6 6-6" />
  </Svg>
);

const MenuIcon = ({ color = "#1F2937", size = 24 }) => (
  <Svg width={size} height={size} viewBox="0 0 24 24" fill="none" stroke={color} strokeWidth="2">
    <Path d="M3 12h18M3 6h18M3 18h18" />
  </Svg>
);

const HeartIcon = ({ color = "#EF4444", size = 20 }) => (
  <Svg width={size} height={size} viewBox="0 0 24 24" fill={color} stroke={color} strokeWidth="2">
    <Path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z" />
  </Svg>
);

const ExternalLinkIcon = ({ color = "#6B7280", size = 20 }) => (
  <Svg width={size} height={size} viewBox="0 0 24 24" fill="none" stroke={color} strokeWidth="2">
    <Path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6" />
    <Path d="M15 3h6v6" />
    <Path d="M10 14L21 3" />
  </Svg>
);

const MailIcon = ({ color = "#6B7280", size = 20 }) => (
  <Svg width={size} height={size} viewBox="0 0 24 24" fill="none" stroke={color} strokeWidth="2">
    <Path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z" />
    <Path d="M22 6l-10 7L2 6" />
  </Svg>
);

export default function AboutScreen() {
  const colorScheme = useColorScheme() ?? 'light';

  const handleBack = () => {
    router.back();
  };

  const openDrawer = () => {
    router.push('/(drawer)/(tabs)');
  };

  const openLink = (url: string) => {
    Linking.openURL(url);
  };

  const sendEmail = () => {
    Linking.openURL('mailto:<EMAIL>');
  };

  const InfoItem = ({ 
    title, 
    value, 
    onPress 
  }: {
    title: string;
    value: string;
    onPress?: () => void;
  }) => (
    <TouchableOpacity 
      style={[styles.infoItem, { borderBottomColor: Colors[colorScheme].tabIconDefault + '30' }]}
      onPress={onPress}
      disabled={!onPress}
    >
      <ThemedText style={[styles.infoTitle, { color: Colors[colorScheme].text }]}>
        {title}
      </ThemedText>
      <View style={styles.infoValueContainer}>
        <ThemedText style={[styles.infoValue, { color: Colors[colorScheme].tabIconDefault }]}>
          {value}
        </ThemedText>
        {onPress && <ExternalLinkIcon color={Colors[colorScheme].tabIconDefault} />}
      </View>
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: Colors[colorScheme].background }]}>
      {/* Header */}
      <View style={[styles.header, { borderBottomColor: Colors[colorScheme].tabIconDefault + '30' }]}>
        <TouchableOpacity style={styles.headerButton} onPress={handleBack}>
          <BackArrowIcon color={Colors[colorScheme].text} />
        </TouchableOpacity>
        <ThemedText style={[styles.headerTitle, { color: Colors[colorScheme].text }]}>
          درباره ما
        </ThemedText>
        <TouchableOpacity style={styles.headerButton} onPress={openDrawer}>
          <MenuIcon color={Colors[colorScheme].text} />
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* App Logo/Icon */}
        <View style={styles.logoSection}>
          <View style={[styles.logoContainer, { backgroundColor: Colors[colorScheme].tint }]}>
            <ThemedText style={styles.logoText}>آزمون</ThemedText>
          </View>
          <ThemedText style={[styles.appName, { color: Colors[colorScheme].text }]}>
            سامانه مدیریت آزمون
          </ThemedText>
          <ThemedText style={[styles.version, { color: Colors[colorScheme].tabIconDefault }]}>
            نسخه ۱.۰.۰
          </ThemedText>
        </View>

        {/* Description */}
        <View style={styles.descriptionSection}>
          <ThemedText style={[styles.description, { color: Colors[colorScheme].text }]}>
            سامانه مدیریت آزمون یک برنامه جامع برای مدیریت و برگزاری آزمون‌های آنلاین است. 
            این برنامه امکانات کاملی برای ایجاد، ویرایش و مدیریت آزمون‌ها، رشته‌های تحصیلی 
            و نتایج آزمون‌ها را فراهم می‌کند.
          </ThemedText>
        </View>

        {/* Features */}
        <View style={styles.featuresSection}>
          <ThemedText style={[styles.sectionTitle, { color: Colors[colorScheme].text }]}>
            ویژگی‌ها
          </ThemedText>
          
          <View style={styles.featuresList}>
            <ThemedText style={[styles.featureItem, { color: Colors[colorScheme].tabIconDefault }]}>
              • مدیریت رشته‌های تحصیلی
            </ThemedText>
            <ThemedText style={[styles.featureItem, { color: Colors[colorScheme].tabIconDefault }]}>
              • ایجاد و ویرایش آزمون‌ها
            </ThemedText>
            <ThemedText style={[styles.featureItem, { color: Colors[colorScheme].tabIconDefault }]}>
              • پشتیبانی کامل از زبان فارسی و RTL
            </ThemedText>
            <ThemedText style={[styles.featureItem, { color: Colors[colorScheme].tabIconDefault }]}>
              • رابط کاربری مدرن و کاربرپسند
            </ThemedText>
            <ThemedText style={[styles.featureItem, { color: Colors[colorScheme].tabIconDefault }]}>
              • امکان تنظیمات شخصی‌سازی
            </ThemedText>
          </View>
        </View>

        {/* App Information */}
        <View style={styles.infoSection}>
          <ThemedText style={[styles.sectionTitle, { color: Colors[colorScheme].text }]}>
            اطلاعات برنامه
          </ThemedText>
          
          <InfoItem title="نسخه" value="۱.۰.۰" />
          <InfoItem title="تاریخ انتشار" value="۱۴۰۳/۱۰/۱۵" />
          <InfoItem title="سازنده" value="تیم توسعه آزمون" />
          <InfoItem title="پلتفرم" value="React Native / Expo" />
        </View>

        {/* Contact */}
        <View style={styles.contactSection}>
          <ThemedText style={[styles.sectionTitle, { color: Colors[colorScheme].text }]}>
            تماس با ما
          </ThemedText>
          
          <TouchableOpacity style={styles.contactItem} onPress={sendEmail}>
            <MailIcon color={Colors[colorScheme].tint} />
            <ThemedText style={[styles.contactText, { color: Colors[colorScheme].tint }]}>
              <EMAIL>
            </ThemedText>
          </TouchableOpacity>
          
          <TouchableOpacity 
            style={styles.contactItem}
            onPress={() => openLink('https://azmoon.com')}
          >
            <ExternalLinkIcon color={Colors[colorScheme].tint} />
            <ThemedText style={[styles.contactText, { color: Colors[colorScheme].tint }]}>
              www.azmoon.com
            </ThemedText>
          </TouchableOpacity>
        </View>

        {/* Footer */}
        <View style={styles.footer}>
          <View style={styles.footerContent}>
            <ThemedText style={[styles.footerText, { color: Colors[colorScheme].tabIconDefault }]}>
              ساخته شده با
            </ThemedText>
            <HeartIcon />
            <ThemedText style={[styles.footerText, { color: Colors[colorScheme].tabIconDefault }]}>
              در ایران
            </ThemedText>
          </View>
          <ThemedText style={[styles.copyright, { color: Colors[colorScheme].tabIconDefault }]}>
            © ۱۴۰۳ تمامی حقوق محفوظ است
          </ThemedText>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: rtlStyle.flexDirection.row,
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
  },
  headerButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    fontFamily: 'Vazirmatn',
    textAlign: 'center',
  },
  content: {
    flex: 1,
  },
  logoSection: {
    alignItems: 'center',
    paddingVertical: 40,
  },
  logoContainer: {
    width: 80,
    height: 80,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
  },
  logoText: {
    color: '#FFFFFF',
    fontSize: 24,
    fontWeight: 'bold',
    fontFamily: 'Vazirmatn',
  },
  appName: {
    fontSize: 24,
    fontWeight: 'bold',
    fontFamily: 'Vazirmatn',
    textAlign: 'center',
    marginBottom: 8,
  },
  version: {
    fontSize: 16,
    fontFamily: 'Vazirmatn',
  },
  descriptionSection: {
    paddingHorizontal: 16,
    marginBottom: 32,
  },
  description: {
    fontSize: 16,
    lineHeight: 24,
    fontFamily: 'Vazirmatn',
    textAlign: rtlStyle.textAlign.start,
  },
  featuresSection: {
    paddingHorizontal: 16,
    marginBottom: 32,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    fontFamily: 'Vazirmatn',
    marginBottom: 16,
    textAlign: rtlStyle.textAlign.start,
  },
  featuresList: {
    paddingLeft: 16,
  },
  featureItem: {
    fontSize: 16,
    lineHeight: 24,
    fontFamily: 'Vazirmatn',
    marginBottom: 8,
    textAlign: rtlStyle.textAlign.start,
  },
  infoSection: {
    paddingHorizontal: 16,
    marginBottom: 32,
  },
  infoItem: {
    flexDirection: rtlStyle.flexDirection.row,
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 16,
    borderBottomWidth: 1,
  },
  infoTitle: {
    fontSize: 16,
    fontFamily: 'Vazirmatn',
  },
  infoValueContainer: {
    flexDirection: rtlStyle.flexDirection.row,
    alignItems: 'center',
  },
  infoValue: {
    fontSize: 16,
    fontFamily: 'Vazirmatn',
    ...rtlStyle.marginLeft(8),
  },
  contactSection: {
    paddingHorizontal: 16,
    marginBottom: 32,
  },
  contactItem: {
    flexDirection: rtlStyle.flexDirection.row,
    alignItems: 'center',
    paddingVertical: 12,
  },
  contactText: {
    fontSize: 16,
    fontFamily: 'Vazirmatn',
    ...rtlStyle.marginLeft(12),
  },
  footer: {
    paddingHorizontal: 16,
    paddingVertical: 32,
    alignItems: 'center',
  },
  footerContent: {
    flexDirection: rtlStyle.flexDirection.row,
    alignItems: 'center',
    marginBottom: 8,
  },
  footerText: {
    fontSize: 16,
    fontFamily: 'Vazirmatn',
    marginHorizontal: 8,
  },
  copyright: {
    fontSize: 14,
    fontFamily: 'Vazirmatn',
    textAlign: 'center',
  },
});
