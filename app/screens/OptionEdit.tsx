import React, { useEffect, useState } from 'react';
import { StyleSheet, TextInput, Pressable, Alert, ActivityIndicator, Switch } from 'react-native';
import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { Option, optionService } from '@/services/optionService';
import KeyboardAvoidingWrapper from '@/components/KeyboardAvoidingWrapper';
import { useLocalSearchParams, router } from 'expo-router';

export default function OptionEditScreen() {
  const { id } = useLocalSearchParams();
  const [text, setText] = useState('');
  const [questionId, setQuestionId] = useState('');
  const [isCorrect, setIsCorrect] = useState(false);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (id) {
      const fetchOption = async () => {
        try {
          const data = await optionService.getOption(id as string);
          setText(data.text);
          setQuestionId(data.questionId);
          setIsCorrect(data.isCorrect);
        } catch (err) {
          setError('خطا در دریافت جزئیات گزینه برای ویرایش.');
          console.error(err);
        } finally {
          setLoading(false);
        }
      };
      fetchOption();
    } else {
      setError('شناسه گزینه برای ویرایش ارائه نشده است.');
      setLoading(false);
    }
  }, [id]);

  const handleUpdateOption = async () => {
    if (!text.trim() || !questionId.trim()) {
      Alert.alert('خطای اعتبارسنجی', 'متن گزینه و شناسه سوال نمی‌توانند خالی باشند.');
      return;
    }
    if (!id) {
      Alert.alert('خطا', 'شناسه گزینه موجود نیست.');
      return;
    }

    setSaving(true);
    try {
      await optionService.updateOption(id as string, { text, questionId, isCorrect });
      Alert.alert('موفقیت', 'گزینه با موفقیت به‌روزرسانی شد!');
      router.back();
    } catch (error) {
      Alert.alert('خطا', 'به‌روزرسانی گزینه ناموفق بود. لطفاً دوباره تلاش کنید.');
      console.error('Failed to update option:', error);
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <ThemedView style={styles.centered}>
        <ActivityIndicator size="large" />
        <ThemedText>در حال بارگذاری گزینه برای ویرایش...</ThemedText>
      </ThemedView>
    );
  }

  if (error) {
    return (
      <ThemedView style={styles.centered}>
        <ThemedText style={styles.errorText}>{error}</ThemedText>
      </ThemedView>
    );
  }

  return (
    <KeyboardAvoidingWrapper style={styles.container}>
      <ThemedText style={styles.title}>ویرایش گزینه</ThemedText>
      <TextInput
        style={styles.input}
        placeholder="متن گزینه"
        value={text}
        onChangeText={setText}
        editable={!saving}
        multiline
      />
      <TextInput
        style={styles.input}
        placeholder="شناسه سوال"
        value={questionId}
        onChangeText={setQuestionId}
        editable={!saving}
      />
      <ThemedView style={styles.switchContainer}>
        <ThemedText style={styles.label}>پاسخ صحیح:</ThemedText>
        <Switch
          value={isCorrect}
          onValueChange={setIsCorrect}
          disabled={saving}
        />
      </ThemedView>
      <Pressable
        style={[styles.button, saving && styles.buttonDisabled]}
        onPress={handleUpdateOption}
        disabled={saving}
      >
        <ThemedText style={styles.buttonText}>
          {saving ? 'در حال ذخیره...' : 'ذخیره تغییرات'}
        </ThemedText>
      </Pressable>
    </KeyboardAvoidingWrapper>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
    justifyContent: 'center',
  },
  centered: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 20,
    textAlign: 'center',
  },
  input: {
    height: 40,
    borderColor: '#ccc',
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 10,
    marginBottom: 15,
    backgroundColor: '#fff',
  },
  switchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 15,
    justifyContent: 'space-between',
  },
  label: {
    fontSize: 16,
  },
  button: {
    backgroundColor: '#007bff',
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  buttonText: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 16,
  },
  buttonDisabled: {
    backgroundColor: '#a0c9ff',
  },
  errorText: {
    color: 'red',
    fontSize: 16,
  },
});
