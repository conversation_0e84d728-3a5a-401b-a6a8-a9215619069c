import React, { useEffect, useState } from 'react';
import { StyleSheet, TextInput, Pressable, Alert, ActivityIndicator } from 'react-native';
import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { Question, questionService } from '@/services/questionService';
import KeyboardAvoidingWrapper from '@/components/KeyboardAvoidingWrapper';
import { useLocalSearchParams, router } from 'expo-router';

export default function QuestionEditScreen() {
  const { id } = useLocalSearchParams();
  const [questionText, setQuestionText] = useState('');
  const [explanation, setExplanation] = useState('');
  const [difficultyLevel, setDifficultyLevel] = useState('');
  const [chapterId, setChapterId] = useState('');
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (id) {
      const fetchQuestion = async () => {
        try {
          const data = await questionService.getQuestion(id as string);
          setQuestionText(data.question_text);
          setExplanation(data.explanation);
          setDifficultyLevel(data.difficulty_level.toString());
          setChapterId(data.chapter_id);
        } catch (err) {
          setError('خطا در دریافت جزئیات سوال برای ویرایش.');
          console.error(err);
        } finally {
          setLoading(false);
        }
      };
      fetchQuestion();
    } else {
      setError('شناسه سوال برای ویرایش ارائه نشده است.');
      setLoading(false);
    }
  }, [id]);

  const handleUpdateQuestion = async () => {
    if (!questionText.trim() || !chapterId.trim() || !explanation.trim() || !difficultyLevel.trim()) {
      Alert.alert('خطای اعتبارسنجی', 'تمام فیلدها الزامی هستند.');
      return;
    }
    if (!id) {
      Alert.alert('خطا', 'شناسه سوال موجود نیست.');
      return;
    }

    setSaving(true);
    try {
      await questionService.updateQuestion(id as string, {
        question_text: questionText,
        explanation,
        difficulty_level: Number(difficultyLevel),
        chapter_id: chapterId,
      });
      Alert.alert('موفقیت', 'سوال با موفقیت به‌روزرسانی شد!');
      router.back();
    } catch (error) {
      Alert.alert('خطا', 'به‌روزرسانی سوال ناموفق بود. لطفاً دوباره تلاش کنید.');
      console.error('Failed to update question:', error);
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <ThemedView style={styles.centered}>
        <ActivityIndicator size="large" />
        <ThemedText>در حال بارگذاری سوال برای ویرایش...</ThemedText>
      </ThemedView>
    );
  }

  if (error) {
    return (
      <ThemedView style={styles.centered}>
        <ThemedText style={styles.errorText}>{error}</ThemedText>
      </ThemedView>
    );
  }

  return (
    <KeyboardAvoidingWrapper style={styles.container}>
      <ThemedText style={styles.title}>ویرایش سوال</ThemedText>
      <TextInput
        style={styles.input}
        placeholder="متن سوال"
        value={questionText}
        onChangeText={setQuestionText}
        editable={!saving}
        multiline
      />
      <TextInput
        style={styles.input}
        placeholder="توضیحات"
        value={explanation}
        onChangeText={setExplanation}
        editable={!saving}
        multiline
      />
      <TextInput
        style={styles.input}
        placeholder="سطح دشواری"
        value={difficultyLevel}
        onChangeText={setDifficultyLevel}
        editable={!saving}
      />
      <TextInput
        style={styles.input}
        placeholder="شناسه فصل"
        value={chapterId}
        onChangeText={setChapterId}
        editable={!saving}
      />
      <Pressable
        style={[styles.button, saving && styles.buttonDisabled]}
        onPress={handleUpdateQuestion}
        disabled={saving}
      >
        <ThemedText style={styles.buttonText}>
          {saving ? 'در حال ذخیره...' : 'ذخیره تغییرات'}
        </ThemedText>
      </Pressable>
    </KeyboardAvoidingWrapper>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
    justifyContent: 'center',
  },
  centered: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 20,
    textAlign: 'center',
  },
  input: {
    height: 40,
    borderColor: '#ccc',
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 10,
    marginBottom: 15,
    backgroundColor: '#fff',
  },
  button: {
    backgroundColor: '#007bff',
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  buttonText: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 16,
  },
  buttonDisabled: {
    backgroundColor: '#a0c9ff',
  },
  errorText: {
    color: 'red',
    fontSize: 16,
  },
});
