import { api } from './api';

// Define types
export interface CourseGroup {
  id: string;
  name: string;
  createdAt?: string;
  updatedAt?: string;
}

export interface CreateCourseGroupDto {
  name: string;
}

export interface UpdateCourseGroupDto {
  name?: string;
}

// CourseGroup service for handling course group-related API calls
export const courseGroupService = {
  // Get all course groups
  async getCourseGroups(): Promise<CourseGroup[]> {
    const response = await api.get<CourseGroup[]>('/v1/api/course-groups');
    return response.data;
  },

  // Get a specific course group
  async getCourseGroup(id: string): Promise<CourseGroup> {
    const response = await api.get<CourseGroup>(`/v1/api/course-groups/${id}`);
    return response.data;
  },

  // Create a new course group
  async createCourseGroup(courseGroupData: CreateCourseGroupDto): Promise<CourseGroup> {
    const response = await api.post<CourseGroup>('/v1/api/course-groups', courseGroupData);
    return response.data;
  },

  // Update a course group
  async updateCourseGroup(id: string, courseGroupData: UpdateCourseGroupDto): Promise<CourseGroup> {
    const response = await api.patch<CourseGroup>(`/v1/api/course-groups/${id}`, courseGroupData);
    return response.data;
  },

  // Delete a course group
  async deleteCourseGroup(id: string): Promise<void> {
    await api.delete(`/v1/api/course-groups/${id}`);
  }
};
