import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Alert,
} from 'react-native';
import { router } from 'expo-router';
import { Svg, Path, Circle } from 'react-native-svg';

import { rtlStyle } from '@/utils/rtl';
import { useColorScheme } from '@/hooks/useColorScheme';
import { Colors } from '@/constants/Colors';

// Icon Components
const BackArrowIcon = ({ color = "#1F2937", size = 24 }) => (
  <Svg width={size} height={size} viewBox="0 0 24 24" fill="none" stroke={color} strokeWidth="2">
    <Path d="M15 18l-6-6 6-6" />
  </Svg>
);

const MenuIcon = ({ color = "#1F2937", size = 24 }) => (
  <Svg width={size} height={size} viewBox="0 0 24 24" fill="none" stroke={color} strokeWidth="2">
    <Path d="M3 12h18M3 6h18M3 18h18" />
  </Svg>
);

const UserIcon = ({ color = "#FFFFFF", size = 48 }) => (
  <Svg width={size} height={size} viewBox="0 0 24 24" fill="none" stroke={color} strokeWidth="2">
    <Path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2" />
    <Circle cx="12" cy="7" r="4" />
  </Svg>
);

const EditIcon = ({ color = "#6B7280", size = 20 }) => (
  <Svg width={size} height={size} viewBox="0 0 24 24" fill="none" stroke={color} strokeWidth="2">
    <Path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7" />
    <Path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z" />
  </Svg>
);

export default function ProfileScreen() {
  const colorScheme = useColorScheme() ?? 'light';
  const [isEditing, setIsEditing] = useState(false);
  const [name, setName] = useState('کاربر آزمون');
  const [email, setEmail] = useState('<EMAIL>');
  const [phone, setPhone] = useState('09123456789');
  const [bio, setBio] = useState('توضیحات کاربر در اینجا نمایش داده می‌شود.');

  const handleBack = () => {
    router.back();
  };

  const openDrawer = () => {
    router.push('/(drawer)/(tabs)');
  };

  const handleSave = () => {
    setIsEditing(false);
    Alert.alert('موفقیت', 'اطلاعات پروفایل به‌روزرسانی شد');
  };

  const handleCancel = () => {
    setIsEditing(false);
    // Reset to original values if needed
  };

  const ProfileField = ({ 
    label, 
    value, 
    onChangeText, 
    multiline = false 
  }: {
    label: string;
    value: string;
    onChangeText: (text: string) => void;
    multiline?: boolean;
  }) => (
    <View style={styles.fieldContainer}>
      <Text style={[styles.fieldLabel, { color: Colors[colorScheme].text }]}>
        {label}
      </Text>
      {isEditing ? (
        <TextInput
          style={[
            styles.fieldInput,
            multiline && styles.fieldInputMultiline,
            { 
              color: Colors[colorScheme].text,
              borderColor: Colors[colorScheme].tabIconDefault,
              backgroundColor: Colors[colorScheme].background,
            }
          ]}
          value={value}
          onChangeText={onChangeText}
          multiline={multiline}
          numberOfLines={multiline ? 4 : 1}
          textAlignVertical={multiline ? 'top' : 'center'}
          textAlign={rtlStyle.textAlign.start}
        />
      ) : (
        <Text style={[styles.fieldValue, { color: Colors[colorScheme].tabIconDefault }]}>
          {value}
        </Text>
      )}
    </View>
  );

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: Colors[colorScheme].background }]}>
      {/* Header */}
      <View style={[styles.header, { borderBottomColor: Colors[colorScheme].tabIconDefault + '30' }]}>
        <TouchableOpacity style={styles.headerButton} onPress={handleBack}>
          <BackArrowIcon color={Colors[colorScheme].text} />
        </TouchableOpacity>
        <Text style={[styles.headerTitle, { color: Colors[colorScheme].text }]}>
          پروفایل
        </Text>
        <TouchableOpacity style={styles.headerButton} onPress={openDrawer}>
          <MenuIcon color={Colors[colorScheme].text} />
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Profile Picture Section */}
        <View style={styles.profileSection}>
          <View style={[styles.avatarContainer, { backgroundColor: Colors[colorScheme].tint }]}>
            <UserIcon color="#FFFFFF" size={64} />
          </View>
          <TouchableOpacity style={styles.changePhotoButton}>
            <Text style={[styles.changePhotoText, { color: Colors[colorScheme].tint }]}>
              تغییر عکس پروفایل
            </Text>
          </TouchableOpacity>
        </View>

        {/* Profile Information */}
        <View style={styles.infoSection}>
          <View style={styles.sectionHeader}>
            <Text style={[styles.sectionTitle, { color: Colors[colorScheme].text }]}>
              اطلاعات شخصی
            </Text>
            <TouchableOpacity 
              style={styles.editButton}
              onPress={() => setIsEditing(!isEditing)}
            >
              <EditIcon color={Colors[colorScheme].tint} />
              <Text style={[styles.editButtonText, { color: Colors[colorScheme].tint }]}>
                {isEditing ? 'لغو' : 'ویرایش'}
              </Text>
            </TouchableOpacity>
          </View>

          <ProfileField
            label="نام"
            value={name}
            onChangeText={setName}
          />

          <ProfileField
            label="ایمیل"
            value={email}
            onChangeText={setEmail}
          />

          <ProfileField
            label="شماره تلفن"
            value={phone}
            onChangeText={setPhone}
          />

          <ProfileField
            label="درباره من"
            value={bio}
            onChangeText={setBio}
            multiline
          />

          {isEditing && (
            <View style={styles.buttonContainer}>
              <TouchableOpacity 
                style={[styles.saveButton, { backgroundColor: Colors[colorScheme].tint }]}
                onPress={handleSave}
              >
                <Text style={styles.saveButtonText}>ذخیره تغییرات</Text>
              </TouchableOpacity>
              
              <TouchableOpacity 
                style={[styles.cancelButton, { borderColor: Colors[colorScheme].tabIconDefault }]}
                onPress={handleCancel}
              >
                <Text style={[styles.cancelButtonText, { color: Colors[colorScheme].tabIconDefault }]}>
                  لغو
                </Text>
              </TouchableOpacity>
            </View>
          )}
        </View>

        {/* Statistics Section */}
        <View style={styles.statsSection}>
          <Text style={[styles.sectionTitle, { color: Colors[colorScheme].text }]}>
            آمار
          </Text>
          
          <View style={styles.statsContainer}>
            <View style={[styles.statItem, { backgroundColor: Colors[colorScheme].tint + '20' }]}>
              <Text style={[styles.statNumber, { color: Colors[colorScheme].tint }]}>12</Text>
              <Text style={[styles.statLabel, { color: Colors[colorScheme].text }]}>آزمون شرکت کرده</Text>
            </View>
            
            <View style={[styles.statItem, { backgroundColor: Colors[colorScheme].tint + '20' }]}>
              <Text style={[styles.statNumber, { color: Colors[colorScheme].tint }]}>85%</Text>
              <Text style={[styles.statLabel, { color: Colors[colorScheme].text }]}>میانگین نمرات</Text>
            </View>
            
            <View style={[styles.statItem, { backgroundColor: Colors[colorScheme].tint + '20' }]}>
              <Text style={[styles.statNumber, { color: Colors[colorScheme].tint }]}>3</Text>
              <Text style={[styles.statLabel, { color: Colors[colorScheme].text }]}>رشته مطالعه</Text>
            </View>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: rtlStyle.flexDirection.row,
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
  },
  headerButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    fontFamily: 'Vazirmatn',
    textAlign: 'center',
  },
  content: {
    flex: 1,
  },
  profileSection: {
    alignItems: 'center',
    paddingVertical: 32,
  },
  avatarContainer: {
    width: 120,
    height: 120,
    borderRadius: 60,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
  },
  changePhotoButton: {
    paddingVertical: 8,
  },
  changePhotoText: {
    fontSize: 16,
    fontFamily: 'Vazirmatn',
  },
  infoSection: {
    paddingHorizontal: 16,
    marginBottom: 32,
  },
  sectionHeader: {
    flexDirection: rtlStyle.flexDirection.row,
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    fontFamily: 'Vazirmatn',
  },
  editButton: {
    flexDirection: rtlStyle.flexDirection.row,
    alignItems: 'center',
  },
  editButtonText: {
    fontSize: 16,
    fontFamily: 'Vazirmatn',
    ...rtlStyle.marginLeft(8),
  },
  fieldContainer: {
    marginBottom: 20,
  },
  fieldLabel: {
    fontSize: 16,
    fontWeight: '500',
    fontFamily: 'Vazirmatn',
    marginBottom: 8,
    textAlign: rtlStyle.textAlign.start,
  },
  fieldValue: {
    fontSize: 16,
    fontFamily: 'Vazirmatn',
    textAlign: rtlStyle.textAlign.start,
  },
  fieldInput: {
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 12,
    fontSize: 16,
    fontFamily: 'Vazirmatn',
  },
  fieldInputMultiline: {
    height: 100,
    textAlignVertical: 'top',
  },
  buttonContainer: {
    marginTop: 24,
  },
  saveButton: {
    borderRadius: 8,
    paddingVertical: 16,
    alignItems: 'center',
    marginBottom: 12,
  },
  saveButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
    fontFamily: 'Vazirmatn',
  },
  cancelButton: {
    borderWidth: 1,
    borderRadius: 8,
    paddingVertical: 16,
    alignItems: 'center',
  },
  cancelButtonText: {
    fontSize: 16,
    fontWeight: '600',
    fontFamily: 'Vazirmatn',
  },
  statsSection: {
    paddingHorizontal: 16,
    marginBottom: 32,
  },
  statsContainer: {
    flexDirection: rtlStyle.flexDirection.row,
    justifyContent: 'space-between',
    marginTop: 16,
  },
  statItem: {
    flex: 1,
    alignItems: 'center',
    paddingVertical: 20,
    marginHorizontal: 4,
    borderRadius: 12,
  },
  statNumber: {
    fontSize: 24,
    fontWeight: 'bold',
    fontFamily: 'Vazirmatn',
  },
  statLabel: {
    fontSize: 14,
    marginTop: 4,
    fontFamily: 'Vazirmatn',
    textAlign: 'center',
  },
});
