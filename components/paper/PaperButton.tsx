import React from 'react';
import { Button as PaperButton, useTheme } from 'react-native-paper';
import { StyleSheet, type ViewStyle, type TextStyle } from 'react-native';

export interface PaperButtonProps {
  title: string;
  onPress: () => void;
  style?: ViewStyle;
  textStyle?: TextStyle;
  disabled?: boolean;
  mode?: 'text' | 'outlined' | 'contained' | 'elevated' | 'contained-tonal';
  icon?: string;
  loading?: boolean;
  compact?: boolean;
  uppercase?: boolean;
}

export function PaperButtonComponent({ 
  title, 
  onPress, 
  style, 
  textStyle, 
  disabled = false,
  mode = 'contained',
  icon,
  loading = false,
  compact = false,
  uppercase = false,
}: PaperButtonProps) {
  const theme = useTheme();

  return (
    <PaperButton
      mode={mode}
      onPress={onPress}
      disabled={disabled}
      icon={icon}
      loading={loading}
      compact={compact}
      uppercase={uppercase}
      style={[styles.button, style]}
      labelStyle={[styles.buttonText, textStyle]}
      contentStyle={styles.buttonContent}
    >
      {title}
    </PaperButton>
  );
}

const styles = StyleSheet.create({
  button: {
    marginVertical: 4,
  },
  buttonContent: {
    paddingVertical: 8,
    paddingHorizontal: 16,
  },
  buttonText: {
    fontFamily: 'Vazirmatn',
    fontSize: 16,
    fontWeight: '500',
  },
});

// Export as default for easier importing
export default PaperButtonComponent;
