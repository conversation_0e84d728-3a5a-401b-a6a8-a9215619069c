import React, { useState } from 'react';
import { StyleSheet, TextInput, Pressable, Alert } from 'react-native';
import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { sessionService } from '@/services/sessionService';
import KeyboardAvoidingWrapper from '@/components/KeyboardAvoidingWrapper';
import { router } from 'expo-router';

export default function SessionAddScreen() {
  const [userId, setUserId] = useState('');
  const [examId, setExamId] = useState('');
  const [score, setScore] = useState('');
  const [loading, setLoading] = useState(false);

  const handleAddSession = async () => {
    if (!userId.trim() || !examId.trim() || !score.trim()) {
      Alert.alert('خطای اعتبارسنجی', 'شناسه کاربر، شناسه آزمون و نمره نمی‌توانند خالی باشند.');
      return;
    }

    setLoading(true);
    try {
      // Assuming your CreateSessionDto has 'userId', 'examId', and 'score' fields
      await sessionService.createSession({ userId, examId, score: parseInt(score) });
      Alert.alert('موفقیت', 'جلسه با موفقیت اضافه شد!');
      router.back();
    } catch (error) {
      Alert.alert('خطا', 'افزودن جلسه ناموفق بود. لطفاً دوباره تلاش کنید.');
      console.error('Failed to add session:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <KeyboardAvoidingWrapper style={styles.container}>
      <ThemedText style={styles.title}>افزودن جلسه جدید</ThemedText>
      <TextInput
        style={styles.input}
        placeholder="شناسه کاربر"
        value={userId}
        onChangeText={setUserId}
        editable={!loading}
      />
      <TextInput
        style={styles.input}
        placeholder="شناسه آزمون"
        value={examId}
        onChangeText={setExamId}
        editable={!loading}
      />
      <TextInput
        style={styles.input}
        placeholder="نمره"
        value={score}
        onChangeText={setScore}
        editable={!loading}
        keyboardType="numeric"
      />
      <Pressable
        style={[styles.button, loading && styles.buttonDisabled]}
        onPress={handleAddSession}
        disabled={loading}
      >
        <ThemedText style={styles.buttonText}>
          {loading ? 'در حال افزودن...' : 'افزودن جلسه'}
        </ThemedText>
      </Pressable>
    </KeyboardAvoidingWrapper>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
    justifyContent: 'center',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 20,
    textAlign: 'center',
  },
  input: {
    height: 40,
    borderColor: '#ccc',
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 10,
    marginBottom: 15,
    backgroundColor: '#fff',
  },
  button: {
    backgroundColor: '#007bff',
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  buttonText: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 16,
  },
  buttonDisabled: {
    backgroundColor: '#a0c9ff',
  },
});
