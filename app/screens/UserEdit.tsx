import React, { useEffect, useState } from 'react';
import { StyleSheet, SafeAreaView } from 'react-native';
import { Surface, ActivityIndicator } from 'react-native-paper';
import { User, userService } from '@/services/userService';
import KeyboardAvoidingWrapper from '@/components/KeyboardAvoidingWrapper';
import { useLocalSearchParams, router } from 'expo-router';
import {
  PaperFormAppBar,
  PaperTextInput,
  PaperButton,
  PaperTitle,
  PaperBody,
  PaperFormField,
  PaperFormSection,
  PaperAlertDialog,
  PaperRadioGroup,
} from '@/components/paper';
import { t } from '@/constants/Localization';

export default function UserEditScreen() {
  const { id } = useLocalSearchParams();
  const [email, setEmail] = useState('');
  const [mobile, setMobile] = useState('');
  const [passwordHash, setPasswordHash] = useState('');
  const [firstName, setFirstName] = useState('');
  const [lastName, setLastName] = useState('');
  const [role, setRole] = useState('');
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showAlert, setShowAlert] = useState(false);
  const [alertMessage, setAlertMessage] = useState('');
  const [alertTitle, setAlertTitle] = useState('');

  // Form validation
  const [errors, setErrors] = useState({
    email: '',
    mobile: '',
    firstName: '',
    lastName: '',
    role: '',
  });

  const roleOptions = [
    { label: t('admin') || 'مدیر', value: 'admin' },
    { label: t('teacher') || 'مدرس', value: 'teacher' },
    { label: t('student') || 'دانشجو', value: 'student' },
  ];

  useEffect(() => {
    if (id) {
      const fetchUser = async () => {
        try {
          const data = await userService.getUser(id as string);
          setEmail(data.email);
          setMobile(data.mobile);
          // password_hash is not typically fetched for security reasons,
          // but we keep the state for potential updates.
          setFirstName(data.first_name);
          setLastName(data.last_name);
          setRole(data.role);
        } catch (err) {
          setError('Failed to fetch user details for editing.');
          console.error(err);
        } finally {
          setLoading(false);
        }
      };
      fetchUser();
    } else {
      setError('User ID not provided for editing.');
      setLoading(false);
    }
  }, [id]);

  const validateForm = () => {
    const newErrors = {
      email: '',
      mobile: '',
      firstName: '',
      lastName: '',
      role: '',
    };

    if (!email.trim()) {
      newErrors.email = t('required') || 'این فیلد الزامی است';
    } else if (!/\S+@\S+\.\S+/.test(email)) {
      newErrors.email = t('invalidEmail') || 'ایمیل نامعتبر است';
    }

    if (!mobile.trim()) {
      newErrors.mobile = t('required') || 'این فیلد الزامی است';
    }

    if (!firstName.trim()) {
      newErrors.firstName = t('required') || 'این فیلد الزامی است';
    }

    if (!lastName.trim()) {
      newErrors.lastName = t('required') || 'این فیلد الزامی است';
    }

    if (!role.trim()) {
      newErrors.role = t('required') || 'این فیلد الزامی است';
    }

    setErrors(newErrors);
    return !Object.values(newErrors).some(error => error !== '');
  };

  const handleUpdateUser = async () => {
    if (!validateForm()) {
      return;
    }

    if (!id) {
      setAlertTitle(t('error'));
      setAlertMessage('شناسه کاربر یافت نشد.');
      setShowAlert(true);
      return;
    }

    setSaving(true);
    try {
      const userData: Partial<User> = {
        email,
        mobile,
        first_name: firstName,
        last_name: lastName,
        role,
      };
      if (passwordHash.trim()) {
        userData.password_hash = passwordHash;
      }

      await userService.updateUser(id as string, userData);
      setAlertTitle(t('success'));
      setAlertMessage(t('updateSuccess') || 'کاربر با موفقیت به‌روزرسانی شد!');
      setShowAlert(true);
      // Navigate back after showing success message
      setTimeout(() => {
        router.back();
      }, 1500);
    } catch (error) {
      setAlertTitle(t('error'));
      setAlertMessage('به‌روزرسانی کاربر انجام نشد. لطفا دوباره امتحان کنید.');
      setShowAlert(true);
      console.error('Failed to update user:', error);
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <SafeAreaView style={styles.centered}>
        <ActivityIndicator size="large" />
        <PaperBody style={styles.loadingText}>
          {t('loading') || 'در حال بارگذاری...'}
        </PaperBody>
      </SafeAreaView>
    );
  }

  if (error) {
    return (
      <SafeAreaView style={styles.centered}>
        <PaperBody style={styles.errorText}>{error}</PaperBody>
        <PaperButton
          title={t('back')}
          onPress={() => router.back()}
          mode="outlined"
          style={styles.backButton}
        />
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      {/* App Bar */}
      <PaperFormAppBar
        title={t('editUser') || 'ویرایش کاربر'}
        onSavePress={handleUpdateUser}
        saveDisabled={saving}
      />

      {/* Content */}
      <Surface style={styles.content}>
        <KeyboardAvoidingWrapper>
          <PaperFormSection title={t('userDetails') || 'اطلاعات کاربر'}>
            <PaperFormField
              label={t('email')}
              error={errors.email}
              required
            >
              <PaperTextInput
                value={email}
                onChangeText={setEmail}
                placeholder="<EMAIL>"
                disabled={saving}
                keyboardType="email-address"
                autoCapitalize="none"
                error={!!errors.email}
              />
            </PaperFormField>

            <PaperFormField
              label={t('mobile') || 'شماره موبایل'}
              error={errors.mobile}
              required
            >
              <PaperTextInput
                value={mobile}
                onChangeText={setMobile}
                placeholder="09123456789"
                disabled={saving}
                keyboardType="phone-pad"
                error={!!errors.mobile}
              />
            </PaperFormField>

            <PaperFormField
              label={t('password') || 'رمز عبور'}
              error=""
            >
              <PaperTextInput
                value={passwordHash}
                onChangeText={setPasswordHash}
                placeholder="برای حفظ رمز فعلی خالی بگذارید"
                disabled={saving}
                secureTextEntry
              />
            </PaperFormField>

            <PaperFormField
              label={t('firstName')}
              error={errors.firstName}
              required
            >
              <PaperTextInput
                value={firstName}
                onChangeText={setFirstName}
                placeholder="نام"
                disabled={saving}
                error={!!errors.firstName}
              />
            </PaperFormField>

            <PaperFormField
              label={t('lastName')}
              error={errors.lastName}
              required
            >
              <PaperTextInput
                value={lastName}
                onChangeText={setLastName}
                placeholder="نام خانوادگی"
                disabled={saving}
                error={!!errors.lastName}
              />
            </PaperFormField>

            <PaperFormField
              label={t('role')}
              error={errors.role}
              required
            >
              <PaperRadioGroup
                options={roleOptions}
                value={role}
                onValueChange={setRole}
              />
            </PaperFormField>

            <PaperButton
              title={saving ? t('saving') || 'در حال ذخیره...' : t('save')}
              onPress={handleUpdateUser}
              disabled={saving}
              loading={saving}
              mode="contained"
              style={styles.submitButton}
              icon="content-save"
            />
          </PaperFormSection>
        </KeyboardAvoidingWrapper>
      </Surface>

      {/* Alert Dialog */}
      <PaperAlertDialog
        visible={showAlert}
        onDismiss={() => setShowAlert(false)}
        title={alertTitle}
        message={alertMessage}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  centered: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 16,
  },
  loadingText: {
    marginTop: 16,
    textAlign: 'center',
  },
  errorText: {
    textAlign: 'center',
    marginBottom: 16,
  },
  backButton: {
    marginTop: 16,
  },
  submitButton: {
    marginTop: 24,
  },
});
