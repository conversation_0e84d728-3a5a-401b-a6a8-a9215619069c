import React, { useEffect, useState } from "react";
import {
  ActivityIndicator,
  StyleSheet,
  View,
  FlatList,
  I18nManager,
} from "react-native";
import { ThemedText } from "@/components/ThemedText";
import { ThemedView } from "@/components/ThemedView";
import { Exam, examService } from "@/services/examService";
import { Question } from "@/services/questionService";
import { useLocalSearchParams } from "expo-router";
import QuestionDetails from "./QuestionDetails";

export default function ExamDetailsScreen() {
  const { id } = useLocalSearchParams();
  const [exam, setExam] = useState<Exam | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (id) {
      const fetchExam = async () => {
        try {
          const data = await examService.getExam(id as string);
          setExam(data);
        } catch (err) {
          setError("خطا در دریافت جزئیات آزمون.");
          console.error(err);
        } finally {
          setLoading(false);
        }
      };
      fetchExam();
    } else {
      setError("شناسه آزمون ارائه نشده است.");
      setLoading(false);
    }
  }, [id]);

  if (loading) {
    return (
      <ThemedView style={styles.centered}>
        <ActivityIndicator size="large" />
        <ThemedText>در حال بارگذاری جزئیات آزمون...</ThemedText>
      </ThemedView>
    );
  }

  if (error) {
    return (
      <ThemedView style={styles.centered}>
        <ThemedText style={styles.errorText}>{error}</ThemedText>
      </ThemedView>
    );
  }

  if (!exam) {
    return (
      <ThemedView style={styles.centered}>
        <ThemedText>آزمون یافت نشد.</ThemedText>
      </ThemedView>
    );
  }

  return (
    <ThemedView style={styles.container}>
      <ThemedText style={styles.title}>جزئیات آزمون</ThemedText>
      <View style={styles.detailItem}>
        <ThemedText style={styles.label}>شناسه:</ThemedText>
        <ThemedText>{exam.id}</ThemedText>
      </View>
      <View style={styles.detailItem}>
        <ThemedText style={styles.label}>نام:</ThemedText>
        <ThemedText>{exam.name}</ThemedText>
      </View>
      <View style={styles.detailItem}>
        <ThemedText style={styles.label}>مدت زمان:</ThemedText>
        <ThemedText>{exam.duration_minutes} دقیقه</ThemedText>
      </View>
      <View style={styles.detailItem}>
        <ThemedText style={styles.label}>دستورالعمل:</ThemedText>
        <ThemedText>{exam.instructions}</ThemedText>
      </View>
      <View style={styles.detailItem}>
        <ThemedText style={styles.label}>شناسه رشته:</ThemedText>
        <ThemedText>{exam.major_id}</ThemedText>
      </View>
      {exam.created_at && (
        <View style={styles.detailItem}>
          <ThemedText style={styles.label}>تاریخ ایجاد:</ThemedText>
          <ThemedText>
            {new Date(exam.created_at).toLocaleString("fa-IR")}
          </ThemedText>
        </View>
      )}
      {exam.updatad_at && (
        <View style={styles.detailItem}>
          <ThemedText style={styles.label}>تاریخ بروزرسانی:</ThemedText>
          <ThemedText>
            {new Date(exam.updatad_at).toLocaleString("fa-IR")}
          </ThemedText>
        </View>
      )}

      <ThemedText style={styles.title}>سوالات</ThemedText>
      <FlatList
        style={styles.list}
        data={exam.questions}
        keyExtractor={(item) => item.id}
        renderItem={({ item }) => <QuestionDetails question={item} />}
      />
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
    direction: "rtl",
  },
  list: {
    // flex: 1,
    // direction: "rtl",
    // writingDirection: "rtl",
    // textAlign: "right",
    // flexDirection: "row",
    // marginBottom: 10,
    // alignItems: "flex-start",
  },
  questionContainer: {
    marginBottom: 10,
    padding: 10,
    borderWidth: 1,
    borderColor: "#ddd",
    borderRadius: 5,
    direction: "rtl",
  },
  questionText: {
    fontWeight: "bold",
    textAlign: "right",
    writingDirection: "rtl",
  },
  explanationText: {
    marginTop: 5,
    textAlign: "right",
    writingDirection: "rtl",
  },
  centered: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  title: {
    fontSize: 24,
    fontWeight: "bold",
    marginBottom: 20,
    textAlign: "right",
    writingDirection: "rtl",
  },
  detailItem: {
    flexDirection: "row",
    marginBottom: 10,
    alignItems: "flex-start",
  },
  label: {
    fontWeight: "bold",
    marginLeft: 5,
    textAlign: "right",
    writingDirection: "rtl",
  },
  errorText: {
    color: "red",
    fontSize: 16,
    textAlign: "center",
  },
});
