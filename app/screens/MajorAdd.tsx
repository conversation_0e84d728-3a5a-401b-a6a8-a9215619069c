import React, { useState } from 'react';
import { StyleSheet, TextInput, Pressable, Alert } from 'react-native';
import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { majorService } from '@/services/majorService';
import KeyboardAvoidingWrapper from '@/components/KeyboardAvoidingWrapper';
import { router } from 'expo-router';

export default function MajorAddScreen() {
  const [name, setName] = useState('');
  const [description, setDescription] = useState('');
  const [loading, setLoading] = useState(false);

  const handleAddMajor = async () => {
    if (!name.trim()) {
      Alert.alert('Validation Error', 'Major name cannot be empty.');
      return;
    }
    if (!description.trim()) {
      Alert.alert('Validation Error', 'Major description cannot be empty.');
      return;
    }

    setLoading(true);
    try {
      await majorService.createMajor({ name, description });
      Alert.alert('Success', 'Major added successfully!');
      router.back();
    } catch (error) {
      Alert.alert('Error', 'Failed to add major. Please try again.');
      console.error('Failed to add major:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <KeyboardAvoidingWrapper style={styles.container}>
      <ThemedText style={styles.title}>Add New Major</ThemedText>
      <TextInput
        style={styles.input}
        placeholder="Major Name"
        value={name}
        onChangeText={setName}
        editable={!loading}
      />
      <TextInput
        style={styles.input}
        placeholder="Major Description"
        value={description}
        onChangeText={setDescription}
        editable={!loading}
        multiline
      />
      <Pressable
        style={[styles.button, loading && styles.buttonDisabled]}
        onPress={handleAddMajor}
        disabled={loading}
      >
        <ThemedText style={styles.buttonText}>
          {loading ? 'Adding...' : 'Add Major'}
        </ThemedText>
      </Pressable>
    </KeyboardAvoidingWrapper>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
    justifyContent: 'center',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 20,
    textAlign: 'center',
  },
  input: {
    height: 40,
    borderColor: '#ccc',
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 10,
    marginBottom: 15,
    backgroundColor: '#fff',
  },
  button: {
    backgroundColor: '#007bff',
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  buttonText: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 16,
  },
  buttonDisabled: {
    backgroundColor: '#a0c9ff',
  },
});
