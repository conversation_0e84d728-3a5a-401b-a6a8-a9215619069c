import React, { useEffect, useState } from 'react';
import { StyleSheet, TextInput, Pressable, Alert, ActivityIndicator } from 'react-native';
import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { Chapter, chapterService } from '@/services/chapterService';
import KeyboardAvoidingWrapper from '@/components/KeyboardAvoidingWrapper';
import { useLocalSearchParams, router } from 'expo-router';

export default function ChapterEditScreen() {
  const { id } = useLocalSearchParams();
  const [name, setName] = useState('');
  const [description, setDescription] = useState('');
  const [order_in_course, setOrderInCourse] = useState('');
  const [course_id, setCourseId] = useState('');
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (id) {
      const fetchChapter = async () => {
        try {
          const data = await chapterService.getChapter(id as string);
          setName(data.name);
          setDescription(data.description);
          setOrderInCourse(data.order_in_course.toString());
          setCourseId(data.course_id.toString());
        } catch (err) {
          setError('Failed to fetch chapter details for editing.');
          console.error(err);
        } finally {
          setLoading(false);
        }
      };
      fetchChapter();
    } else {
      setError('Chapter ID not provided for editing.');
      setLoading(false);
    }
  }, [id]);

  const handleUpdateChapter = async () => {
    if (!name.trim() || !description.trim() || !order_in_course.trim() || !course_id.trim()) {
      Alert.alert('Validation Error', 'All fields are required.');
      return;
    }
    if (!id) {
      Alert.alert('Error', 'Chapter ID is missing.');
      return;
    }

    setSaving(true);
    try {
      await chapterService.updateChapter(id as string, {
        name,
        description,
        order_in_course: parseInt(order_in_course, 10),
        course_id: parseInt(course_id, 10),
      });
      Alert.alert('Success', 'Chapter updated successfully!');
      router.back();
    } catch (error) {
      Alert.alert('Error', 'Failed to update chapter. Please try again.');
      console.error('Failed to update chapter:', error);
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <ThemedView style={styles.centered}>
        <ActivityIndicator size="large" />
        <ThemedText>Loading chapter for editing...</ThemedText>
      </ThemedView>
    );
  }

  if (error) {
    return (
      <ThemedView style={styles.centered}>
        <ThemedText style={styles.errorText}>{error}</ThemedText>
      </ThemedView>
    );
  }

  return (
    <KeyboardAvoidingWrapper style={styles.container}>
      <ThemedText style={styles.title}>Edit Chapter</ThemedText>
      <TextInput
        style={styles.input}
        placeholder="Chapter Name"
        value={name}
        onChangeText={setName}
        editable={!saving}
      />
      <TextInput
        style={styles.input}
        placeholder="Description"
        value={description}
        onChangeText={setDescription}
        editable={!saving}
      />
      <TextInput
        style={styles.input}
        placeholder="Order in Course"
        value={order_in_course}
        onChangeText={setOrderInCourse}
        editable={!saving}
        keyboardType="numeric"
      />
      <TextInput
        style={styles.input}
        placeholder="Course ID"
        value={course_id}
        onChangeText={setCourseId}
        editable={!saving}
        keyboardType="numeric"
      />
      <Pressable
        style={[styles.button, saving && styles.buttonDisabled]}
        onPress={handleUpdateChapter}
        disabled={saving}
      >
        <ThemedText style={styles.buttonText}>
          {saving ? 'Saving...' : 'ذخیره تغییرات'}
        </ThemedText>
      </Pressable>
    </KeyboardAvoidingWrapper>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
    justifyContent: 'center',
  },
  centered: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 20,
    textAlign: 'center',
  },
  input: {
    height: 40,
    borderColor: '#ccc',
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 10,
    marginBottom: 15,
    backgroundColor: '#fff',
  },
  button: {
    backgroundColor: '#007bff',
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  buttonText: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 16,
  },
  buttonDisabled: {
    backgroundColor: '#a0c9ff',
  },
  errorText: {
    color: 'red',
    fontSize: 16,
  },
});
