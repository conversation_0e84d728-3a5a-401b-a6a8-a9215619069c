import React from 'react';
import { List, Divider, useTheme } from 'react-native-paper';
import { StyleSheet, type ViewStyle } from 'react-native';
import { ScrollView, RefreshControl } from 'react-native';

export interface PaperListItemProps {
  title: string;
  description?: string;
  left?: (props: any) => React.ReactNode;
  right?: (props: any) => React.ReactNode;
  onPress?: () => void;
  onLongPress?: () => void;
  style?: ViewStyle;
  disabled?: boolean;
}

export function PaperListItem({
  title,
  description,
  left,
  right,
  onPress,
  onLongPress,
  style,
  disabled = false,
}: PaperListItemProps) {
  return (
    <List.Item
      title={title}
      description={description}
      left={left}
      right={right}
      onPress={onPress}
      onLongPress={onLongPress}
      style={[styles.listItem, style]}
      titleStyle={styles.listItemTitle}
      descriptionStyle={styles.listItemDescription}
      disabled={disabled}
    />
  );
}

// List Section component
export function PaperListSection({
  title,
  children,
  style,
}: {
  title?: string;
  children: React.ReactNode;
  style?: ViewStyle;
}) {
  return (
    <List.Section title={title} style={style} titleStyle={styles.sectionTitle}>
      {children}
    </List.Section>
  );
}

// List Accordion component
export function PaperListAccordion({
  title,
  description,
  left,
  children,
  expanded,
  onPress,
  style,
}: {
  title: string;
  description?: string;
  left?: (props: any) => React.ReactNode;
  children: React.ReactNode;
  expanded?: boolean;
  onPress?: () => void;
  style?: ViewStyle;
}) {
  return (
    <List.Accordion
      title={title}
      description={description}
      left={left}
      expanded={expanded}
      onPress={onPress}
      style={[styles.accordion, style]}
      titleStyle={styles.accordionTitle}
      descriptionStyle={styles.accordionDescription}
    >
      {children}
    </List.Accordion>
  );
}

// Scrollable List with Pull-to-Refresh
export function PaperScrollableList({
  children,
  refreshing = false,
  onRefresh,
  style,
  contentContainerStyle,
}: {
  children: React.ReactNode;
  refreshing?: boolean;
  onRefresh?: () => void;
  style?: ViewStyle;
  contentContainerStyle?: ViewStyle;
}) {
  return (
    <ScrollView
      style={[styles.scrollView, style]}
      contentContainerStyle={contentContainerStyle}
      refreshControl={
        onRefresh ? (
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={['#2196F3']} // Android
            tintColor="#2196F3" // iOS
          />
        ) : undefined
      }
    >
      {children}
    </ScrollView>
  );
}

// List with Dividers
export function PaperDividedList({
  items,
  renderItem,
  keyExtractor,
  style,
}: {
  items: any[];
  renderItem: (item: any, index: number) => React.ReactNode;
  keyExtractor: (item: any, index: number) => string;
  style?: ViewStyle;
}) {
  return (
    <List.Section style={style}>
      {items.map((item, index) => (
        <React.Fragment key={keyExtractor(item, index)}>
          {renderItem(item, index)}
          {index < items.length - 1 && <Divider style={styles.divider} />}
        </React.Fragment>
      ))}
    </List.Section>
  );
}

// Empty List component
export function PaperEmptyList({
  title = 'هیچ موردی یافت نشد',
  description = 'در حال حاضر هیچ داده‌ای برای نمایش وجود ندارد',
  icon = 'inbox-outline',
  action,
  style,
}: {
  title?: string;
  description?: string;
  icon?: string;
  action?: React.ReactNode;
  style?: ViewStyle;
}) {
  return (
    <List.Section style={[styles.emptyList, style]}>
      <List.Item
        title={title}
        description={description}
        left={(props) => <List.Icon {...props} icon={icon} />}
        titleStyle={styles.emptyListTitle}
        descriptionStyle={styles.emptyListDescription}
      />
      {action && <div style={styles.emptyListAction}>{action}</div>}
    </List.Section>
  );
}

const styles = StyleSheet.create({
  listItem: {
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  listItemTitle: {
    fontFamily: 'Vazirmatn',
    fontSize: 16,
    fontWeight: '500',
    textAlign: 'right',
  },
  listItemDescription: {
    fontFamily: 'Vazirmatn',
    fontSize: 14,
    textAlign: 'right',
    marginTop: 4,
  },
  sectionTitle: {
    fontFamily: 'Vazirmatn',
    fontSize: 18,
    fontWeight: '600',
    textAlign: 'right',
    paddingHorizontal: 16,
  },
  accordion: {
    marginVertical: 4,
  },
  accordionTitle: {
    fontFamily: 'Vazirmatn',
    fontSize: 16,
    fontWeight: '500',
    textAlign: 'right',
  },
  accordionDescription: {
    fontFamily: 'Vazirmatn',
    fontSize: 14,
    textAlign: 'right',
  },
  scrollView: {
    flex: 1,
  },
  divider: {
    marginHorizontal: 16,
  },
  emptyList: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 32,
  },
  emptyListTitle: {
    fontFamily: 'Vazirmatn',
    fontSize: 18,
    fontWeight: '600',
    textAlign: 'center',
  },
  emptyListDescription: {
    fontFamily: 'Vazirmatn',
    fontSize: 14,
    textAlign: 'center',
    marginTop: 8,
  },
  emptyListAction: {
    marginTop: 16,
  },
});
