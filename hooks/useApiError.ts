import { useCallback } from 'react';
import { handleError, logError, getErrorActionMessage } from '@/utils/errorHandler';
import { useToast } from '@/services/toastService';
import { t } from '@/constants/Localization';

/**
 * Custom hook for handling API errors with toast notifications and retry functionality
 */
export function useApiError() {
  const toast = useToast();

  const handleApiError = useCallback((error: any, context?: string, onRetry?: () => void) => {
    const errorInfo = handleError(error);
    logError(errorInfo, context);

    // Show appropriate toast based on error type and retry capability
    if (errorInfo.canRetry && onRetry) {
      toast.showRetryError(errorInfo.userMessage, onRetry);
    } else {
      toast.showError(errorInfo.userMessage);
    }

    return errorInfo;
  }, [toast]);

  const handleNetworkError = useCallback((onRetry?: () => void) => {
    if (onRetry) {
      toast.showRetryError(t('connectionError'), onRetry);
    } else {
      toast.showError(t('connectionError'));
    }
  }, [toast]);

  const handleServerError = useCallback(() => {
    toast.showError(t('serverError'));
  }, [toast]);

  const showSuccess = useCallback((message: string) => {
    toast.showSuccess(message);
  }, [toast]);

  const showLoadError = useCallback((onRetry?: () => void) => {
    if (onRetry) {
      toast.showRetryError(t('dataLoadFailed'), onRetry);
    } else {
      toast.showError(t('dataLoadFailed'));
    }
  }, [toast]);

  const showSaveError = useCallback((onRetry?: () => void) => {
    if (onRetry) {
      toast.showRetryError(t('dataSaveFailed'), onRetry);
    } else {
      toast.showError(t('dataSaveFailed'));
    }
  }, [toast]);

  return {
    handleApiError,
    handleNetworkError,
    handleServerError,
    showSuccess,
    showLoadError,
    showSaveError,
    toast,
  };
}

/**
 * Hook for handling API operations with automatic error handling and retry
 */
export function useApiOperation() {
  const { handleApiError, showSuccess } = useApiError();

  const executeOperation = useCallback(async <T>(
    operation: () => Promise<T>,
    options?: {
      onSuccess?: (data: T) => void;
      onError?: (error: any) => void;
      successMessage?: string;
      context?: string;
      enableRetry?: boolean;
    }
  ): Promise<{ success: boolean; data?: T; error?: any }> => {
    try {
      const data = await operation();
      
      if (options?.successMessage) {
        showSuccess(options.successMessage);
      }
      
      options?.onSuccess?.(data);
      
      return { success: true, data };
    } catch (error) {
      const retryOperation = options?.enableRetry ? () => executeOperation(operation, options) : undefined;
      
      handleApiError(error, options?.context, retryOperation);
      options?.onError?.(error);
      
      return { success: false, error };
    }
  }, [handleApiError, showSuccess]);

  return { executeOperation };
}
