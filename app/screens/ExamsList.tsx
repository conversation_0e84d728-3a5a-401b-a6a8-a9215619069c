import React, { useEffect, useState } from "react";
import { StyleSheet, SafeAreaView } from "react-native";
import { Surface } from "react-native-paper";
import { examService, Exam } from "../../services/examService";
import { router } from "expo-router";
import {
  PaperListAppBar,
  PaperRefreshableFlatList,
  PaperCard,
  PaperCardTitle,
  PaperCardActions,
  PaperButton,
  PaperTitle,
  PaperBody,
  PaperAddFAB,
  PaperDeleteDialog,
  useRefresh,
} from "@/components/paper";
import { t } from "@/constants/Localization";
import { useApiError } from "@/hooks/useApiError";

export default function ExamsListScreen() {
  const [exams, setExams] = useState<Exam[]>([]);
  const [loading, setLoading] = useState(true);
  const [deleteDialogVisible, setDeleteDialogVisible] = useState(false);
  const [examToDelete, setExamToDelete] = useState<Exam | null>(null);
  const { handleApiError } = useApiError();

  const loadExams = async () => {
    try {
      setLoading(true);
      const data = await examService.getExams();
      setExams(data);
    } catch (err) {
      handleApiError(err, "Failed to load exams");
      console.error("Failed to load exams:", err);
    } finally {
      setLoading(false);
    }
  };

  const { refreshing, onRefresh } = useRefresh(loadExams);

  useEffect(() => {
    loadExams();
  }, []);

  // Navigation handlers
  const handleAddExam = () => {
    router.push('/screens/ExamAdd');
  };

  const handleExamPress = (exam: Exam) => {
    router.push(`/screens/ExamDetails?id=${exam.id}`);
  };

  const handleEditExam = (exam: Exam) => {
    router.push(`/screens/ExamEdit?id=${exam.id}`);
  };

  const handleDeleteExam = (exam: Exam) => {
    setExamToDelete(exam);
    setDeleteDialogVisible(true);
  };

  const confirmDeleteExam = async () => {
    if (!examToDelete) return;

    try {
      await examService.deleteExam(examToDelete.id);
      setDeleteDialogVisible(false);
      setExamToDelete(null);
      loadExams(); // Refresh the list
    } catch (error) {
      handleApiError(error, 'Failed to delete exam');
      console.error('Failed to delete exam:', error);
    }
  };

  const renderExamItem = ({ item }: { item: Exam }) => (
    <PaperCard
      style={styles.examCard}
      onPress={() => handleExamPress(item)}
    >
      <PaperCardTitle
        title={item.name}
        subtitle={`${item.duration_minutes} دقیقه | ${item.instructions || 'بدون توضیحات'}`}
        left={(props) => (
          <Surface {...props} style={styles.examIcon}>
            <PaperTitle size="small">E</PaperTitle>
          </Surface>
        )}
      />
      <PaperCardActions>
        <PaperButton
          title={t('edit')}
          mode="outlined"
          onPress={() => handleEditExam(item)}
          icon="pencil"
          compact
        />
        <PaperButton
          title={t('delete')}
          mode="outlined"
          onPress={() => handleDeleteExam(item)}
          icon="delete"
          compact
          style={styles.deleteButton}
        />
      </PaperCardActions>
    </PaperCard>
  );

  return (
    <SafeAreaView style={styles.container}>
      {/* App Bar */}
      <PaperListAppBar
        title={t('exams') || 'آزمون‌ها'}
        onAddPress={handleAddExam}
      />

      {/* Content */}
      <Surface style={styles.content}>
        <PaperRefreshableFlatList
          data={exams}
          renderItem={renderExamItem}
          keyExtractor={(item) => item.id}
          refreshing={refreshing}
          onRefresh={onRefresh}
          loading={loading}
          style={styles.list}
          contentContainerStyle={styles.listContent}
          emptyTitle={'هیچ آزمونی یافت نشد'}
          emptyDescription={'برای افزودن آزمون جدید از دکمه + استفاده کنید'}
          emptyIcon="school"
          emptyAction={
            <PaperButton
              title={'افزودن آزمون'}
              mode="contained"
              onPress={handleAddExam}
              icon="plus"
            />
          }
        />
      </Surface>

      {/* Floating Action Button */}
      <PaperAddFAB
        onPress={handleAddExam}
        visible={!loading}
      />

      {/* Delete Confirmation Dialog */}
      <PaperDeleteDialog
        visible={deleteDialogVisible}
        onDismiss={() => setDeleteDialogVisible(false)}
        onConfirm={confirmDeleteExam}
        title={t('delete') || 'حذف آزمون'}
        content={
          examToDelete
            ? `آیا از حذف آزمون "${examToDelete.name}" مطمئن هستید؟`
            : ''
        }
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
  },
  list: {
    flex: 1,
  },
  listContent: {
    padding: 16,
  },
  examCard: {
    marginVertical: 8,
  },
  examIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#e3f2fd',
  },
  deleteButton: {
    borderColor: '#f44336',
  },
});
