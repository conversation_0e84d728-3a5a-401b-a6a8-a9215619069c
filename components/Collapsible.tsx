import { PropsWithChildren, useState } from 'react';
import { StyleSheet } from 'react-native';
import { List, Surface, useTheme } from 'react-native-paper';
import { PaperText } from './paper';

export function Collapsible({ children, title }: PropsWithChildren & { title: string }) {
  const [isOpen, setIsOpen] = useState(false);
  const theme = useTheme();

  return (
    <Surface style={styles.container}>
      <List.Accordion
        title={title}
        expanded={isOpen}
        onPress={() => setIsOpen((value) => !value)}
        style={styles.accordion}
        titleStyle={styles.title}
        left={(props) => (
          <List.Icon
            {...props}
            icon={isOpen ? 'chevron-down' : 'chevron-right'}
          />
        )}
      >
        <Surface style={styles.content}>
          {children}
        </Surface>
      </List.Accordion>
    </Surface>
  );
}

const styles = StyleSheet.create({
  heading: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  content: {
    marginTop: 6,
    marginLeft: 24,
  },
});
