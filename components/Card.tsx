import React from 'react';
import { Card as PaperCard, useTheme } from 'react-native-paper';
import { StyleSheet, type ViewStyle } from 'react-native';

export interface CardProps {
  children: React.ReactNode;
  style?: ViewStyle;
  mode?: 'elevated' | 'outlined' | 'contained';
  onPress?: () => void;
  onLongPress?: () => void;
  disabled?: boolean;
}

export function Card({
  children,
  style,
  mode = 'elevated',
  onPress,
  onLongPress,
  disabled = false,
}: CardProps) {
  const theme = useTheme();

  return (
    <PaperCard
      mode={mode}
      style={[styles.card, style]}
      onPress={onPress}
      onLongPress={onLongPress}
      disabled={disabled}
    >
      <PaperCard.Content style={styles.cardContent}>
        {children}
      </PaperCard.Content>
    </PaperCard>
  );
}

// Card Title component
export function CardTitle({
  title,
  subtitle,
  left,
  right,
  style
}: {
  title: string;
  subtitle?: string;
  left?: (props: any) => React.ReactNode;
  right?: (props: any) => React.ReactNode;
  style?: ViewStyle;
}) {
  return (
    <PaperCard.Title
      title={title}
      subtitle={subtitle}
      left={left}
      right={right}
      style={style}
      titleStyle={styles.cardTitle}
      subtitleStyle={styles.cardSubtitle}
    />
  );
}

// Card Actions component
export function CardActions({
  children,
  style
}: {
  children: React.ReactNode;
  style?: ViewStyle;
}) {
  return (
    <PaperCard.Actions style={style}>
      {children}
    </PaperCard.Actions>
  );
}

const styles = StyleSheet.create({
  card: {
    marginVertical: 8,
    marginHorizontal: 16,
  },
  cardContent: {
    padding: 16,
  },
  cardTitle: {
    fontFamily: 'Vazirmatn',
    fontSize: 18,
    fontWeight: '600',
    textAlign: 'right',
  },
  cardSubtitle: {
    fontFamily: 'Vazirmatn',
    fontSize: 14,
    textAlign: 'right',
    marginTop: 4,
  },
});
