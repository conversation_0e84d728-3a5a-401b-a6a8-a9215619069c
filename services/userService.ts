import { api } from './api';

// Define types
export interface User {
  id: string;
  email: string;
  mobile: string;
  password_hash: string;
  first_name: string;
  last_name: string;
  role: string;
  createdAt?: string;
}

export interface CreateUserDto {
  email: string;
  mobile: string;
  password_hash: string;
  first_name: string;
  last_name: string;
  role: string;
}

export interface UpdateUserDto {
  email?: string;
  mobile?: string;
  password_hash?: string;
  first_name?: string;
  last_name?: string;
  role?: string;
}

// User service for handling user-related API calls
export const userService = {
  // Get all users
  async getUsers(): Promise<User[]> {
    const response = await api.get<User[]>('/v1/api/users');
    return response.data;
  },

  // Get a specific user
  async getUser(id: string): Promise<User> {
    const response = await api.get<User>(`/v1/api/users/${id}`);
    return response.data;
  },

  // Create a new user
  async createUser(userData: CreateUserDto): Promise<User> {
    const response = await api.post<User>('/v1/api/users', userData);
    return response.data;
  },

  // Update a user
  async updateUser(id: string, userData: UpdateUserDto): Promise<User> {
    const response = await api.patch<User>(`/v1/api/users/${id}`, userData);
    return response.data;
  },

  // Delete a user
  async deleteUser(id: string): Promise<void> {
    await api.delete(`/v1/api/users/${id}`);
  },

  // Get sessions for a user
  async getUserSessions(id: string): Promise<any[]> { // Assuming 'any[]' for now, will define Session interface later
    const response = await api.get<any[]>(`/v1/api/users/${id}/sessions`);
    return response.data;
  }
};
