import '@testing-library/jest-native/extend-expect';

// Mock expo-router
jest.mock('expo-router', () => ({
  Link: ({ children, href, asChild, ...props }) => {
    const React = require('react');
    if (asChild && React.Children.count(children) === 1) {
      return React.cloneElement(children, {
        ...props,
        testID: `link-${href}`,
        onPress: () => console.log(`Navigate to: ${href}`)
      });
    }
    return React.createElement('View', {
      ...props,
      testID: `link-${href}`,
      onPress: () => console.log(`Navigate to: ${href}`)
    }, children);
  },
  router: {
    push: jest.fn(),
    back: jest.fn(),
    replace: jest.fn(),
  },
  useLocalSearchParams: () => ({}),
  useRouter: () => ({
    push: jest.fn(),
    back: jest.fn(),
    replace: jest.fn(),
  }),
}));

// Mock expo-constants
jest.mock('expo-constants', () => ({
  expoConfig: {
    extra: {
      apiUrl: 'https://test-api.example.com'
    }
  }
}));

// Mock react-native-svg
jest.mock('react-native-svg', () => {
  const React = require('react');
  return {
    Svg: ({ children, ...props }) => React.createElement('View', props, children),
    Path: (props) => React.createElement('View', props),
  };
});

// Mock React Native components that cause issues
jest.mock('react-native/Libraries/TurboModule/TurboModuleRegistry', () => ({
  getEnforcing: jest.fn(() => ({})),
}));

// Mock DevMenu
jest.mock('react-native/src/private/devmenu/DevMenu', () => ({}));

// Global test timeout
jest.setTimeout(10000);
