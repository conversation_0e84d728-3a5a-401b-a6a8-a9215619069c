import { I18nManager } from 'react-native';

/**
 * RTL utility functions for consistent layout handling
 */

export const isRTL = I18nManager.isRTL;

/**
 * Get the appropriate margin/padding direction for RTL
 */
export const rtlStyle = {
  // Margin utilities
  marginLeft: (value: number) => ({
    [isRTL ? 'marginRight' : 'marginLeft']: value,
  }),
  marginRight: (value: number) => ({
    [isRTL ? 'marginLeft' : 'marginRight']: value,
  }),
  marginStart: (value: number) => ({
    marginStart: value,
  }),
  marginEnd: (value: number) => ({
    marginEnd: value,
  }),

  // Padding utilities
  paddingLeft: (value: number) => ({
    [isRTL ? 'paddingRight' : 'paddingLeft']: value,
  }),
  paddingRight: (value: number) => ({
    [isRTL ? 'paddingLeft' : 'paddingRight']: value,
  }),
  paddingStart: (value: number) => ({
    paddingStart: value,
  }),
  paddingEnd: (value: number) => ({
    paddingEnd: value,
  }),

  // Position utilities
  left: (value: number) => ({
    [isRTL ? 'right' : 'left']: value,
  }),
  right: (value: number) => ({
    [isRTL ? 'left' : 'right']: value,
  }),

  // Text alignment
  textAlign: {
    left: isRTL ? 'right' : 'left',
    right: isRTL ? 'left' : 'right',
    start: 'left',
    end: 'right',
  } as const,

  // Flex direction
  flexDirection: {
    row: isRTL ? 'row-reverse' : 'row',
    rowReverse: isRTL ? 'row' : 'row-reverse',
  } as const,

  // Transform utilities
  scaleX: (value: number) => ({
    transform: [{ scaleX: isRTL ? -value : value }],
  }),
};

/**
 * Helper function to get RTL-aware styles
 */
export const getRTLStyle = (ltrStyle: any, rtlStyle?: any) => {
  return isRTL && rtlStyle ? rtlStyle : ltrStyle;
};

/**
 * Helper function for conditional RTL values
 */
export const rtlValue = <T>(ltrValue: T, rtlValue: T): T => {
  return isRTL ? rtlValue : ltrValue;
};

/**
 * Text direction utility
 */
export const textDirection = isRTL ? 'rtl' : 'ltr';

/**
 * Writing direction for text inputs
 */
export const writingDirection = isRTL ? 'rtl' : 'ltr';
