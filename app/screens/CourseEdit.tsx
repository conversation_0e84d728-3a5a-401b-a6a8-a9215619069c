import React, { useEffect, useState } from "react";
import {
  StyleSheet,
  TextInput,
  Pressable,
  Alert,
  ActivityIndicator,
} from "react-native";
import { ThemedText } from "@/components/ThemedText";
import { ThemedView } from "@/components/ThemedView";
import { Course, courseService } from "@/services/courseService";
import { courseGroupService, CourseGroup } from "@/services/courseGroupService";
import KeyboardAvoidingWrapper from "@/components/KeyboardAvoidingWrapper";
import { useLocalSearchParams, router } from "expo-router";
// @ts-ignore
import { Picker } from "@react-native-picker/picker";

export default function CourseEditScreen() {
  const { id } = useLocalSearchParams();
  const [name, setName] = useState("");
  const [description, setDescription] = useState("");
  const [coefficient, setCoefficient] = useState("");
  const [negativeMarkingFactor, setNegativeMarkingFactor] = useState("");
  const [courseGroupId, setCourseGroupId] = useState<string | null>(null);
  const [courseGroups, setCourseGroups] = useState<CourseGroup[]>([]);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchCourseData = async () => {
      if (!id) {
        setError("Course ID not provided for editing.");
        setLoading(false);
        return;
      }

      try {
        const [courseData, groupsData] = await Promise.all([
          courseService.getCourse(id as string),
          courseGroupService.getCourseGroups(),
        ]);

        setName(courseData.name);
        setDescription(courseData.description);
        setCoefficient(courseData.coefficient.toString());
        setNegativeMarkingFactor(courseData.negative_marking_factor.toString());
        setCourseGroups(groupsData);
        setCourseGroupId(courseData.course_group_id.toString());
      } catch (err) {
        setError("Failed to fetch course details or groups for editing.");
        console.error(err);
      } finally {
        setLoading(false);
      }
    };
    fetchCourseData();
  }, [id]);

  const handleUpdateCourse = async () => {
    if (
      !name.trim() ||
      !description.trim() ||
      !coefficient.trim() ||
      !negativeMarkingFactor.trim() ||
      courseGroupId === null
    ) {
      Alert.alert("خطای اعتبارسنجی", "لطفاً تمام فیلدهای الزامی را پر کنید.");
      return;
    }
    if (!id) {
      Alert.alert("خطا", "شناسه درس موجود نیست.");
      return;
    }

    const parsedCoefficient = parseFloat(coefficient);
    const parsedNegativeMarkingFactor = parseFloat(negativeMarkingFactor);
    const parsedCourseGroupId = parseInt(courseGroupId, 10);

    if (
      isNaN(parsedCoefficient) ||
      isNaN(parsedNegativeMarkingFactor) ||
      isNaN(parsedCourseGroupId)
    ) {
      Alert.alert(
        "خطای اعتبارسنجی",
        "لطفاً مقادیر عددی را به درستی وارد کنید."
      );
      return;
    }

    setSaving(true);
    try {
      await courseService.updateCourse(id as string, {
        name,
        description,
        coefficient: parsedCoefficient,
        negative_marking_factor: parsedNegativeMarkingFactor,
        course_group_id: parsedCourseGroupId,
      });
      Alert.alert("موفقیت", "درس با موفقیت به‌روزرسانی شد!");
      router.back();
    } catch (error) {
      Alert.alert("خطا", "به‌روزرسانی درس ناموفق بود. لطفاً دوباره تلاش کنید.");
      console.error("Failed to update course:", error);
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <ThemedView style={styles.centered}>
        <ActivityIndicator size="large" />
        <ThemedText>در حال بارگیری جزئیات درس...</ThemedText>
      </ThemedView>
    );
  }

  if (error) {
    return (
      <ThemedView style={styles.centered}>
        <ThemedText style={styles.errorText}>{error}</ThemedText>
      </ThemedView>
    );
  }

  return (
    <KeyboardAvoidingWrapper style={styles.container}>
      <ThemedText style={styles.title}>ویرایش درس</ThemedText>
      <TextInput
        style={styles.input}
        placeholder="نام درس"
        value={name}
        onChangeText={setName}
        editable={!saving}
      />
      <TextInput
        style={styles.input}
        placeholder="توضیحات درس"
        value={description}
        onChangeText={setDescription}
        editable={!saving}
      />
      <TextInput
        style={styles.input}
        placeholder="ضریب"
        value={coefficient}
        onChangeText={setCoefficient}
        keyboardType="numeric"
        editable={!saving}
      />
      <TextInput
        style={styles.input}
        placeholder="ضریب نمره منفی"
        value={negativeMarkingFactor}
        onChangeText={setNegativeMarkingFactor}
        keyboardType="numeric"
        editable={!saving}
      />
      <ThemedText style={styles.pickerLabel}>گروه درسی:</ThemedText>
      <Picker
        selectedValue={courseGroupId}
        onValueChange={(itemValue: string) => setCourseGroupId(itemValue)}
        style={styles.picker}
        enabled={!saving}
      >
        {courseGroups.map((group) => (
          <Picker.Item key={group.id} label={group.name} value={group.id} />
        ))}
      </Picker>
      <Pressable
        style={[styles.button, saving && styles.buttonDisabled]}
        onPress={handleUpdateCourse}
        disabled={saving}
      >
        <ThemedText style={styles.buttonText}>
          {saving ? "در حال ذخیره..." : "ذخیره تغییرات"}
        </ThemedText>
      </Pressable>
    </KeyboardAvoidingWrapper>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
    justifyContent: "center",
  },
  centered: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  title: {
    fontSize: 24,
    fontWeight: "bold",
    marginBottom: 20,
    textAlign: "center",
  },
  input: {
    height: 40,
    borderColor: "#ccc",
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 10,
    marginBottom: 15,
    backgroundColor: "#fff",
  },
  pickerLabel: {
    fontSize: 16,
    marginBottom: 5,
    color: "#333",
  },
  picker: {
    height: 50,
    width: "100%",
    marginBottom: 15,
    borderColor: "#ccc",
    borderWidth: 1,
    borderRadius: 8,
    backgroundColor: "#fff",
  },
  button: {
    backgroundColor: "#007bff",
    padding: 12,
    borderRadius: 8,
    alignItems: "center",
  },
  buttonText: {
    color: "#fff",
    fontWeight: "bold",
    fontSize: 16,
  },
  buttonDisabled: {
    backgroundColor: "#a0c9ff",
  },
  errorText: {
    color: "red",
    fontSize: 16,
  },
});
