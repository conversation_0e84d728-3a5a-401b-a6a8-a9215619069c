import { api } from './api';

// Define types
export interface Major {
  id: string;
  name: string;
  description: string;
  createdAt?: string;
}

export interface CreateMajorDto {
  name: string;
  description: string;
}

export interface UpdateMajorDto {
  name?: string;
  description?: string;
}

// Major service for handling major-related API calls
export const majorService = {
  // Get all majors
  async getMajors(): Promise<Major[]> {
    const response = await api.get<Major[]>('/v1/api/majors');
    return response.data;
  },

  // Get a specific major
  async getMajor(id: string): Promise<Major> {
    const response = await api.get<Major>(`/v1/api/majors/${id}`);
    return response.data;
  },

  // Create a new major
  async createMajor(majorData: CreateMajorDto): Promise<Major> {
    const response = await api.post<Major>('/v1/api/majors', majorData);
    return response.data;
  },

  // Update a major (PATCH)
  async updateMajor(id: string, majorData: UpdateMajorDto): Promise<Major> {
    const response = await api.patch<Major>(`/v1/api/majors/${id}`, majorData);
    return response.data;
  },

  // Update a major (PUT)
  async updateMajorPut(id: string, majorData: UpdateMajorDto): Promise<Major> {
    const response = await api.put<Major>(`/v1/api/majors/${id}`, majorData);
    return response.data;
  },

  // Delete a major
  async deleteMajor(id: string): Promise<void> {
    await api.delete(`/v1/api/majors/${id}`);
  },

  // Get courses for a major
  async getMajorCourses(id: string): Promise<any[]> { // Assuming 'any[]' for now, will define Course interface later
    const response = await api.get<any[]>(`/v1/api/majors/${id}/courses`);
    return response.data;
  }
};
