const IS_DEV = process.env.APP_VARIANT === "development";
const IS_PREVIEW = process.env.APP_VARIANT === "preview";

// Get API URL from environment or use default
const getApiUrl = () => {
  return process.env.EXPO_PUBLIC_API_URL || "http://192.168.166.95:4000";
};

const getUniqueIdentifier = () => {
  if (IS_DEV) {
    return "ir.tdd_classes.exam.dev";
  }

  if (IS_PREVIEW) {
    return "ir.tdd_classes.exam.preview";
  }

  return "ir.tdd_classes.exam";
};

const getAppName = () => {
  if (IS_DEV) {
    return "TDD Exams (Dev)";
  }

  if (IS_PREVIEW) {
    return "TDD Exams (Preview)";
  }

  return "TDD-Classes.ir | Exams";
};

export default {
  expo: {
    version: "1.0.0",
    orientation: "portrait",
    icon: "./assets/images/icon.png",
    scheme: "azmoon",
    userInterfaceStyle: "automatic",
    newArchEnabled: true,
    supportsRTL: true,

    extra: {
      apiUrl: getApiUrl(),
      eas: {
        projectId: "eed7e2d0-020b-4d51-a942-c027ac937456",
      },
    },
    ios: {
      bundleIdentifier: getUniqueIdentifier(),
    },
    android: {
      package: getUniqueIdentifier(),
      versionCode: 1,
      adaptiveIcon: {
        foregroundImage: "./assets/images/adaptive-icon.png",
        backgroundColor: "#ffffff",
      },
      edgeToEdgeEnabled: true,
    },
    web: {
      bundler: "metro",
      output: "static",
      favicon: "./assets/images/favicon.png",
    },
  },
  name: getAppName(),
  slug: getAppName(),
  android: {
    package: getUniqueIdentifier(),
    versionCode: 1,
    adaptiveIcon: {
      foregroundImage: "./assets/images/adaptive-icon.png",
      backgroundColor: "#ffffff",
    },
    edgeToEdgeEnabled: true,
  },
  plugins: [
    "expo-router",
    [
      "expo-splash-screen",
      {
        image: "./assets/images/splash-icon.png",
        imageWidth: 200,
        resizeMode: "contain",
        backgroundColor: "#ffffff",
      },
    ],
  ],
};
