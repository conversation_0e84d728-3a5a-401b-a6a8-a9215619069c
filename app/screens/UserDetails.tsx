import React, { useEffect, useState } from 'react';
import { ActivityIndicator, StyleSheet } from 'react-native';
import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { User, userService } from '@/services/userService';
import { useLocalSearchParams } from 'expo-router';

export default function UserDetailsScreen() {
  const { id } = useLocalSearchParams();
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (id) {
      const fetchUser = async () => {
        try {
          const data = await userService.getUser(id as string);
          setUser(data);
        } catch (err) {
          setError('Failed to fetch user details.');
          console.error(err);
        } finally {
          setLoading(false);
        }
      };
      fetchUser();
    } else {
      setError('User ID not provided.');
      setLoading(false);
    }
  }, [id]);

  if (loading) {
    return (
      <ThemedView style={styles.centered}>
        <ActivityIndicator size="large" />
        <ThemedText>Loading user details...</ThemedText>
      </ThemedView>
    );
  }

  if (error) {
    return (
      <ThemedView style={styles.centered}>
        <ThemedText style={styles.errorText}>{error}</ThemedText>
      </ThemedView>
    );
  }

  if (!user) {
    return (
      <ThemedView style={styles.centered}>
        <ThemedText>User not found.</ThemedText>
      </ThemedView>
    );
  }

  return (
    <ThemedView style={styles.container}>
      <ThemedText style={styles.title}>User Details</ThemedText>
      <ThemedView style={styles.detailItem}>
        <ThemedText style={styles.label}>ID:</ThemedText>
        <ThemedText>{user.id}</ThemedText>
      </ThemedView>
      {/* Add other user properties here based on your schema */}
      {user.createdAt && (
        <ThemedView style={styles.detailItem}>
          <ThemedText style={styles.label}>Created At:</ThemedText>
          <ThemedText>{new Date(user.createdAt).toLocaleString()}</ThemedText>
        </ThemedView>
      )}
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  centered: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 20,
  },
  detailItem: {
    flexDirection: 'row',
    marginBottom: 10,
  },
  label: {
    fontWeight: 'bold',
    marginRight: 5,
  },
  errorText: {
    color: 'red',
    fontSize: 16,
  },
});
