import React, { useState } from 'react';
import { StyleSheet, SafeAreaView } from 'react-native';
import { Surface } from 'react-native-paper';
import { chapterService } from '@/services/chapterService';
import KeyboardAvoidingWrapper from '@/components/KeyboardAvoidingWrapper';
import { router, useLocalSearchParams } from 'expo-router';
import {
  PaperFormAppBar,
  PaperTextInput,
  PaperButton,
  PaperTitle,
  PaperBody,
  PaperFormField,
  PaperFormSection,
  PaperAlertDialog,
} from '@/components/paper';
import { t } from '@/constants/Localization';

export default function ChapterAddScreen() {
  const { course_id, courseName } = useLocalSearchParams<{ course_id: string, courseName: string }>();
  const [name, setName] = useState('');
  const [description, setDescription] = useState('');
  const [order_in_course, setOrderInCourse] = useState('');
  const [loading, setLoading] = useState(false);
  const [showAlert, setShowAlert] = useState(false);
  const [alertMessage, setAlertMessage] = useState('');
  const [alertTitle, setAlertTitle] = useState('');

  // Form validation
  const [errors, setErrors] = useState({
    name: '',
    description: '',
    order_in_course: '',
  });

  const validateForm = () => {
    const newErrors = {
      name: '',
      description: '',
      order_in_course: '',
    };

    if (!name.trim()) {
      newErrors.name = t('required') || 'این فیلد الزامی است';
    }

    if (!description.trim()) {
      newErrors.description = t('required') || 'این فیلد الزامی است';
    }

    if (!order_in_course.trim()) {
      newErrors.order_in_course = t('required') || 'این فیلد الزامی است';
    } else if (isNaN(parseInt(order_in_course, 10))) {
      newErrors.order_in_course = 'لطفا یک عدد معتبر وارد کنید';
    }

    setErrors(newErrors);
    return !Object.values(newErrors).some(error => error !== '');
  };

  const handleAddChapter = async () => {
    if (!validateForm()) {
      return;
    }

    if (!course_id) {
      setAlertTitle('خطا');
      setAlertMessage('شناسه دوره آموزشی یافت نشد.');
      setShowAlert(true);
      return;
    }

    setLoading(true);
    try {
      await chapterService.createChapter({
        name,
        description,
        order_in_course: parseInt(order_in_course, 10),
        course_id: parseInt(course_id, 10),
      });
      setAlertTitle(t('success'));
      setAlertMessage('فصل با موفقیت اضافه شد!');
      setShowAlert(true);
      // Navigate back after showing success message
      setTimeout(() => {
        router.back();
      }, 1500);
    } catch (error) {
      setAlertTitle(t('error'));
      setAlertMessage('فصل اضافه نشد. لطفا دوباره امتحان کنید.');
      setShowAlert(true);
      console.error('Failed to add chapter:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      {/* App Bar */}
      <PaperFormAppBar
        title={t('addChapter') || 'اضافه کردن فصل جدید'}
        onSavePress={handleAddChapter}
        saveDisabled={loading}
      />

      {/* Content */}
      <Surface style={styles.content}>
        <KeyboardAvoidingWrapper>
          <PaperFormSection>
            {courseName && (
              <PaperBody style={styles.courseInfo}>
                دوره: {courseName} (شناسه: {course_id})
              </PaperBody>
            )}

            <PaperFormField
              label={t('chapterTitle') || 'نام فصل'}
              error={errors.name}
              required
            >
              <PaperTextInput
                value={name}
                onChangeText={setName}
                placeholder="نام فصل را وارد کنید"
                disabled={loading}
                error={!!errors.name}
              />
            </PaperFormField>

            <PaperFormField
              label={t('chapterDescription') || 'توضیحات'}
              error={errors.description}
              required
            >
              <PaperTextInput
                value={description}
                onChangeText={setDescription}
                placeholder="توضیحات فصل را وارد کنید"
                disabled={loading}
                multiline
                numberOfLines={3}
                error={!!errors.description}
              />
            </PaperFormField>

            <PaperFormField
              label={t('chapterNumber') || 'ترتیب در دوره'}
              error={errors.order_in_course}
              required
            >
              <PaperTextInput
                value={order_in_course}
                onChangeText={setOrderInCourse}
                placeholder="شماره ترتیب فصل"
                disabled={loading}
                keyboardType="numeric"
                error={!!errors.order_in_course}
              />
            </PaperFormField>

            <PaperButton
              title={loading ? 'در حال اضافه کردن...' : t('addChapter') || 'اضافه کردن فصل'}
              onPress={handleAddChapter}
              disabled={loading}
              loading={loading}
              mode="contained"
              style={styles.submitButton}
              icon="plus"
            />
          </PaperFormSection>
        </KeyboardAvoidingWrapper>
      </Surface>

      {/* Alert Dialog */}
      <PaperAlertDialog
        visible={showAlert}
        onDismiss={() => setShowAlert(false)}
        title={alertTitle}
        message={alertMessage}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  courseInfo: {
    textAlign: 'center',
    fontStyle: 'italic',
    marginBottom: 16,
  },
  submitButton: {
    marginTop: 24,
  },
});
