import React, { useEffect, useState } from "react";
import { StyleSheet, SafeAreaView } from "react-native";
import { Surface } from "react-native-paper";
import { Session, sessionService } from "@/services/sessionService";
import { router } from "expo-router";
import {
  PaperListAppBar,
  PaperRefreshableFlatList,
  PaperCard,
  PaperCardTitle,
  PaperCardActions,
  PaperButton,
  PaperTitle,
  PaperBody,
  PaperAddFAB,
  useRefresh,
} from "@/components/paper";
import { t } from "@/constants/Localization";
import { useApiError } from "@/hooks/useApiError";

export default function SessionsListScreen() {
  const [sessions, setSessions] = useState<Session[]>([]);
  const [loading, setLoading] = useState(true);
  const { handleApiError } = useApiError();

  const loadSessions = async () => {
    try {
      setLoading(true);
      const data = await sessionService.getSessions();
      setSessions(data);
    } catch (err) {
      handleApiError(err, "خطا در دریافت جلسات");
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  const { refreshing, onRefresh } = useRefresh(loadSessions);

  useEffect(() => {
    loadSessions();
  }, []);

  // Navigation handlers
  const handleAddSession = () => {
    router.push('/screens/SessionAdd');
  };

  const handleSessionPress = (session: Session) => {
    router.push(`/screens/SessionDetails?id=${session.id}`);
  };

  const handleViewSession = (session: Session) => {
    router.push(`/screens/SessionDetails?id=${session.id}`);
  };

  const renderSessionItem = ({ item }: { item: Session }) => (
    <PaperCard
      style={styles.sessionCard}
      onPress={() => handleSessionPress(item)}
    >
      <PaperCardTitle
        title={`جلسه #${item.id}`}
        subtitle={`آزمون: ${item.exam?.name || 'نامشخص'} | دانشجو: ${item.user?.first_name || ''} ${item.user?.last_name || ''}`}
        left={(props) => (
          <Surface {...props} style={styles.sessionIcon}>
            <PaperTitle size="small">S</PaperTitle>
          </Surface>
        )}
      />
      <PaperCardActions>
        <PaperButton
          title={'مشاهده'}
          mode="outlined"
          onPress={() => handleViewSession(item)}
          icon="eye"
          compact
        />
      </PaperCardActions>
    </PaperCard>
  );

  return (
    <SafeAreaView style={styles.container}>
      {/* App Bar */}
      <PaperListAppBar
        title={t('sessions') || 'جلسات'}
        onAddPress={handleAddSession}
      />

      {/* Content */}
      <Surface style={styles.content}>
        <PaperRefreshableFlatList
          data={sessions}
          renderItem={renderSessionItem}
          keyExtractor={(item) => item.id}
          refreshing={refreshing}
          onRefresh={onRefresh}
          loading={loading}
          style={styles.list}
          contentContainerStyle={styles.listContent}
          emptyTitle={'هیچ جلسه‌ای یافت نشد'}
          emptyDescription={'برای افزودن جلسه جدید از دکمه + استفاده کنید'}
          emptyIcon="calendar-clock"
          emptyAction={
            <PaperButton
              title={t('addSession') || 'افزودن جلسه'}
              mode="contained"
              onPress={handleAddSession}
              icon="plus"
            />
          }
        />
      </Surface>

      {/* Floating Action Button */}
      <PaperAddFAB
        onPress={handleAddSession}
        visible={!loading}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
  },
  list: {
    flex: 1,
  },
  listContent: {
    padding: 16,
  },
  sessionCard: {
    marginVertical: 8,
  },
  sessionIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#fff3e0',
  },
});
