import React, { useEffect, useState } from 'react';
import { StyleSheet, SafeAreaView } from 'react-native';
import { Surface, ActivityIndicator } from 'react-native-paper';
import { User, userService } from '@/services/userService';
import { useLocalSearchParams, router } from 'expo-router';
import {
  PaperDetailsAppBar,
  PaperCard,
  PaperCardTitle,
  PaperCardActions,
  PaperButton,
  PaperTitle,
  PaperBody,
  PaperLabel,
  PaperText,
} from '@/components/paper';
import { t } from '@/constants/Localization';
import { useApiError } from '@/hooks/useApiError';

export default function UserDetailsScreen() {
  const { id } = useLocalSearchParams();
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const { handleApiError } = useApiError();

  useEffect(() => {
    if (id) {
      const fetchUser = async () => {
        try {
          const data = await userService.getUser(id as string);
          setUser(data);
        } catch (err) {
          handleApiError(err, 'خطا در دریافت جزئیات کاربر');
          console.error(err);
        } finally {
          setLoading(false);
        }
      };
      fetchUser();
    } else {
      handleApiError(new Error('User ID not provided'), 'شناسه کاربر ارائه نشده');
      setLoading(false);
    }
  }, [id]);

  // Navigation handlers
  const handleEditUser = () => {
    router.push(`/screens/UserEdit?id=${id}`);
  };

  const handleBack = () => {
    router.back();
  };

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <PaperDetailsAppBar
          title={t('userDetails') || 'جزئیات کاربر'}
          onBackPress={handleBack}
        />
        <Surface style={styles.centered}>
          <ActivityIndicator size="large" />
          <PaperBody style={styles.loadingText}>در حال بارگذاری جزئیات کاربر...</PaperBody>
        </Surface>
      </SafeAreaView>
    );
  }

  if (!user) {
    return (
      <SafeAreaView style={styles.container}>
        <PaperDetailsAppBar
          title={t('userDetails') || 'جزئیات کاربر'}
          onBackPress={handleBack}
        />
        <Surface style={styles.centered}>
          <PaperBody>کاربر یافت نشد</PaperBody>
        </Surface>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      {/* App Bar */}
      <PaperDetailsAppBar
        title={t('userDetails') || 'جزئیات کاربر'}
        onBackPress={handleBack}
        onEditPress={handleEditUser}
      />

      {/* Content */}
      <Surface style={styles.content}>
        <PaperCard style={styles.detailCard}>
          <PaperCardTitle
            title={`${user.first_name} ${user.last_name}`}
            subtitle={user.role}
            left={(props) => (
              <Surface {...props} style={styles.userIcon}>
                <PaperTitle size="small">{user.first_name.charAt(0)}</PaperTitle>
              </Surface>
            )}
          />

          <Surface style={styles.cardContent}>
            <PaperLabel style={styles.sectionTitle}>اطلاعات حساب کاربری</PaperLabel>

            <Surface style={styles.detailRow}>
              <PaperLabel style={styles.label}>شناسه:</PaperLabel>
              <PaperBody>{user.id}</PaperBody>
            </Surface>

            <Surface style={styles.detailRow}>
              <PaperLabel style={styles.label}>ایمیل:</PaperLabel>
              <PaperBody>{user.email}</PaperBody>
            </Surface>

            <Surface style={styles.detailRow}>
              <PaperLabel style={styles.label}>موبایل:</PaperLabel>
              <PaperBody>{user.mobile}</PaperBody>
            </Surface>

            <Surface style={styles.detailRow}>
              <PaperLabel style={styles.label}>نقش:</PaperLabel>
              <PaperBody>{user.role}</PaperBody>
            </Surface>

            {user.createdAt && (
              <Surface style={styles.detailRow}>
                <PaperLabel style={styles.label}>تاریخ ایجاد:</PaperLabel>
                <PaperBody>{new Date(user.createdAt).toLocaleDateString('fa-IR')}</PaperBody>
              </Surface>
            )}
          </Surface>

          <PaperCardActions>
            <PaperButton
              title={t('edit') || 'ویرایش'}
              mode="contained"
              onPress={handleEditUser}
              icon="pencil"
            />
          </PaperCardActions>
        </PaperCard>
      </Surface>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  centered: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 16,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  detailCard: {
    marginBottom: 16,
  },
  cardContent: {
    padding: 16,
  },
  sectionTitle: {
    marginBottom: 16,
    fontWeight: 'bold',
  },
  detailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
    paddingVertical: 4,
  },
  label: {
    fontWeight: 'bold',
    flex: 1,
  },
  userIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#e3f2fd',
  },
  loadingText: {
    marginTop: 16,
  },
});
