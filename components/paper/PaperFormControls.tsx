import React from 'react';
import { 
  Checkbox, 
  Switch, 
  RadioButton, 
  SegmentedButtons,
  useTheme 
} from 'react-native-paper';
import { StyleSheet, View, type ViewStyle } from 'react-native';
import { PaperTextComponent } from './PaperText';

// Checkbox Component
export interface PaperCheckboxProps {
  status: 'checked' | 'unchecked' | 'indeterminate';
  onPress: () => void;
  label?: string;
  disabled?: boolean;
  style?: ViewStyle;
  labelStyle?: any;
}

export function PaperCheckboxComponent({
  status,
  onPress,
  label,
  disabled = false,
  style,
  labelStyle,
}: PaperCheckboxProps) {
  return (
    <View style={[styles.checkboxContainer, style]}>
      <Checkbox
        status={status}
        onPress={onPress}
        disabled={disabled}
      />
      {label && (
        <PaperTextComponent 
          variant="bodyMedium" 
          style={[styles.checkboxLabel, labelStyle]}
          onPress={disabled ? undefined : onPress}
        >
          {label}
        </PaperTextComponent>
      )}
    </View>
  );
}

// Switch Component
export interface PaperSwitchProps {
  value: boolean;
  onValueChange: (value: boolean) => void;
  label?: string;
  disabled?: boolean;
  style?: ViewStyle;
  labelStyle?: any;
}

export function PaperSwitchComponent({
  value,
  onValueChange,
  label,
  disabled = false,
  style,
  labelStyle,
}: PaperSwitchProps) {
  return (
    <View style={[styles.switchContainer, style]}>
      {label && (
        <PaperTextComponent 
          variant="bodyMedium" 
          style={[styles.switchLabel, labelStyle]}
        >
          {label}
        </PaperTextComponent>
      )}
      <Switch
        value={value}
        onValueChange={onValueChange}
        disabled={disabled}
      />
    </View>
  );
}

// Radio Button Group Component
export interface PaperRadioOption {
  label: string;
  value: string;
  disabled?: boolean;
}

export interface PaperRadioGroupProps {
  options: PaperRadioOption[];
  value: string;
  onValueChange: (value: string) => void;
  style?: ViewStyle;
  labelStyle?: any;
}

export function PaperRadioGroup({
  options,
  value,
  onValueChange,
  style,
  labelStyle,
}: PaperRadioGroupProps) {
  return (
    <RadioButton.Group onValueChange={onValueChange} value={value}>
      <View style={[styles.radioGroup, style]}>
        {options.map((option) => (
          <View key={option.value} style={styles.radioItem}>
            <RadioButton
              value={option.value}
              disabled={option.disabled}
            />
            <PaperTextComponent 
              variant="bodyMedium" 
              style={[styles.radioLabel, labelStyle]}
              onPress={() => !option.disabled && onValueChange(option.value)}
            >
              {option.label}
            </PaperTextComponent>
          </View>
        ))}
      </View>
    </RadioButton.Group>
  );
}

// Segmented Buttons Component
export interface PaperSegmentedButtonsProps {
  value: string;
  onValueChange: (value: string) => void;
  buttons: Array<{
    value: string;
    label: string;
    icon?: string;
    disabled?: boolean;
    accessibilityLabel?: string;
  }>;
  multiSelect?: boolean;
  density?: 'regular' | 'small' | 'medium' | 'high';
  style?: ViewStyle;
}

export function PaperSegmentedButtonsComponent({
  value,
  onValueChange,
  buttons,
  multiSelect = false,
  density = 'regular',
  style,
}: PaperSegmentedButtonsProps) {
  return (
    <SegmentedButtons
      value={value}
      onValueChange={onValueChange}
      buttons={buttons}
      multiSelect={multiSelect}
      density={density}
      style={[styles.segmentedButtons, style]}
    />
  );
}

// Form Field Wrapper
export interface PaperFormFieldProps {
  children: React.ReactNode;
  label?: string;
  error?: string;
  required?: boolean;
  style?: ViewStyle;
}

export function PaperFormField({
  children,
  label,
  error,
  required = false,
  style,
}: PaperFormFieldProps) {
  const theme = useTheme();

  return (
    <View style={[styles.formField, style]}>
      {label && (
        <PaperTextComponent 
          variant="labelMedium" 
          style={[
            styles.fieldLabel,
            { color: theme.colors.onSurface }
          ]}
        >
          {label}
          {required && (
            <PaperTextComponent 
              variant="labelMedium" 
              style={{ color: theme.colors.error }}
            >
              {' *'}
            </PaperTextComponent>
          )}
        </PaperTextComponent>
      )}
      {children}
      {error && (
        <PaperTextComponent 
          variant="bodySmall" 
          style={[
            styles.fieldError,
            { color: theme.colors.error }
          ]}
        >
          {error}
        </PaperTextComponent>
      )}
    </View>
  );
}

// Form Section
export interface PaperFormSectionProps {
  title?: string;
  children: React.ReactNode;
  style?: ViewStyle;
}

export function PaperFormSection({
  title,
  children,
  style,
}: PaperFormSectionProps) {
  return (
    <View style={[styles.formSection, style]}>
      {title && (
        <PaperTextComponent 
          variant="titleMedium" 
          style={styles.sectionTitle}
        >
          {title}
        </PaperTextComponent>
      )}
      {children}
    </View>
  );
}

const styles = StyleSheet.create({
  checkboxContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
  },
  checkboxLabel: {
    marginLeft: 8,
    flex: 1,
    textAlign: 'right',
  },
  switchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 8,
  },
  switchLabel: {
    flex: 1,
    textAlign: 'right',
    marginRight: 16,
  },
  radioGroup: {
    paddingVertical: 8,
  },
  radioItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 4,
  },
  radioLabel: {
    marginLeft: 8,
    flex: 1,
    textAlign: 'right',
  },
  segmentedButtons: {
    marginVertical: 8,
  },
  formField: {
    marginVertical: 8,
  },
  fieldLabel: {
    marginBottom: 4,
    fontWeight: '500',
  },
  fieldError: {
    marginTop: 4,
  },
  formSection: {
    marginVertical: 16,
  },
  sectionTitle: {
    marginBottom: 16,
    fontWeight: '600',
  },
});

// Export individual components
export {
  PaperCheckboxComponent as PaperCheckbox,
  PaperSwitchComponent as PaperSwitch,
  PaperSegmentedButtonsComponent as PaperSegmentedButtons,
};
