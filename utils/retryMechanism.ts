import { handleError, ErrorInfo, ErrorType } from './errorHandler';
import { t } from '@/constants/Localization';

// Retry configuration
export interface RetryConfig {
  maxAttempts: number;
  baseDelay: number; // Base delay in milliseconds
  maxDelay: number; // Maximum delay in milliseconds
  backoffFactor: number; // Exponential backoff multiplier
  retryCondition?: (error: any, attempt: number) => boolean;
  onRetry?: (error: any, attempt: number) => void;
  onMaxRetriesReached?: (error: any) => void;
}

// Default retry configuration
export const DEFAULT_RETRY_CONFIG: RetryConfig = {
  maxAttempts: 3,
  baseDelay: 1000, // 1 second
  maxDelay: 10000, // 10 seconds
  backoffFactor: 2,
};

// Retry result
export interface RetryResult<T> {
  success: boolean;
  data?: T;
  error?: any;
  attempts: number;
  totalTime: number;
}

/**
 * Calculate delay for exponential backoff
 */
function calculateDelay(attempt: number, config: RetryConfig): number {
  const delay = config.baseDelay * Math.pow(config.backoffFactor, attempt - 1);
  return Math.min(delay, config.maxDelay);
}

/**
 * Add jitter to delay to avoid thundering herd problem
 */
function addJitter(delay: number): number {
  // Add random jitter of ±25%
  const jitter = delay * 0.25 * (Math.random() * 2 - 1);
  return Math.max(0, delay + jitter);
}

/**
 * Default retry condition - determines if an error should be retried
 */
function defaultRetryCondition(error: any, attempt: number): boolean {
  const errorInfo = handleError(error);
  return errorInfo.canRetry;
}

/**
 * Sleep function for delays
 */
function sleep(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * Retry mechanism with exponential backoff
 */
export async function withRetry<T>(
  operation: () => Promise<T>,
  config: Partial<RetryConfig> = {}
): Promise<RetryResult<T>> {
  const finalConfig: RetryConfig = {
    ...DEFAULT_RETRY_CONFIG,
    ...config,
    retryCondition: config.retryCondition || defaultRetryCondition,
  };

  const startTime = Date.now();
  let lastError: any;
  let attempts = 0;

  for (let attempt = 1; attempt <= finalConfig.maxAttempts; attempt++) {
    attempts = attempt;
    
    try {
      const result = await operation();
      const totalTime = Date.now() - startTime;
      
      return {
        success: true,
        data: result,
        attempts,
        totalTime,
      };
    } catch (error) {
      lastError = error;
      
      // Check if we should retry this error
      const shouldRetry = finalConfig.retryCondition!(error, attempt);
      
      // If this is the last attempt or we shouldn't retry, break
      if (attempt >= finalConfig.maxAttempts || !shouldRetry) {
        break;
      }
      
      // Call onRetry callback if provided
      finalConfig.onRetry?.(error, attempt);
      
      // Calculate delay with jitter
      const delay = calculateDelay(attempt, finalConfig);
      const jitteredDelay = addJitter(delay);
      
      // Wait before retrying
      await sleep(jitteredDelay);
    }
  }

  // All retries failed
  const totalTime = Date.now() - startTime;
  
  // Call onMaxRetriesReached callback if provided
  finalConfig.onMaxRetriesReached?.(lastError);
  
  return {
    success: false,
    error: lastError,
    attempts,
    totalTime,
  };
}

/**
 * Create a retryable version of an async function
 */
export function createRetryableFunction<T extends any[], R>(
  fn: (...args: T) => Promise<R>,
  config: Partial<RetryConfig> = {}
): (...args: T) => Promise<R> {
  return async (...args: T): Promise<R> => {
    const result = await withRetry(() => fn(...args), config);
    
    if (result.success) {
      return result.data!;
    } else {
      throw result.error;
    }
  };
}

/**
 * Retry configuration presets for common scenarios
 */
export const RetryPresets = {
  // Quick retry for user interactions
  QUICK: {
    maxAttempts: 2,
    baseDelay: 500,
    maxDelay: 2000,
    backoffFactor: 1.5,
  } as Partial<RetryConfig>,

  // Standard retry for API calls
  STANDARD: {
    maxAttempts: 3,
    baseDelay: 1000,
    maxDelay: 5000,
    backoffFactor: 2,
  } as Partial<RetryConfig>,

  // Aggressive retry for critical operations
  AGGRESSIVE: {
    maxAttempts: 5,
    baseDelay: 1000,
    maxDelay: 15000,
    backoffFactor: 2,
  } as Partial<RetryConfig>,

  // Network-specific retry
  NETWORK: {
    maxAttempts: 4,
    baseDelay: 2000,
    maxDelay: 10000,
    backoffFactor: 1.8,
    retryCondition: (error: any) => {
      const errorInfo = handleError(error);
      return errorInfo.type === ErrorType.NETWORK || errorInfo.type === ErrorType.TIMEOUT;
    },
  } as Partial<RetryConfig>,
};

/**
 * Utility class for managing retry operations
 */
export class RetryManager {
  private activeRetries = new Map<string, AbortController>();

  /**
   * Execute operation with retry and cancellation support
   */
  async executeWithRetry<T>(
    key: string,
    operation: (signal?: AbortSignal) => Promise<T>,
    config: Partial<RetryConfig> = {}
  ): Promise<RetryResult<T>> {
    // Cancel any existing retry for this key
    this.cancelRetry(key);

    // Create abort controller for this retry
    const abortController = new AbortController();
    this.activeRetries.set(key, abortController);

    try {
      const result = await withRetry(
        () => operation(abortController.signal),
        config
      );

      return result;
    } finally {
      // Clean up
      this.activeRetries.delete(key);
    }
  }

  /**
   * Cancel a specific retry operation
   */
  cancelRetry(key: string): void {
    const controller = this.activeRetries.get(key);
    if (controller) {
      controller.abort();
      this.activeRetries.delete(key);
    }
  }

  /**
   * Cancel all active retries
   */
  cancelAllRetries(): void {
    for (const [key, controller] of this.activeRetries) {
      controller.abort();
    }
    this.activeRetries.clear();
  }

  /**
   * Get number of active retries
   */
  getActiveRetryCount(): number {
    return this.activeRetries.size;
  }

  /**
   * Check if a specific retry is active
   */
  isRetryActive(key: string): boolean {
    return this.activeRetries.has(key);
  }
}

// Global retry manager instance
export const globalRetryManager = new RetryManager();

/**
 * Higher-order function to add retry capability to any async function
 */
export function withRetryCapability<T extends any[], R>(
  fn: (...args: T) => Promise<R>,
  config: Partial<RetryConfig> = {}
) {
  return {
    // Execute with retry
    withRetry: (...args: T) => createRetryableFunction(fn, config)(...args),
    
    // Execute without retry (original function)
    withoutRetry: fn,
    
    // Execute with custom config
    withConfig: (customConfig: Partial<RetryConfig>) => 
      createRetryableFunction(fn, { ...config, ...customConfig }),
  };
}
