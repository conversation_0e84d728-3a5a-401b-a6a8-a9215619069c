import React from 'react';
import { Appbar, useTheme } from 'react-native-paper';
import { StyleSheet, type ViewStyle } from 'react-native';
import { router } from 'expo-router';

export interface PaperAppBarProps {
  title: string;
  subtitle?: string;
  showBackAction?: boolean;
  onBackPress?: () => void;
  actions?: Array<{
    icon: string;
    onPress: () => void;
    accessibilityLabel?: string;
  }>;
  style?: ViewStyle;
  elevated?: boolean;
  mode?: 'small' | 'medium' | 'large' | 'center-aligned';
}

export function PaperAppBarComponent({
  title,
  subtitle,
  showBackAction = true,
  onBackPress,
  actions = [],
  style,
  elevated = true,
  mode = 'small',
}: PaperAppBarProps) {
  const theme = useTheme();

  const handleBackPress = () => {
    if (onBackPress) {
      onBackPress();
    } else {
      router.back();
    }
  };

  return (
    <Appbar.Header 
      style={[styles.appBar, style]}
      elevated={elevated}
      mode={mode}
    >
      {showBackAction && (
        <Appbar.BackAction 
          onPress={handleBackPress}
          accessibilityLabel="بازگشت"
        />
      )}
      
      <Appbar.Content 
        title={title}
        subtitle={subtitle}
        titleStyle={styles.title}
        subtitleStyle={styles.subtitle}
      />
      
      {actions.map((action, index) => (
        <Appbar.Action
          key={index}
          icon={action.icon}
          onPress={action.onPress}
          accessibilityLabel={action.accessibilityLabel}
        />
      ))}
    </Appbar.Header>
  );
}

// Specialized AppBar components
export function PaperListAppBar({
  title,
  onAddPress,
  onSearchPress,
  showSearch = true,
  showAdd = true,
  ...props
}: Omit<PaperAppBarProps, 'actions'> & {
  onAddPress?: () => void;
  onSearchPress?: () => void;
  showSearch?: boolean;
  showAdd?: boolean;
}) {
  const actions = [];
  
  if (showSearch && onSearchPress) {
    actions.push({
      icon: 'magnify',
      onPress: onSearchPress,
      accessibilityLabel: 'جستجو',
    });
  }
  
  if (showAdd && onAddPress) {
    actions.push({
      icon: 'plus',
      onPress: onAddPress,
      accessibilityLabel: 'افزودن',
    });
  }

  return (
    <PaperAppBarComponent
      title={title}
      actions={actions}
      {...props}
    />
  );
}

export function PaperFormAppBar({
  title,
  onSavePress,
  onCancelPress,
  saveDisabled = false,
  showSave = true,
  showCancel = false,
  ...props
}: Omit<PaperAppBarProps, 'actions'> & {
  onSavePress?: () => void;
  onCancelPress?: () => void;
  saveDisabled?: boolean;
  showSave?: boolean;
  showCancel?: boolean;
}) {
  const actions = [];
  
  if (showCancel && onCancelPress) {
    actions.push({
      icon: 'close',
      onPress: onCancelPress,
      accessibilityLabel: 'لغو',
    });
  }
  
  if (showSave && onSavePress) {
    actions.push({
      icon: 'check',
      onPress: onSavePress,
      accessibilityLabel: 'ذخیره',
    });
  }

  return (
    <PaperAppBarComponent
      title={title}
      actions={actions}
      {...props}
    />
  );
}

export function PaperDetailsAppBar({
  title,
  onEditPress,
  onDeletePress,
  onSharePress,
  showEdit = true,
  showDelete = true,
  showShare = false,
  ...props
}: Omit<PaperAppBarProps, 'actions'> & {
  onEditPress?: () => void;
  onDeletePress?: () => void;
  onSharePress?: () => void;
  showEdit?: boolean;
  showDelete?: boolean;
  showShare?: boolean;
}) {
  const actions = [];
  
  if (showShare && onSharePress) {
    actions.push({
      icon: 'share',
      onPress: onSharePress,
      accessibilityLabel: 'اشتراک‌گذاری',
    });
  }
  
  if (showEdit && onEditPress) {
    actions.push({
      icon: 'pencil',
      onPress: onEditPress,
      accessibilityLabel: 'ویرایش',
    });
  }
  
  if (showDelete && onDeletePress) {
    actions.push({
      icon: 'delete',
      onPress: onDeletePress,
      accessibilityLabel: 'حذف',
    });
  }

  return (
    <PaperAppBarComponent
      title={title}
      actions={actions}
      {...props}
    />
  );
}

const styles = StyleSheet.create({
  appBar: {
    // Custom styling if needed
  },
  title: {
    fontFamily: 'Vazirmatn',
    textAlign: 'right',
    fontSize: 20,
    fontWeight: '600',
  },
  subtitle: {
    fontFamily: 'Vazirmatn',
    textAlign: 'right',
    fontSize: 14,
  },
});

// Export as default for easier importing
export default PaperAppBarComponent;
