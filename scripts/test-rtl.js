/**
 * Test script to verify RTL configuration
 * Run with: node scripts/test-rtl.js
 */

console.log('Testing RTL Configuration...');

// Simulate React Native I18nManager
const mockI18nManager = {
  isRTL: false,
  allowRTL: function(allow) {
    console.log(`I18nManager.allowRTL(${allow}) called`);
    return true;
  },
  forceRTL: function(force) {
    console.log(`I18nManager.forceRTL(${force}) called`);
    this.isRTL = force;
    return true;
  }
};

// Test the configuration
console.log('Initial RTL status:', mockI18nManager.isRTL);

mockI18nManager.allowRTL(true);
mockI18nManager.forceRTL(true);

console.log('Final RTL status:', mockI18nManager.isRTL);
console.log('Drawer position should be:', mockI18nManager.isRTL ? 'right' : 'left');

console.log('\n✅ RTL configuration test completed');
console.log('📱 Now restart your app to see the changes');
console.log('🔄 Use: npm start or expo start');
