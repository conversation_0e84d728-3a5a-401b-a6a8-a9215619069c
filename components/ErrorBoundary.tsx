import React, { Component, ReactNode } from 'react';
import { View, StyleSheet } from 'react-native';
import { PaperText, PaperButton, PaperCard } from '@/components/paper';
import { t } from '@/constants/Localization';
import { handleError, logError } from '@/utils/errorHandler';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: React.ErrorInfo) => void;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: React.ErrorInfo | null;
}

/**
 * Error Boundary component to catch and handle React errors gracefully
 */
export class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
    };
  }

  static getDerivedStateFromError(error: Error): State {
    // Update state so the next render will show the fallback UI
    return {
      hasError: true,
      error,
      errorInfo: null,
    };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    // Log the error
    const errorDetails = handleError(error);
    logError(errorDetails, 'React Error Boundary');
    
    // Update state with error info
    this.setState({
      error,
      errorInfo,
    });

    // Call custom error handler if provided
    this.props.onError?.(error, errorInfo);
  }

  handleRetry = () => {
    // Reset error state to retry rendering
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
    });
  };

  render() {
    if (this.state.hasError) {
      // Custom fallback UI
      if (this.props.fallback) {
        return this.props.fallback;
      }

      // Default error UI
      return (
        <View style={styles.container}>
          <PaperCard style={styles.errorCard}>
            <PaperCard.Content>
              <PaperText variant="headlineSmall" style={styles.errorTitle}>
                {t('error')}
              </PaperText>
              
              <PaperText variant="bodyMedium" style={styles.errorMessage}>
                {t('unknownError')}
              </PaperText>
              
              {__DEV__ && this.state.error && (
                <View style={styles.debugInfo}>
                  <PaperText variant="bodySmall" style={styles.debugTitle}>
                    Debug Information:
                  </PaperText>
                  <PaperText variant="bodySmall" style={styles.debugText}>
                    {this.state.error.message}
                  </PaperText>
                  {this.state.error.stack && (
                    <PaperText variant="bodySmall" style={styles.debugText}>
                      {this.state.error.stack}
                    </PaperText>
                  )}
                </View>
              )}
              
              <View style={styles.buttonContainer}>
                <PaperButton
                  title={t('retry')}
                  onPress={this.handleRetry}
                  mode="contained"
                  style={styles.retryButton}
                />
              </View>
            </PaperCard.Content>
          </PaperCard>
        </View>
      );
    }

    return this.props.children;
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 16,
    backgroundColor: '#f5f5f5',
  },
  errorCard: {
    width: '100%',
    maxWidth: 400,
    elevation: 4,
  },
  errorTitle: {
    textAlign: 'center',
    marginBottom: 16,
    color: '#d32f2f',
    fontFamily: 'Vazirmatn',
  },
  errorMessage: {
    textAlign: 'center',
    marginBottom: 24,
    fontFamily: 'Vazirmatn',
  },
  debugInfo: {
    marginTop: 16,
    padding: 12,
    backgroundColor: '#f5f5f5',
    borderRadius: 8,
  },
  debugTitle: {
    fontWeight: 'bold',
    marginBottom: 8,
    fontFamily: 'Vazirmatn',
  },
  debugText: {
    fontFamily: 'monospace',
    fontSize: 12,
    marginBottom: 4,
  },
  buttonContainer: {
    alignItems: 'center',
    marginTop: 16,
  },
  retryButton: {
    minWidth: 120,
  },
});

/**
 * Higher-order component to wrap components with error boundary
 */
export function withErrorBoundary<P extends object>(
  Component: React.ComponentType<P>,
  fallback?: ReactNode,
  onError?: (error: Error, errorInfo: React.ErrorInfo) => void
) {
  const WrappedComponent = (props: P) => (
    <ErrorBoundary fallback={fallback} onError={onError}>
      <Component {...props} />
    </ErrorBoundary>
  );

  WrappedComponent.displayName = `withErrorBoundary(${Component.displayName || Component.name})`;
  
  return WrappedComponent;
}

/**
 * Hook to handle errors in functional components
 */
export function useErrorHandler() {
  const [error, setError] = React.useState<Error | null>(null);

  const handleError = React.useCallback((error: Error) => {
    setError(error);
  }, []);

  const clearError = React.useCallback(() => {
    setError(null);
  }, []);

  // Throw error to be caught by Error Boundary
  if (error) {
    throw error;
  }

  return { handleError, clearError };
}

/**
 * Custom error boundary for specific error types
 */
interface NetworkErrorBoundaryProps {
  children: ReactNode;
  onRetry?: () => void;
}

export function NetworkErrorBoundary({ children, onRetry }: NetworkErrorBoundaryProps) {
  return (
    <ErrorBoundary
      fallback={
        <View style={styles.container}>
          <PaperCard style={styles.errorCard}>
            <PaperCard.Content>
              <PaperText variant="headlineSmall" style={styles.errorTitle}>
                {t('connectionError')}
              </PaperText>
              
              <PaperText variant="bodyMedium" style={styles.errorMessage}>
                {t('checkConnection')}
              </PaperText>
              
              <View style={styles.buttonContainer}>
                <PaperButton
                  title={t('retry')}
                  onPress={onRetry}
                  mode="contained"
                  style={styles.retryButton}
                />
              </View>
            </PaperCard.Content>
          </PaperCard>
        </View>
      }
    >
      {children}
    </ErrorBoundary>
  );
}
