import React from "react";
import {
  View,
  StyleSheet,
  SafeAreaView,
  ScrollView,
} from "react-native";
import { DrawerContentComponentProps } from "@react-navigation/drawer";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import { router } from "expo-router";
import {
  Drawer,
  Avatar,
  Text,
  Divider,
  useTheme,
  IconButton,
} from "react-native-paper";

import { rtlStyle } from "@/utils/rtl";
import { useColorScheme } from "@/hooks/useColorScheme";
import { Colors } from "@/constants/Colors";
import { PaperConfirmDialog } from "./paper/PaperDialog";
import { t } from "@/constants/Localization";

interface DrawerItemProps {
  icon: string;
  label: string;
  onPress: () => void;
  isActive?: boolean;
}

const DrawerItem: React.FC<DrawerItemProps> = ({
  icon,
  label,
  onPress,
  isActive,
}) => (
  <Drawer.Item
    icon={icon}
    label={label}
    onPress={onPress}
    active={isActive}
    style={styles.drawerItem}
    labelStyle={styles.drawerItemText}
  />
);

export function CustomDrawerContent(props: DrawerContentComponentProps) {
  const colorScheme = useColorScheme() ?? "light";
  const insets = useSafeAreaInsets();
  const theme = useTheme();
  const [showLogoutDialog, setShowLogoutDialog] = React.useState(false);

  const handleLogout = () => {
    setShowLogoutDialog(true);
  };

  const confirmLogout = () => {
    // Implement logout logic here
    console.log("User logged out");
  };

  return (
    <>
      <SafeAreaView style={styles.container}>
        {/* Header */}
        <View style={[styles.header, { paddingTop: insets.top }]}>
          <View style={styles.profileSection}>
            <Avatar.Icon
              size={60}
              icon="account"
              style={styles.avatar}
            />
            <View style={styles.profileInfo}>
              <Text variant="titleMedium" style={styles.userName}>
                {t('userName') || 'کاربر آزمون'}
              </Text>
              <Text variant="bodySmall" style={styles.userEmail}>
                <EMAIL>
              </Text>
            </View>
          </View>
        </View>

        {/* Menu Items */}
        <ScrollView
          style={styles.menuContainer}
          showsVerticalScrollIndicator={false}
        >
          <Drawer.Section>
            <DrawerItem
              icon="home"
              label={t('home')}
              onPress={() => router.push("/(drawer)/(tabs)")}
            />

            <DrawerItem
              icon="school"
              label={t('majors')}
              onPress={() => router.push("/screens/MajorsList")}
            />

            <DrawerItem
              icon="book"
              label={t('courses')}
              onPress={() => router.push("/screens/CoursesList")}
            />

            <DrawerItem
              icon="help-circle"
              label={t('questions')}
              onPress={() => router.push("/screens/QuestionsList")}
            />

            <DrawerItem
              icon="account-group"
              label={t('users')}
              onPress={() => router.push("/screens/UsersList")}
            />

            <DrawerItem
              icon="clipboard-text"
              label={t('exams')}
              onPress={() => router.push("/screens/ExamsList")}
            />

            <DrawerItem
              icon="calendar"
              label={t('sessions')}
              onPress={() => router.push("/screens/SessionsList")}
            />
          </Drawer.Section>

          <Divider style={styles.divider} />

          <Drawer.Section>
            <DrawerItem
              icon="account"
              label={t('profile') || 'پروفایل'}
              onPress={() => router.push("/screens/Profile")}
            />

            <DrawerItem
              icon="cog"
              label={t('settings')}
              onPress={() => router.push("/screens/Settings")}
            />

            <DrawerItem
              icon="information"
              label={t('about')}
              onPress={() => router.push("/screens/About")}
            />

            <DrawerItem
              icon="logout"
              label={t('logout') || 'خروج از حساب'}
              onPress={handleLogout}
            />
          </Drawer.Section>
        </ScrollView>

        {/* Footer */}
        <View style={styles.footer}>
          <Text variant="bodySmall" style={styles.footerText}>
            نسخه ۰.۰.۱
          </Text>
        </View>
      </SafeAreaView>

      {/* Logout Confirmation Dialog */}
      <PaperConfirmDialog
        visible={showLogoutDialog}
        onDismiss={() => setShowLogoutDialog(false)}
        onConfirm={confirmLogout}
        title={t('logout') || 'خروج از حساب'}
        message={t('logoutConfirm') || 'آیا مطمئن هستید که می‌خواهید از حساب کاربری خود خارج شوید؟'}
        confirmText={t('logout') || 'خروج'}
        cancelText={t('cancel')}
      />
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    // Ensure RTL layout direction
    direction: 'rtl',
  },
  header: {
    paddingHorizontal: 20,
    paddingBottom: 20,
    // RTL-aware padding
    ...rtlStyle.paddingStart(20),
    ...rtlStyle.paddingEnd(20),
  },
  profileSection: {
    flexDirection: rtlStyle.flexDirection.row,
    alignItems: "center",
  },
  avatar: {
    ...rtlStyle.marginEnd(16), // Changed from marginLeft to marginEnd for better RTL support
  },
  profileInfo: {
    flex: 1,
    // Ensure text flows correctly in RTL
    alignItems: 'flex-start',
  },
  userName: {
    fontFamily: "Vazirmatn",
    textAlign: rtlStyle.textAlign.start,
    writingDirection: 'rtl',
  },
  userEmail: {
    fontFamily: "Vazirmatn",
    textAlign: rtlStyle.textAlign.start,
    marginTop: 4,
    writingDirection: 'rtl',
  },
  menuContainer: {
    flex: 1,
    paddingTop: 20,
  },
  drawerItem: {
    marginHorizontal: 12,
    // Ensure proper RTL item alignment
    ...rtlStyle.marginStart(12),
    ...rtlStyle.marginEnd(12),
  },
  drawerItemText: {
    fontFamily: "Vazirmatn",
    textAlign: rtlStyle.textAlign.start,
    writingDirection: 'rtl',
  },
  divider: {
    marginHorizontal: 20,
    marginVertical: 16,
    // RTL-aware margins
    ...rtlStyle.marginStart(20),
    ...rtlStyle.marginEnd(20),
  },
  footer: {
    paddingHorizontal: 20,
    paddingVertical: 16,
    // RTL-aware padding
    ...rtlStyle.paddingStart(20),
    ...rtlStyle.paddingEnd(20),
  },
  footerText: {
    fontFamily: "Vazirmatn",
    textAlign: "center",
    writingDirection: 'rtl',
  },
});
