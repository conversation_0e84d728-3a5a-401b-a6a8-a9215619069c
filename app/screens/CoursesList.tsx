import React, { useEffect, useState } from 'react';
import { StyleSheet, SafeAreaView } from 'react-native';
import { Surface } from 'react-native-paper';
import { Course, courseService } from '@/services/courseService';
import { router } from 'expo-router';
import {
  PaperListAppBar,
  PaperRefreshableFlatList,
  PaperCard,
  PaperCardTitle,
  PaperCardActions,
  PaperButton,
  PaperTitle,
  PaperBody,
  PaperAddFAB,
  PaperDeleteDialog,
  useRefresh,
} from '@/components/paper';
import { t } from '@/constants/Localization';
import { useApiError } from '@/hooks/useApiError';

export default function CoursesListScreen() {
  const [courses, setCourses] = useState<Course[]>([]);
  const [loading, setLoading] = useState(true);
  const [deleteDialogVisible, setDeleteDialogVisible] = useState(false);
  const [courseToDelete, setCourseToDelete] = useState<Course | null>(null);
  const { handleApiError } = useApiError();

  const loadCourses = async () => {
    try {
      setLoading(true);
      const data = await courseService.getCourses();
      setCourses(data);
    } catch (err) {
      handleApiError(err, 'خطا در دریافت دروس');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  const { refreshing, onRefresh } = useRefresh(loadCourses);

  useEffect(() => {
    loadCourses();
  }, []);

  // Navigation handlers
  const handleAddCourse = () => {
    router.push('/screens/CourseAdd');
  };

  const handleCoursePress = (course: Course) => {
    router.push(`/screens/CourseDetails?id=${course.id}`);
  };

  const handleEditCourse = (course: Course) => {
    router.push(`/screens/CourseEdit?id=${course.id}`);
  };

  const handleViewChapters = (course: Course) => {
    router.push(`/screens/ChaptersList?courseId=${course.id}`);
  };

  const handleDeleteCourse = (course: Course) => {
    setCourseToDelete(course);
    setDeleteDialogVisible(true);
  };

  const confirmDeleteCourse = async () => {
    if (!courseToDelete) return;

    try {
      await courseService.deleteCourse(courseToDelete.id);
      setDeleteDialogVisible(false);
      setCourseToDelete(null);
      loadCourses(); // Refresh the list
    } catch (error) {
      handleApiError(error, 'خطا در حذف درس');
      console.error('Failed to delete course:', error);
    }
  };

  const renderCourseItem = ({ item }: { item: Course }) => (
    <PaperCard
      style={styles.courseCard}
      onPress={() => handleCoursePress(item)}
    >
      <PaperCardTitle
        title={item.name}
        subtitle={item.description || t('courseDescription')}
        left={(props) => (
          <Surface {...props} style={styles.courseIcon}>
            <PaperTitle size="small">{item.name.charAt(0)}</PaperTitle>
          </Surface>
        )}
      />
      <PaperCardActions>
        <PaperButton
          title={t('edit')}
          mode="outlined"
          onPress={() => handleEditCourse(item)}
          icon="pencil"
          compact
        />
        <PaperButton
          title={'فصل‌ها'}
          mode="outlined"
          onPress={() => handleViewChapters(item)}
          icon="book-open"
          compact
        />
        <PaperButton
          title={t('delete')}
          mode="outlined"
          onPress={() => handleDeleteCourse(item)}
          icon="delete"
          compact
          style={styles.deleteButton}
        />
      </PaperCardActions>
    </PaperCard>
  );

  return (
    <SafeAreaView style={styles.container}>
      {/* App Bar */}
      <PaperListAppBar
        title={t('courses') || 'دروس'}
        onAddPress={handleAddCourse}
      />

      {/* Content */}
      <Surface style={styles.content}>
        <PaperRefreshableFlatList
          data={courses}
          renderItem={renderCourseItem}
          keyExtractor={(item) => item.id}
          refreshing={refreshing}
          onRefresh={onRefresh}
          loading={loading}
          style={styles.list}
          contentContainerStyle={styles.listContent}
          emptyTitle={'درسی یافت نشد'}
          emptyDescription={'برای افزودن درس جدید از دکمه + استفاده کنید'}
          emptyIcon="book"
          emptyAction={
            <PaperButton
              title={'افزودن درس'}
              mode="contained"
              onPress={handleAddCourse}
              icon="plus"
            />
          }
        />
      </Surface>

      {/* Floating Action Button */}
      <PaperAddFAB
        onPress={handleAddCourse}
        visible={!loading}
      />

      {/* Delete Confirmation Dialog */}
      <PaperDeleteDialog
        visible={deleteDialogVisible}
        onDismiss={() => setDeleteDialogVisible(false)}
        onConfirm={confirmDeleteCourse}
        title={t('delete') || 'حذف درس'}
        content={
          courseToDelete
            ? `آیا از حذف درس "${courseToDelete.name}" مطمئن هستید؟`
            : ''
        }
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
  },
  list: {
    flex: 1,
  },
  listContent: {
    padding: 16,
  },
  courseCard: {
    marginVertical: 8,
  },
  courseIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#e3f2fd',
  },
  deleteButton: {
    borderColor: '#f44336',
  },
});
