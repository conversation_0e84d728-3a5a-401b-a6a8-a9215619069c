import React from 'react';
import { TextInput, useTheme } from 'react-native-paper';
import { StyleSheet, type ViewStyle, type TextStyle } from 'react-native';

export interface PaperTextInputProps {
  label?: string;
  placeholder?: string;
  value: string;
  onChangeText: (text: string) => void;
  style?: ViewStyle;
  textStyle?: TextStyle;
  mode?: 'flat' | 'outlined';
  disabled?: boolean;
  error?: boolean;
  errorText?: string;
  multiline?: boolean;
  numberOfLines?: number;
  secureTextEntry?: boolean;
  keyboardType?: 'default' | 'email-address' | 'numeric' | 'phone-pad' | 'number-pad';
  autoCapitalize?: 'none' | 'sentences' | 'words' | 'characters';
  autoCorrect?: boolean;
  left?: React.ReactNode;
  right?: React.ReactNode;
  dense?: boolean;
  onFocus?: () => void;
  onBlur?: () => void;
  maxLength?: number;
}

export function PaperTextInputComponent({
  label,
  placeholder,
  value,
  onChangeText,
  style,
  textStyle,
  mode = 'outlined',
  disabled = false,
  error = false,
  errorText,
  multiline = false,
  numberOfLines,
  secureTextEntry = false,
  keyboardType = 'default',
  autoCapitalize = 'sentences',
  autoCorrect = true,
  left,
  right,
  dense = false,
  onFocus,
  onBlur,
  maxLength,
}: PaperTextInputProps) {
  const theme = useTheme();

  return (
    <>
      <TextInput
        label={label}
        placeholder={placeholder}
        value={value}
        onChangeText={onChangeText}
        style={[styles.textInput, style]}
        contentStyle={[styles.textContent, textStyle]}
        mode={mode}
        disabled={disabled}
        error={error}
        multiline={multiline}
        numberOfLines={numberOfLines}
        secureTextEntry={secureTextEntry}
        keyboardType={keyboardType}
        autoCapitalize={autoCapitalize}
        autoCorrect={autoCorrect}
        left={left}
        right={right}
        dense={dense}
        onFocus={onFocus}
        onBlur={onBlur}
        maxLength={maxLength}
      />
      {error && errorText && (
        <PaperHelperText type="error" visible={error}>
          {errorText}
        </PaperHelperText>
      )}
    </>
  );
}

// Helper Text component
export function PaperHelperText({
  children,
  type = 'info',
  visible = true,
  style,
}: {
  children: React.ReactNode;
  type?: 'error' | 'info';
  visible?: boolean;
  style?: TextStyle;
}) {
  return (
    <TextInput.Affix
      text=""
      textStyle={[
        styles.helperText,
        type === 'error' && styles.errorText,
        style,
      ]}
    >
      {visible ? children : null}
    </TextInput.Affix>
  );
}

// Search Input component
export function PaperSearchInput({
  placeholder = 'جستجو...',
  value,
  onChangeText,
  onSubmitEditing,
  style,
}: {
  placeholder?: string;
  value: string;
  onChangeText: (text: string) => void;
  onSubmitEditing?: () => void;
  style?: ViewStyle;
}) {
  return (
    <PaperTextInputComponent
      placeholder={placeholder}
      value={value}
      onChangeText={onChangeText}
      style={[styles.searchInput, style]}
      mode="outlined"
      left={<TextInput.Icon icon="magnify" />}
      right={
        value ? (
          <TextInput.Icon 
            icon="close" 
            onPress={() => onChangeText('')} 
          />
        ) : undefined
      }
      onSubmitEditing={onSubmitEditing}
    />
  );
}

const styles = StyleSheet.create({
  textInput: {
    marginVertical: 8,
  },
  textContent: {
    fontFamily: 'Vazirmatn',
    textAlign: 'right',
    writingDirection: 'rtl',
  },
  searchInput: {
    marginVertical: 8,
    marginHorizontal: 16,
  },
  helperText: {
    fontFamily: 'Vazirmatn',
    fontSize: 12,
    textAlign: 'right',
    marginTop: 4,
  },
  errorText: {
    color: '#B00020',
  },
});

// Export as default for easier importing
export default PaperTextInputComponent;
