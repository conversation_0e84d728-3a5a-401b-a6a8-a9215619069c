import { api } from './api';

// Define types
export interface MajorCourse {
  majorId: string;
  courseId: string;
  createdAt?: string;
}

export interface CreateMajorCourseDto {
  majorId: string;
  courseId: string;
}

export interface UpdateMajorCourseDto {
  majorId?: string;
  courseId?: string;
}

// MajorCourse service for handling major-course-related API calls
export const majorCourseService = {
  // Get all major-courses
  async getMajorCourses(): Promise<MajorCourse[]> {
    const response = await api.get<MajorCourse[]>('/v1/api/major-courses');
    return response.data;
  },

  // Get a specific major-course by majorId and courseId
  async getMajorCourse(majorId: string, courseId: string): Promise<MajorCourse> {
    const response = await api.get<MajorCourse>(`/v1/api/major-courses/${majorId}/${courseId}`);
    return response.data;
  },

  // Create a new major-course
  async createMajorCourse(majorCourseData: CreateMajorCourseDto): Promise<MajorCourse> {
    const response = await api.post<MajorCourse>('/v1/api/major-courses', majorCourseData);
    return response.data;
  },

  // Update a major-course
  async updateMajorCourse(majorId: string, courseId: string, majorCourseData: UpdateMajorCourseDto): Promise<MajorCourse> {
    const response = await api.patch<MajorCourse>(`/v1/api/major-courses/${majorId}/${courseId}`, majorCourseData);
    return response.data;
  },

  // Delete a major-course
  async deleteMajorCourse(majorId: string, courseId: string): Promise<void> {
    await api.delete(`/v1/api/major-courses/${majorId}/${courseId}`);
  }
};
