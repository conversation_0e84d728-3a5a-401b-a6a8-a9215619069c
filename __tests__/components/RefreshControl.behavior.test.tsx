import React from 'react';
import { render, fireEvent, waitFor, act } from '@testing-library/react-native';
import ChaptersListScreen from '../../app/screens/ChaptersList';
import QuestionsListScreen from '../../app/screens/QuestionsList';
import { chapterService } from '../../services/chapterService';
import { questionService } from '../../services/questionService';

// Mock services
jest.mock('../../services/chapterService', () => ({
  chapterService: {
    getChapters: jest.fn(),
    deleteChapter: jest.fn(),
  },
}));

jest.mock('../../services/questionService', () => ({
  questionService: {
    getQuestions: jest.fn(),
  },
}));

// Mock useLocalSearchParams for ChaptersList
jest.mock('expo-router', () => ({
  ...jest.requireActual('expo-router'),
  useLocalSearchParams: () => ({ courseId: 'test-course-id' }),
}));

describe('RefreshControl Behavior Tests', () => {
  describe('Refresh State Management', () => {
    beforeEach(() => {
      jest.clearAllMocks();
    });

    it('should show refreshing state during pull-to-refresh', async () => {
      const mockChapters = [
        { id: '1', name: 'Chapter 1', courseId: 'test-course-id' },
      ];

      // Mock a delayed response to test refreshing state
      (chapterService.getChapters as jest.Mock).mockImplementation(() => 
        new Promise(resolve => setTimeout(() => resolve(mockChapters), 100))
      );

      const { getByTestId } = render(<ChaptersListScreen />);
      
      // Wait for initial load
      await waitFor(() => {
        expect(chapterService.getChapters).toHaveBeenCalledTimes(1);
      });

      const flatList = getByTestId('chapters-flatlist');
      
      // Trigger refresh
      act(() => {
        fireEvent(flatList, 'refresh');
      });

      // Check that refreshing state is true
      expect(flatList.props.refreshControl.props.refreshing).toBe(true);

      // Wait for refresh to complete
      await waitFor(() => {
        expect(chapterService.getChapters).toHaveBeenCalledTimes(2);
      });

      // Check that refreshing state is false after completion
      await waitFor(() => {
        expect(flatList.props.refreshControl.props.refreshing).toBe(false);
      });
    });

    it('should handle rapid consecutive refresh attempts', async () => {
      const mockQuestions = [
        { id: '1', text: 'Question 1', chapterId: 'chapter-1' },
      ];

      (questionService.getQuestions as jest.Mock).mockResolvedValue(mockQuestions);

      const { getByTestId } = render(<QuestionsListScreen />);
      
      // Wait for initial load
      await waitFor(() => {
        expect(questionService.getQuestions).toHaveBeenCalledTimes(1);
      });

      const flatList = getByTestId('questions-flatlist');
      
      // Trigger multiple rapid refreshes
      act(() => {
        fireEvent(flatList, 'refresh');
        fireEvent(flatList, 'refresh');
        fireEvent(flatList, 'refresh');
      });

      // Should only trigger one additional call due to refreshing state protection
      await waitFor(() => {
        expect(questionService.getQuestions).toHaveBeenCalledTimes(2);
      });
    });

    it('should reset refreshing state even when refresh fails', async () => {
      (chapterService.getChapters as jest.Mock)
        .mockResolvedValueOnce([]) // Initial load succeeds
        .mockRejectedValueOnce(new Error('Refresh failed')); // Refresh fails

      const { getByTestId } = render(<ChaptersListScreen />);
      
      // Wait for initial load
      await waitFor(() => {
        expect(chapterService.getChapters).toHaveBeenCalledTimes(1);
      });

      const flatList = getByTestId('chapters-flatlist');
      
      // Trigger refresh that will fail
      act(() => {
        fireEvent(flatList, 'refresh');
      });

      // Wait for refresh to complete (with error)
      await waitFor(() => {
        expect(chapterService.getChapters).toHaveBeenCalledTimes(2);
      });

      // Check that refreshing state is false even after error
      await waitFor(() => {
        expect(flatList.props.refreshControl.props.refreshing).toBe(false);
      });
    });
  });

  describe('Error Handling During Refresh', () => {
    beforeEach(() => {
      jest.clearAllMocks();
    });

    it('should display different error messages for initial load vs refresh', async () => {
      // Test initial load error
      (questionService.getQuestions as jest.Mock).mockRejectedValue(new Error('Initial load failed'));

      const { getByText, rerender } = render(<QuestionsListScreen />);
      
      await waitFor(() => {
        expect(getByText('Failed to fetch questions.')).toBeTruthy();
      });

      // Reset and test refresh error
      jest.clearAllMocks();
      (questionService.getQuestions as jest.Mock)
        .mockResolvedValueOnce([]) // Initial load succeeds
        .mockRejectedValueOnce(new Error('Refresh failed')); // Refresh fails

      rerender(<QuestionsListScreen />);

      await waitFor(() => {
        expect(questionService.getQuestions).toHaveBeenCalledTimes(1);
      });

      const { getByTestId } = render(<QuestionsListScreen />);
      const flatList = getByTestId('questions-flatlist');
      
      act(() => {
        fireEvent(flatList, 'refresh');
      });

      await waitFor(() => {
        expect(getByText('Failed to refresh questions.')).toBeTruthy();
      });
    });

    it('should maintain data when refresh fails', async () => {
      const initialData = [
        { id: '1', name: 'Chapter 1', courseId: 'test-course-id' },
      ];

      (chapterService.getChapters as jest.Mock)
        .mockResolvedValueOnce(initialData) // Initial load succeeds
        .mockRejectedValueOnce(new Error('Refresh failed')); // Refresh fails

      const { getByText, getByTestId } = render(<ChaptersListScreen />);
      
      // Wait for initial load and verify data is displayed
      await waitFor(() => {
        expect(getByText('Chapter 1')).toBeTruthy();
      });

      const flatList = getByTestId('chapters-flatlist');
      
      // Trigger refresh that will fail
      act(() => {
        fireEvent(flatList, 'refresh');
      });

      // Wait for refresh to complete with error
      await waitFor(() => {
        expect(chapterService.getChapters).toHaveBeenCalledTimes(2);
      });

      // Verify original data is still displayed
      expect(getByText('Chapter 1')).toBeTruthy();
    });
  });

  describe('RefreshControl Props Consistency', () => {
    it('should have consistent tintColor across all screens', async () => {
      const expectedColor = '#007bff';
      
      const { getByTestId: getChaptersTestId } = render(<ChaptersListScreen />);
      const { getByTestId: getQuestionsTestId } = render(<QuestionsListScreen />);
      
      await waitFor(() => {
        const chaptersFlatList = getChaptersTestId('chapters-flatlist');
        const questionsFlatList = getQuestionsTestId('questions-flatlist');
        
        expect(chaptersFlatList.props.refreshControl.props.tintColor).toBe(expectedColor);
        expect(questionsFlatList.props.refreshControl.props.tintColor).toBe(expectedColor);
      });
    });
  });
});
