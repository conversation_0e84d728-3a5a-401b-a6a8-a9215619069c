import React from 'react';
import { Text, useTheme } from 'react-native-paper';
import { StyleSheet, type TextStyle } from 'react-native';

export interface PaperTextProps {
  children: React.ReactNode;
  variant?: 'displayLarge' | 'displayMedium' | 'displaySmall' | 
           'headlineLarge' | 'headlineMedium' | 'headlineSmall' |
           'titleLarge' | 'titleMedium' | 'titleSmall' |
           'labelLarge' | 'labelMedium' | 'labelSmall' |
           'bodyLarge' | 'bodyMedium' | 'bodySmall';
  style?: TextStyle;
  numberOfLines?: number;
  ellipsizeMode?: 'head' | 'middle' | 'tail' | 'clip';
  selectable?: boolean;
}

export function PaperTextComponent({
  children,
  variant = 'bodyMedium',
  style,
  numberOfLines,
  ellipsizeMode,
  selectable = false,
}: PaperTextProps) {
  const theme = useTheme();

  return (
    <Text
      variant={variant}
      style={[styles.text, style]}
      numberOfLines={numberOfLines}
      ellipsizeMode={ellipsizeMode}
      selectable={selectable}
    >
      {children}
    </Text>
  );
}

// Specialized text components
export function PaperTitle({ 
  children, 
  style,
  size = 'medium'
}: { 
  children: React.ReactNode; 
  style?: TextStyle;
  size?: 'small' | 'medium' | 'large';
}) {
  const variant = size === 'large' ? 'titleLarge' : 
                  size === 'small' ? 'titleSmall' : 'titleMedium';
  
  return (
    <PaperTextComponent 
      variant={variant} 
      style={[styles.title, style]}
    >
      {children}
    </PaperTextComponent>
  );
}

export function PaperHeadline({ 
  children, 
  style,
  size = 'medium'
}: { 
  children: React.ReactNode; 
  style?: TextStyle;
  size?: 'small' | 'medium' | 'large';
}) {
  const variant = size === 'large' ? 'headlineLarge' : 
                  size === 'small' ? 'headlineSmall' : 'headlineMedium';
  
  return (
    <PaperTextComponent 
      variant={variant} 
      style={[styles.headline, style]}
    >
      {children}
    </PaperTextComponent>
  );
}

export function PaperBody({ 
  children, 
  style,
  size = 'medium'
}: { 
  children: React.ReactNode; 
  style?: TextStyle;
  size?: 'small' | 'medium' | 'large';
}) {
  const variant = size === 'large' ? 'bodyLarge' : 
                  size === 'small' ? 'bodySmall' : 'bodyMedium';
  
  return (
    <PaperTextComponent 
      variant={variant} 
      style={[styles.body, style]}
    >
      {children}
    </PaperTextComponent>
  );
}

export function PaperLabel({ 
  children, 
  style,
  size = 'medium'
}: { 
  children: React.ReactNode; 
  style?: TextStyle;
  size?: 'small' | 'medium' | 'large';
}) {
  const variant = size === 'large' ? 'labelLarge' : 
                  size === 'small' ? 'labelSmall' : 'labelMedium';
  
  return (
    <PaperTextComponent 
      variant={variant} 
      style={[styles.label, style]}
    >
      {children}
    </PaperTextComponent>
  );
}

const styles = StyleSheet.create({
  text: {
    fontFamily: 'Vazirmatn',
    textAlign: 'right',
    writingDirection: 'rtl',
  },
  title: {
    fontWeight: '600',
  },
  headline: {
    fontWeight: '700',
  },
  body: {
    fontWeight: '400',
  },
  label: {
    fontWeight: '500',
  },
});

// Export as default for easier importing
export default PaperTextComponent;
