import { StyleSheet } from "react-native";
import { IconButton, useTheme } from "react-native-paper";
import { useNavigation } from "expo-router";

export function MenuButton() {
  const theme = useTheme();
  const navigation = useNavigation();

  const openDrawer = () => {
    navigation.openDrawer();
  };

  return (
    <IconButton
      icon="menu"
      size={24}
      iconColor={theme.colors.onSurface}
      style={styles.headerButton}
      onPress={openDrawer}
      accessibilityLabel="باز کردن منو"
    />
  );
}

const styles = StyleSheet.create({
  text: {
    fontSize: 28,
    lineHeight: 32,
    marginTop: -6,
  },
  headerButton: {
    padding: 8,
  },
});
