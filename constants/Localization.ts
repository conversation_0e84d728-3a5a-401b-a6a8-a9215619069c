// Persian/Farsi localization strings
export const fa = {
  // Common
  save: 'ذخیره',
  cancel: 'لغو',
  delete: 'حذف',
  edit: 'ویرایش',
  add: 'افزودن',
  search: 'جستجو',
  loading: 'در حال بارگذاری...',
  error: 'خطا',
  success: 'موفقیت',
  confirm: 'تأیید',
  yes: 'بله',
  no: 'خیر',
  ok: 'تأیید',
  back: 'بازگشت',
  next: 'بعدی',
  previous: 'قبلی',
  close: 'بستن',
  refresh: 'تازه‌سازی',
  
  // Navigation
  home: 'خانه',
  majors: 'رشته‌ها',
  courses: 'دروس',
  chapters: 'فصل‌ها',
  questions: 'سؤالات',
  options: 'گزینه‌ها',
  users: 'کاربران',
  sessions: 'جلسات',
  exams: 'آزمون‌ها',
  results: 'نتایج',
  settings: 'تنظیمات',
  about: 'درباره',
  profile: 'پروفایل',
  logout: 'خروج از حساب',
  logoutConfirm: 'آیا مطمئن هستید که می‌خواهید از حساب کاربری خود خارج شوید؟',
  userName: 'کاربر آزمون',
  
  // Majors
  majorsList: 'فهرست رشته‌ها',
  majorDetails: 'جزئیات رشته',
  addMajor: 'افزودن رشته',
  editMajor: 'ویرایش رشته',
  majorName: 'نام رشته',
  majorDescription: 'توضیحات رشته',
  majorCode: 'کد رشته',
  
  // Courses
  coursesList: 'فهرست دروس',
  courseDetails: 'جزئیات درس',
  addCourse: 'افزودن درس',
  editCourse: 'ویرایش درس',
  courseName: 'نام درس',
  courseDescription: 'توضیحات درس',
  courseCode: 'کد درس',
  courseCredits: 'واحد درس',
  
  // Chapters
  chaptersList: 'فهرست فصل‌ها',
  chapterDetails: 'جزئیات فصل',
  addChapter: 'افزودن فصل',
  editChapter: 'ویرایش فصل',
  chapterTitle: 'عنوان فصل',
  chapterDescription: 'توضیحات فصل',
  chapterNumber: 'شماره فصل',
  
  // Questions
  questionsList: 'فهرست سؤالات',
  questionDetails: 'جزئیات سؤال',
  addQuestion: 'افزودن سؤال',
  editQuestion: 'ویرایش سؤال',
  questionText: 'متن سؤال',
  questionType: 'نوع سؤال',
  questionDifficulty: 'سطح دشواری',
  multipleChoice: 'چندگزینه‌ای',
  trueFalse: 'درست/غلط',
  shortAnswer: 'پاسخ کوتاه',
  essay: 'تشریحی',
  
  // Options
  optionsList: 'فهرست گزینه‌ها',
  optionDetails: 'جزئیات گزینه',
  addOption: 'افزودن گزینه',
  editOption: 'ویرایش گزینه',
  optionText: 'متن گزینه',
  isCorrect: 'پاسخ صحیح',
  optionOrder: 'ترتیب گزینه',
  
  // Users
  usersList: 'فهرست کاربران',
  userDetails: 'جزئیات کاربر',
  addUser: 'افزودن کاربر',
  editUser: 'ویرایش کاربر',
  username: 'نام کاربری',
  email: 'ایمیل',
  firstName: 'نام',
  lastName: 'نام خانوادگی',
  password: 'رمز عبور',
  confirmPassword: 'تأیید رمز عبور',
  role: 'نقش',
  admin: 'مدیر',
  teacher: 'مدرس',
  student: 'دانشجو',
  
  // Sessions
  sessionsList: 'فهرست جلسات',
  sessionDetails: 'جزئیات جلسه',
  addSession: 'افزودن جلسه',
  editSession: 'ویرایش جلسه',
  sessionTitle: 'عنوان جلسه',
  sessionDate: 'تاریخ جلسه',
  sessionTime: 'زمان جلسه',
  sessionDuration: 'مدت زمان جلسه',
  
  // Exams
  examsList: 'فهرست آزمون‌ها',
  examDetails: 'جزئیات آزمون',
  addExam: 'افزودن آزمون',
  editExam: 'ویرایش آزمون',
  examTitle: 'عنوان آزمون',
  examDescription: 'توضیحات آزمون',
  examDate: 'تاریخ آزمون',
  examDuration: 'مدت زمان آزمون',
  totalQuestions: 'تعداد کل سؤالات',
  totalMarks: 'نمره کل',
  
  // Results
  resultsList: 'فهرست نتایج',
  resultDetails: 'جزئیات نتیجه',
  score: 'نمره',
  percentage: 'درصد',
  correctAnswers: 'پاسخ‌های صحیح',
  wrongAnswers: 'پاسخ‌های غلط',
  unanswered: 'بدون پاسخ',
  timeTaken: 'زمان صرف شده',
  
  // Settings
  darkMode: 'حالت تاریک',
  language: 'زبان',
  notifications: 'اعلان‌ها',
  privacy: 'حریم خصوصی',
  
  // Messages
  saveSuccess: 'با موفقیت ذخیره شد',
  deleteSuccess: 'با موفقیت حذف شد',
  updateSuccess: 'با موفقیت به‌روزرسانی شد',
  deleteConfirm: 'آیا مطمئن هستید که می‌خواهید این مورد را حذف کنید؟',
  networkError: 'خطا در اتصال به شبکه',
  serverError: 'خطا در سرور',
  validationError: 'خطا در اعتبارسنجی',

  // Error handling and retry
  connectionError: 'خطا در اتصال به اینترنت',
  timeoutError: 'زمان اتصال به پایان رسید',
  unknownError: 'خطای نامشخص رخ داده است',
  retry: 'تلاش مجدد',
  retrying: 'در حال تلاش مجدد...',
  retryFailed: 'تلاش مجدد ناموفق بود',
  maxRetriesReached: 'حداکثر تعداد تلاش انجام شد',
  checkConnection: 'لطفاً اتصال اینترنت خود را بررسی کنید',
  tryAgainLater: 'لطفاً بعداً دوباره تلاش کنید',

  // Toast messages
  operationSuccessful: 'عملیات با موفقیت انجام شد',
  operationFailed: 'عملیات ناموفق بود',
  dataLoadFailed: 'بارگذاری اطلاعات ناموفق بود',
  dataSaveFailed: 'ذخیره اطلاعات ناموفق بود',
  dataDeleteFailed: 'حذف اطلاعات ناموفق بود',
  dataUpdateFailed: 'به‌روزرسانی اطلاعات ناموفق بود',

  // Error types
  badRequest: 'درخواست نامعتبر',
  unauthorized: 'عدم دسترسی - لطفاً وارد شوید',
  forbidden: 'دسترسی ممنوع',
  notFound: 'اطلاعات یافت نشد',
  conflict: 'تداخل در اطلاعات',
  internalServerError: 'خطای داخلی سرور',
  serviceUnavailable: 'سرویس در دسترس نیست',
  
  // Form validation
  required: 'این فیلد الزامی است',
  invalidEmail: 'ایمیل نامعتبر است',
  passwordMismatch: 'رمزهای عبور مطابقت ندارند',
  minLength: 'حداقل {0} کاراکتر وارد کنید',
  maxLength: 'حداکثر {0} کاراکتر مجاز است',
  
  // Pull to refresh
  pullToRefresh: 'برای تازه‌سازی بکشید',
  releaseToRefresh: 'برای تازه‌سازی رها کنید',
  refreshing: 'در حال تازه‌سازی...',
};

// English localization (fallback)
export const en = {
  // Common
  save: 'Save',
  cancel: 'Cancel',
  delete: 'Delete',
  edit: 'Edit',
  add: 'Add',
  search: 'Search',
  loading: 'Loading...',
  error: 'Error',
  success: 'Success',
  confirm: 'Confirm',
  yes: 'Yes',
  no: 'No',
  ok: 'OK',
  back: 'Back',
  next: 'Next',
  previous: 'Previous',
  close: 'Close',
  refresh: 'Refresh',
  
  // Navigation
  home: 'Home',
  majors: 'Majors',
  courses: 'Courses',
  chapters: 'Chapters',
  questions: 'Questions',
  options: 'Options',
  users: 'Users',
  sessions: 'Sessions',
  exams: 'Exams',
  results: 'Results',
  settings: 'Settings',
  about: 'About',
  
  // Pull to refresh
  pullToRefresh: 'Pull to refresh',
  releaseToRefresh: 'Release to refresh',
  refreshing: 'Refreshing...',

  // Error handling and retry (English)
  connectionError: 'Internet connection error',
  timeoutError: 'Connection timeout',
  unknownError: 'An unknown error occurred',
  retry: 'Retry',
  retrying: 'Retrying...',
  retryFailed: 'Retry failed',
  maxRetriesReached: 'Maximum retry attempts reached',
  checkConnection: 'Please check your internet connection',
  tryAgainLater: 'Please try again later',

  // Toast messages (English)
  operationSuccessful: 'Operation successful',
  operationFailed: 'Operation failed',
  dataLoadFailed: 'Failed to load data',
  dataSaveFailed: 'Failed to save data',
  dataDeleteFailed: 'Failed to delete data',
  dataUpdateFailed: 'Failed to update data',

  // Error types (English)
  badRequest: 'Bad request',
  unauthorized: 'Unauthorized - please login',
  forbidden: 'Access forbidden',
  notFound: 'Data not found',
  conflict: 'Data conflict',
  internalServerError: 'Internal server error',
  serviceUnavailable: 'Service unavailable',
};

// Current language (default to Persian)
export const currentLanguage = 'fa';

// Get localized string
export const t = (key: keyof typeof fa): string => {
  const strings = currentLanguage === 'fa' ? fa : en;
  return strings[key] || en[key] || key;
};

export type LocalizationKey = keyof typeof fa;
