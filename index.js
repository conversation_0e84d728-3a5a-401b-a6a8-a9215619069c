/**
 * Entry point for the Azmoon Client app
 * This file ensures <PERSON><PERSON> is properly configured before the app starts
 */

import { I18nManager } from 'react-native';

// Force RTL layout globally - must be called before any components render
console.log('Initializing RTL configuration...');
I18nManager.allowRTL(true);
I18nManager.forceRTL(true);

// Log RTL status for debugging
console.log('RTL Status:', I18nManager.isRTL);
console.log('RTL Allowed:', I18nManager.allowRTL);

// Import and start the Expo app
import 'expo-router/entry';
